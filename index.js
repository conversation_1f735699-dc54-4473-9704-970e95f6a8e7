/**
 * @format
 */
import "react-native-gesture-handler";
import 'react-native-url-polyfill/auto';
import {AppRegistry, Text, Appearance, TextInput, Platform} from 'react-native';
import { GET_STREAM_CHAT_TOKEN } from "./src/service/EndPoint";
import messaging from '@react-native-firebase/messaging';
import PushNotification from 'react-native-push-notification';
import {StreamChat} from 'stream-chat';
import auth from '@react-native-firebase/auth';
import NewRelic from 'newrelic-react-native-agent';
import * as appVersion from './package.json';

import App from './App';
import {name as appName} from './app.json';
import Config from 'react-native-config';

import { ADMIN_USER_ID } from './src/utils/constants/strings';

let appToken;

if (Platform.OS === 'ios') {
    appToken = Config.IOS_NEW_RELIC_APP_TOKEN;
} else {
    appToken = Config.ANDROID_NEW_RELIC_APP_TOKEN;
}

const agentConfiguration = {
    //Android Specific
    // Optional:Enable or disable collection of event data.
    analyticsEventEnabled: true,
  
    // Optional:Enable or disable crash reporting.
    crashReportingEnabled: true,
  
    // Optional:Enable or disable interaction tracing. Trace instrumentation still occurs, but no traces are harvested. This will disable default and custom interactions.
    interactionTracingEnabled: true,
  
    // Optional:Enable or disable reporting successful HTTP requests to the MobileRequest event type.
    networkRequestEnabled: true,
  
    // Optional:Enable or disable reporting network and HTTP request errors to the MobileRequestError event type.
    networkErrorRequestEnabled: true,
  
    // Optional:Enable or disable capture of HTTP response bodies for HTTP error traces, and MobileRequestError events.
    httpResponseBodyCaptureEnabled: true,
  
    // Optional:Enable or disable agent logging.
    loggingEnabled: true,
  
    // Optional:Specifies the log level. Omit this field for the default log level.
    // Options include: ERROR (least verbose), WARNING, INFO, VERBOSE, AUDIT (most verbose).
    logLevel: NewRelic.LogLevel.VERBOSE,
  
    // iOS Specific
    // Optional:Enable/Disable automatic instrumentation of WebViews
    webViewInstrumentation: true,
  
    // Optional:Set a specific collector address for sending data. Omit this field for default address.
    // collectorAddress: "",

    // Additional configuration for better error capture
    distributedTracingEnabled: true,

    // Enable offline storage for when network is unavailable
    offlineStorageEnabled: true,

    // Enable background reporting
    backgroundReportingEnabled: true,

    // Enable new event system
    newEventSystemEnabled: true,
  
    // Optional:Set a specific crash collector address for sending crashes. Omit this field for default address.
    // crashCollectorAddress: ""
  };


Appearance.getColorScheme = () => 'light';

Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.allowFontScaling = false;
TextInput.defaultProps = TextInput.defaultProps || {};
TextInput.defaultProps.allowFontScaling = false;

const getStreamToken = async (token, user) => {
    const streamToken = await fetch(GET_STREAM_CHAT_TOKEN, {
        method: 'POST',
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            userId: user?.claims?.user_id,
        }),
    })
        .then(async (data) => data.json())
        .then((res) => {
            return res.streamToken;
        })
        .catch((err) => {
            console.log('Error----->', err);
        });
    return streamToken;
};

messaging().setBackgroundMessageHandler(async (message) => {
    const token = await auth()?.currentUser?.getIdToken();
    const user = await auth()?.currentUser?.getIdTokenResult();
    if (user && token) {
        const client = new StreamChat(Config.NEXT_PUBLIC_STREAM_KEY);
        await client.connectUser(
            {
                id: user?.claims?.user_id,
            },
            async () => getStreamToken(token, user),
        );
        const messageData = await client.getMessage(message.data.id);
        const senderName = messageData?.message?.user?.id === ADMIN_USER_ID
            ? messageData?.message?.user?.name
            : messageData?.message?.user?.username

        PushNotification.localNotification({
            channelId: 'thousand-greens',
            invokeApp: true,
            alertAction: 'view',
            category: '',
            title: 'New message from ' + senderName,
            message: messageData.message.text,
            userInfo: message.data ? message.data : {},
            playSound: false,
            soundName: 'default',
        });
    }
});
console.log("appToken",appToken, appVersion);

NewRelic.startAgent(appToken,agentConfiguration);
NewRelic.setJSAppVersion(appVersion.version);
AppRegistry.registerComponent(appName, () => App);
