// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* thousandgreensTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* thousandgreensTests.m */; };
		11C4379CA1654C7EBA8DB687 /* Ubuntu-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BD4DB3E8F4DB42379949139B /* Ubuntu-BoldItalic.ttf */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		201A40C9DF2843E69600F5DC /* RobotoSlab-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4FA8E961C20D4A10AA872577 /* RobotoSlab-Medium.ttf */; };
		2531627E095B4571B1153D2D /* Ubuntu-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 847C4B2E380C46A79E1D5076 /* Ubuntu-MediumItalic.ttf */; };
		33070840E37F4648BCAB5F34 /* Ubuntu-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D7CE8F732468427DA48144CE /* Ubuntu-Bold.ttf */; };
		36297CFD53014CB696138E55 /* Ubuntu-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = FD85814FF762475AB404C962 /* Ubuntu-Light.ttf */; };
		40A79776CD472826AAF5EF62 /* Pods_thousandgreens_thousandgreens_qa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F7DD6E57746A13BC06F63558 /* Pods_thousandgreens_thousandgreens_qa.framework */; };
		4B2077275A514D56822DAE9D /* RobotoSlab-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C8E556B41B584D4A8AB00E0B /* RobotoSlab-Bold.ttf */; };
		650994EF02192B0F6B4658BB /* Pods_thousandgreens_thousandgreens_dev.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8F95E1174533835E409F54A7 /* Pods_thousandgreens_thousandgreens_dev.framework */; };
		71CFF469B0EC439BAF0E2B73 /* Ubuntu-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C0976C326A7F42D29B21D4C8 /* Ubuntu-LightItalic.ttf */; };
		76EEA6D98A10425E9226E658 /* RobotoSlab-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 35A2F5FFF24847A592877C0F /* RobotoSlab-Black.ttf */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		83E1DD8DDF4033511757E14C /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */; };
		872E5AB0B71E4375AC415ED3 /* Ubuntu-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BEF7F60138E54349961EF8D4 /* Ubuntu-Medium.ttf */; };
		90887EFE8D024BC88F53BF6B /* Ubuntu-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AF66074E67BB4746A54E0423 /* Ubuntu-Italic.ttf */; };
		AB8CAAC070A54D3D8F62BE2B /* RobotoSlab-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 78F89C14143E4C25B73BB673 /* RobotoSlab-Regular.ttf */; };
		B674A409604246B0A937EB38 /* RobotoSlab-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0DEE490D95BD468A80E8EE45 /* RobotoSlab-ExtraBold.ttf */; };
		C93D7F0A2D59FF5800543A51 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = C93D7F092D59FF5800543A51 /* GoogleService-Info.plist */; };
		C9BD17CA2D3F731D00B654B3 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		C9BD17CB2D3F731D00B654B3 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		C9BD17CF2D3F731D00B654B3 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		C9BD17D12D3F731D00B654B3 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		C9BD17D22D3F731D00B654B3 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */; };
		C9BD17D32D3F731D00B654B3 /* RobotoSlab-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 35A2F5FFF24847A592877C0F /* RobotoSlab-Black.ttf */; };
		C9BD17D42D3F731D00B654B3 /* RobotoSlab-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C8E556B41B584D4A8AB00E0B /* RobotoSlab-Bold.ttf */; };
		C9BD17D52D3F731D00B654B3 /* RobotoSlab-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0DEE490D95BD468A80E8EE45 /* RobotoSlab-ExtraBold.ttf */; };
		C9BD17D62D3F731D00B654B3 /* RobotoSlab-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8F71A24AD0C8471F96059CFD /* RobotoSlab-ExtraLight.ttf */; };
		C9BD17D72D3F731D00B654B3 /* RobotoSlab-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E5697234FBE340C996FC3872 /* RobotoSlab-Light.ttf */; };
		C9BD17D82D3F731D00B654B3 /* RobotoSlab-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4FA8E961C20D4A10AA872577 /* RobotoSlab-Medium.ttf */; };
		C9BD17D92D3F731D00B654B3 /* RobotoSlab-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 78F89C14143E4C25B73BB673 /* RobotoSlab-Regular.ttf */; };
		C9BD17DA2D3F731D00B654B3 /* RobotoSlab-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 3E8E337FB7B0463CA92AE8A9 /* RobotoSlab-SemiBold.ttf */; };
		C9BD17DB2D3F731D00B654B3 /* RobotoSlab-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9F8D91B51D304FD8898D7772 /* RobotoSlab-Thin.ttf */; };
		C9BD17DC2D3F731D00B654B3 /* Ubuntu-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D7CE8F732468427DA48144CE /* Ubuntu-Bold.ttf */; };
		C9BD17DD2D3F731D00B654B3 /* Ubuntu-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BD4DB3E8F4DB42379949139B /* Ubuntu-BoldItalic.ttf */; };
		C9BD17DE2D3F731D00B654B3 /* Ubuntu-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AF66074E67BB4746A54E0423 /* Ubuntu-Italic.ttf */; };
		C9BD17DF2D3F731D00B654B3 /* Ubuntu-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = FD85814FF762475AB404C962 /* Ubuntu-Light.ttf */; };
		C9BD17E02D3F731D00B654B3 /* Ubuntu-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C0976C326A7F42D29B21D4C8 /* Ubuntu-LightItalic.ttf */; };
		C9BD17E12D3F731D00B654B3 /* Ubuntu-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BEF7F60138E54349961EF8D4 /* Ubuntu-Medium.ttf */; };
		C9BD17E22D3F731D00B654B3 /* Ubuntu-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 847C4B2E380C46A79E1D5076 /* Ubuntu-MediumItalic.ttf */; };
		C9BD17E32D3F731D00B654B3 /* Ubuntu-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E519FF95F85843D0A8E33CDF /* Ubuntu-Regular.ttf */; };
		C9BD17F12D3F735C00B654B3 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		C9BD17F22D3F735C00B654B3 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		C9BD17F62D3F735C00B654B3 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		C9BD17F82D3F735C00B654B3 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		C9BD17F92D3F735C00B654B3 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */; };
		C9BD17FA2D3F735C00B654B3 /* RobotoSlab-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 35A2F5FFF24847A592877C0F /* RobotoSlab-Black.ttf */; };
		C9BD17FB2D3F735C00B654B3 /* RobotoSlab-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C8E556B41B584D4A8AB00E0B /* RobotoSlab-Bold.ttf */; };
		C9BD17FC2D3F735C00B654B3 /* RobotoSlab-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0DEE490D95BD468A80E8EE45 /* RobotoSlab-ExtraBold.ttf */; };
		C9BD17FD2D3F735C00B654B3 /* RobotoSlab-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8F71A24AD0C8471F96059CFD /* RobotoSlab-ExtraLight.ttf */; };
		C9BD17FE2D3F735C00B654B3 /* RobotoSlab-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E5697234FBE340C996FC3872 /* RobotoSlab-Light.ttf */; };
		C9BD17FF2D3F735C00B654B3 /* RobotoSlab-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4FA8E961C20D4A10AA872577 /* RobotoSlab-Medium.ttf */; };
		C9BD18002D3F735C00B654B3 /* RobotoSlab-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 78F89C14143E4C25B73BB673 /* RobotoSlab-Regular.ttf */; };
		C9BD18012D3F735C00B654B3 /* RobotoSlab-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 3E8E337FB7B0463CA92AE8A9 /* RobotoSlab-SemiBold.ttf */; };
		C9BD18022D3F735C00B654B3 /* RobotoSlab-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9F8D91B51D304FD8898D7772 /* RobotoSlab-Thin.ttf */; };
		C9BD18032D3F735C00B654B3 /* Ubuntu-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D7CE8F732468427DA48144CE /* Ubuntu-Bold.ttf */; };
		C9BD18042D3F735C00B654B3 /* Ubuntu-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BD4DB3E8F4DB42379949139B /* Ubuntu-BoldItalic.ttf */; };
		C9BD18052D3F735C00B654B3 /* Ubuntu-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AF66074E67BB4746A54E0423 /* Ubuntu-Italic.ttf */; };
		C9BD18062D3F735C00B654B3 /* Ubuntu-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = FD85814FF762475AB404C962 /* Ubuntu-Light.ttf */; };
		C9BD18072D3F735C00B654B3 /* Ubuntu-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C0976C326A7F42D29B21D4C8 /* Ubuntu-LightItalic.ttf */; };
		C9BD18082D3F735C00B654B3 /* Ubuntu-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BEF7F60138E54349961EF8D4 /* Ubuntu-Medium.ttf */; };
		C9BD18092D3F735C00B654B3 /* Ubuntu-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 847C4B2E380C46A79E1D5076 /* Ubuntu-MediumItalic.ttf */; };
		C9BD180A2D3F735C00B654B3 /* Ubuntu-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E519FF95F85843D0A8E33CDF /* Ubuntu-Regular.ttf */; };
		C9BD18182D3F736700B654B3 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		C9BD18192D3F736700B654B3 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		C9BD181D2D3F736700B654B3 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		C9BD181F2D3F736700B654B3 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		C9BD18202D3F736700B654B3 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */; };
		C9BD18212D3F736700B654B3 /* RobotoSlab-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 35A2F5FFF24847A592877C0F /* RobotoSlab-Black.ttf */; };
		C9BD18222D3F736700B654B3 /* RobotoSlab-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C8E556B41B584D4A8AB00E0B /* RobotoSlab-Bold.ttf */; };
		C9BD18232D3F736700B654B3 /* RobotoSlab-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 0DEE490D95BD468A80E8EE45 /* RobotoSlab-ExtraBold.ttf */; };
		C9BD18242D3F736700B654B3 /* RobotoSlab-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8F71A24AD0C8471F96059CFD /* RobotoSlab-ExtraLight.ttf */; };
		C9BD18252D3F736700B654B3 /* RobotoSlab-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E5697234FBE340C996FC3872 /* RobotoSlab-Light.ttf */; };
		C9BD18262D3F736700B654B3 /* RobotoSlab-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4FA8E961C20D4A10AA872577 /* RobotoSlab-Medium.ttf */; };
		C9BD18272D3F736700B654B3 /* RobotoSlab-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 78F89C14143E4C25B73BB673 /* RobotoSlab-Regular.ttf */; };
		C9BD18282D3F736700B654B3 /* RobotoSlab-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 3E8E337FB7B0463CA92AE8A9 /* RobotoSlab-SemiBold.ttf */; };
		C9BD18292D3F736700B654B3 /* RobotoSlab-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9F8D91B51D304FD8898D7772 /* RobotoSlab-Thin.ttf */; };
		C9BD182A2D3F736700B654B3 /* Ubuntu-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D7CE8F732468427DA48144CE /* Ubuntu-Bold.ttf */; };
		C9BD182B2D3F736700B654B3 /* Ubuntu-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BD4DB3E8F4DB42379949139B /* Ubuntu-BoldItalic.ttf */; };
		C9BD182C2D3F736700B654B3 /* Ubuntu-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = AF66074E67BB4746A54E0423 /* Ubuntu-Italic.ttf */; };
		C9BD182D2D3F736700B654B3 /* Ubuntu-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = FD85814FF762475AB404C962 /* Ubuntu-Light.ttf */; };
		C9BD182E2D3F736700B654B3 /* Ubuntu-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C0976C326A7F42D29B21D4C8 /* Ubuntu-LightItalic.ttf */; };
		C9BD182F2D3F736700B654B3 /* Ubuntu-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BEF7F60138E54349961EF8D4 /* Ubuntu-Medium.ttf */; };
		C9BD18302D3F736700B654B3 /* Ubuntu-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 847C4B2E380C46A79E1D5076 /* Ubuntu-MediumItalic.ttf */; };
		C9BD18312D3F736700B654B3 /* Ubuntu-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E519FF95F85843D0A8E33CDF /* Ubuntu-Regular.ttf */; };
		C9BD18472D3F955700B654B3 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = C9BD18462D3F955700B654B3 /* GoogleService-Info.plist */; };
		C9CE169BD0C9484B894ACE61 /* RobotoSlab-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E5697234FBE340C996FC3872 /* RobotoSlab-Light.ttf */; };
		C9E4A6052D64483A00EEEAA4 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = C9E4A6042D64483A00EEEAA4 /* GoogleService-Info.plist */; };
		C9E9E1D42D5336B4001A10A8 /* Mapbox in Frameworks */ = {isa = PBXBuildFile; productRef = DA6B46926A24BB858E81CC9C /* Mapbox */; };
		C9E9E1D52D5336B4001A10A8 /* Mapbox in Frameworks */ = {isa = PBXBuildFile; productRef = 63CE544EBF4FF3A8B4DE5C18 /* Mapbox */; };
		C9E9E1D62D5336B4001A10A8 /* Mapbox in Frameworks */ = {isa = PBXBuildFile; productRef = C6DE59D43EFD817D2E39DEC1 /* Mapbox */; };
		C9E9E1D72D5336B4001A10A8 /* Mapbox in Frameworks */ = {isa = PBXBuildFile; productRef = 8B8EEC5C2B142080970FD2A4 /* Mapbox */; };
		C9E9E1D92D53372A001A10A8 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = C9E9E1D82D53372A001A10A8 /* GoogleService-Info.plist */; };
		CB61686A69554D28BAA25B85 /* Ubuntu-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E519FF95F85843D0A8E33CDF /* Ubuntu-Regular.ttf */; };
		CC22DDEDE87E248672E936C7 /* Pods_thousandgreens_thousandgreens_staging.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 91AB0AB466F97787ACB4A2CC /* Pods_thousandgreens_thousandgreens_staging.framework */; };
		D95CA534D07CA8FF1C8C2F39 /* Pods_thousandgreens.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D70E58D4083B45270D6BDC17 /* Pods_thousandgreens.framework */; };
		DCF5FE319B904A9B954F1D31 /* RobotoSlab-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8F71A24AD0C8471F96059CFD /* RobotoSlab-ExtraLight.ttf */; };
		EA02175F32E542ED85C689FF /* RobotoSlab-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 3E8E337FB7B0463CA92AE8A9 /* RobotoSlab-SemiBold.ttf */; };
		F2DCD838D7F34C60886DB17E /* RobotoSlab-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9F8D91B51D304FD8898D7772 /* RobotoSlab-Thin.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = thousandgreens;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* thousandgreensTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = thousandgreensTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* thousandgreensTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = thousandgreensTests.m; sourceTree = "<group>"; };
		09724154E5EB82845F45C968 /* Pods-thousandgreens.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-thousandgreens.release.xcconfig"; path = "Target Support Files/Pods-thousandgreens/Pods-thousandgreens.release.xcconfig"; sourceTree = "<group>"; };
		0DEE490D95BD468A80E8EE45 /* RobotoSlab-ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "RobotoSlab-ExtraBold.ttf"; path = "../src/assets/fonts/RobotoSlab-ExtraBold.ttf"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* thousandgreens.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = thousandgreens.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = thousandgreens/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = thousandgreens/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = thousandgreens/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = thousandgreens/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = thousandgreens/main.m; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = thousandgreens/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		218BB0C595188A4C26DFCDB8 /* Pods-thousandgreens-thousandgreens-dev.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-thousandgreens-thousandgreens-dev.release.xcconfig"; path = "Target Support Files/Pods-thousandgreens-thousandgreens-dev/Pods-thousandgreens-thousandgreens-dev.release.xcconfig"; sourceTree = "<group>"; };
		35A2F5FFF24847A592877C0F /* RobotoSlab-Black.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "RobotoSlab-Black.ttf"; path = "../src/assets/fonts/RobotoSlab-Black.ttf"; sourceTree = "<group>"; };
		3E8E337FB7B0463CA92AE8A9 /* RobotoSlab-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "RobotoSlab-SemiBold.ttf"; path = "../src/assets/fonts/RobotoSlab-SemiBold.ttf"; sourceTree = "<group>"; };
		4668F51D503F446CAF3A0B7C /* Pods-thousandgreens-thousandgreens-dev.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-thousandgreens-thousandgreens-dev.debug.xcconfig"; path = "Target Support Files/Pods-thousandgreens-thousandgreens-dev/Pods-thousandgreens-thousandgreens-dev.debug.xcconfig"; sourceTree = "<group>"; };
		4FA8E961C20D4A10AA872577 /* RobotoSlab-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "RobotoSlab-Medium.ttf"; path = "../src/assets/fonts/RobotoSlab-Medium.ttf"; sourceTree = "<group>"; };
		67BC3C1CBEC2839D1E2C7C53 /* Pods-thousandgreens-thousandgreens-staging.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-thousandgreens-thousandgreens-staging.debug.xcconfig"; path = "Target Support Files/Pods-thousandgreens-thousandgreens-staging/Pods-thousandgreens-thousandgreens-staging.debug.xcconfig"; sourceTree = "<group>"; };
		78F89C14143E4C25B73BB673 /* RobotoSlab-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "RobotoSlab-Regular.ttf"; path = "../src/assets/fonts/RobotoSlab-Regular.ttf"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = thousandgreens/LaunchScreen.storyboard; sourceTree = "<group>"; };
		847C4B2E380C46A79E1D5076 /* Ubuntu-MediumItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Ubuntu-MediumItalic.ttf"; path = "../src/assets/fonts/Ubuntu-MediumItalic.ttf"; sourceTree = "<group>"; };
		8F71A24AD0C8471F96059CFD /* RobotoSlab-ExtraLight.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "RobotoSlab-ExtraLight.ttf"; path = "../src/assets/fonts/RobotoSlab-ExtraLight.ttf"; sourceTree = "<group>"; };
		8F95E1174533835E409F54A7 /* Pods_thousandgreens_thousandgreens_dev.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_thousandgreens_thousandgreens_dev.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		91AB0AB466F97787ACB4A2CC /* Pods_thousandgreens_thousandgreens_staging.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_thousandgreens_thousandgreens_staging.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9BA1863D86A085E162C5FFA2 /* Pods-thousandgreens-thousandgreens-qa.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-thousandgreens-thousandgreens-qa.debug.xcconfig"; path = "Target Support Files/Pods-thousandgreens-thousandgreens-qa/Pods-thousandgreens-thousandgreens-qa.debug.xcconfig"; sourceTree = "<group>"; };
		9F8D91B51D304FD8898D7772 /* RobotoSlab-Thin.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "RobotoSlab-Thin.ttf"; path = "../src/assets/fonts/RobotoSlab-Thin.ttf"; sourceTree = "<group>"; };
		A434C99B5B3924ED207235F5 /* Pods-thousandgreens.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-thousandgreens.debug.xcconfig"; path = "Target Support Files/Pods-thousandgreens/Pods-thousandgreens.debug.xcconfig"; sourceTree = "<group>"; };
		AA098513B4C14ADDA3EEF518 /* Pods-thousandgreens-thousandgreens-staging.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-thousandgreens-thousandgreens-staging.release.xcconfig"; path = "Target Support Files/Pods-thousandgreens-thousandgreens-staging/Pods-thousandgreens-thousandgreens-staging.release.xcconfig"; sourceTree = "<group>"; };
		AF66074E67BB4746A54E0423 /* Ubuntu-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Ubuntu-Italic.ttf"; path = "../src/assets/fonts/Ubuntu-Italic.ttf"; sourceTree = "<group>"; };
		BD4DB3E8F4DB42379949139B /* Ubuntu-BoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Ubuntu-BoldItalic.ttf"; path = "../src/assets/fonts/Ubuntu-BoldItalic.ttf"; sourceTree = "<group>"; };
		BEF7F60138E54349961EF8D4 /* Ubuntu-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Ubuntu-Medium.ttf"; path = "../src/assets/fonts/Ubuntu-Medium.ttf"; sourceTree = "<group>"; };
		C0976C326A7F42D29B21D4C8 /* Ubuntu-LightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Ubuntu-LightItalic.ttf"; path = "../src/assets/fonts/Ubuntu-LightItalic.ttf"; sourceTree = "<group>"; };
		C8E556B41B584D4A8AB00E0B /* RobotoSlab-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "RobotoSlab-Bold.ttf"; path = "../src/assets/fonts/RobotoSlab-Bold.ttf"; sourceTree = "<group>"; };
		C93D7F092D59FF5800543A51 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "../staging/GoogleService-Info.plist"; sourceTree = "<group>"; };
		C94216AB2D3589000043E2A9 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "../../thousandgreens-mobile/ios/thousandgreens/GoogleService-Info.plist"; sourceTree = "<group>"; };
		C96BDC542D3E51C900D5A03A /* thousandgreens.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = thousandgreens.entitlements; path = thousandgreens/thousandgreens.entitlements; sourceTree = "<group>"; };
		C9BD17EC2D3F731D00B654B3 /* thousandgreens-dev.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "thousandgreens-dev.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		C9BD17ED2D3F731D00B654B3 /* thousandgreens-dev-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "thousandgreens-dev-Info.plist"; path = "/Users/<USER>/Projects/React-native/thousandgreens-mobile/ios/thousandgreens-dev-Info.plist"; sourceTree = "<absolute>"; };
		C9BD18132D3F735C00B654B3 /* thousandgreens-qa.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "thousandgreens-qa.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		C9BD18142D3F735C00B654B3 /* thousandgreens-qa-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "thousandgreens-qa-Info.plist"; path = "/Users/<USER>/Projects/React-native/thousandgreens-mobile/ios/thousandgreens-qa-Info.plist"; sourceTree = "<absolute>"; };
		C9BD183A2D3F736700B654B3 /* thousandgreens-staging.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "thousandgreens-staging.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		C9BD183B2D3F736700B654B3 /* thousandgreens-staging-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "thousandgreens-staging-Info.plist"; path = "/Users/<USER>/Projects/React-native/thousandgreens-mobile/ios/thousandgreens-staging-Info.plist"; sourceTree = "<absolute>"; };
		C9BD183C2D3F785F00B654B3 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "../qa/GoogleService-Info.plist"; sourceTree = "<group>"; };
		C9BD18462D3F955700B654B3 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "../dev/GoogleService-Info.plist"; sourceTree = "<group>"; };
		C9E4A6042D64483A00EEEAA4 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "../qa/GoogleService-Info.plist"; sourceTree = "<group>"; };
		C9E9E1D82D53372A001A10A8 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "../prod/GoogleService-Info.plist"; sourceTree = "<group>"; };
		D016A774497D8E37B4909671 /* Pods-thousandgreens-thousandgreens-qa.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-thousandgreens-thousandgreens-qa.release.xcconfig"; path = "Target Support Files/Pods-thousandgreens-thousandgreens-qa/Pods-thousandgreens-thousandgreens-qa.release.xcconfig"; sourceTree = "<group>"; };
		D70E58D4083B45270D6BDC17 /* Pods_thousandgreens.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_thousandgreens.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		D7CE8F732468427DA48144CE /* Ubuntu-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Ubuntu-Bold.ttf"; path = "../src/assets/fonts/Ubuntu-Bold.ttf"; sourceTree = "<group>"; };
		E519FF95F85843D0A8E33CDF /* Ubuntu-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Ubuntu-Regular.ttf"; path = "../src/assets/fonts/Ubuntu-Regular.ttf"; sourceTree = "<group>"; };
		E5697234FBE340C996FC3872 /* RobotoSlab-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "RobotoSlab-Light.ttf"; path = "../src/assets/fonts/RobotoSlab-Light.ttf"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		F7DD6E57746A13BC06F63558 /* Pods_thousandgreens_thousandgreens_qa.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_thousandgreens_thousandgreens_qa.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		FD85814FF762475AB404C962 /* Ubuntu-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Ubuntu-Light.ttf"; path = "../src/assets/fonts/Ubuntu-Light.ttf"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C9E9E1D42D5336B4001A10A8 /* Mapbox in Frameworks */,
				D95CA534D07CA8FF1C8C2F39 /* Pods_thousandgreens.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9BD17CC2D3F731D00B654B3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C9E9E1D52D5336B4001A10A8 /* Mapbox in Frameworks */,
				650994EF02192B0F6B4658BB /* Pods_thousandgreens_thousandgreens_dev.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9BD17F32D3F735C00B654B3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C9E9E1D62D5336B4001A10A8 /* Mapbox in Frameworks */,
				40A79776CD472826AAF5EF62 /* Pods_thousandgreens_thousandgreens_qa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9BD181A2D3F736700B654B3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C9E9E1D72D5336B4001A10A8 /* Mapbox in Frameworks */,
				CC22DDEDE87E248672E936C7 /* Pods_thousandgreens_thousandgreens_staging.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* thousandgreensTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* thousandgreensTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = thousandgreensTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* thousandgreens */ = {
			isa = PBXGroup;
			children = (
				C96BDC542D3E51C900D5A03A /* thousandgreens.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				C94216AB2D3589000043E2A9 /* GoogleService-Info.plist */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
			);
			name = thousandgreens;
			sourceTree = "<group>";
		};
		160754A4AA194183AEBCF688 /* Resources */ = {
			isa = PBXGroup;
			children = (
				35A2F5FFF24847A592877C0F /* RobotoSlab-Black.ttf */,
				C8E556B41B584D4A8AB00E0B /* RobotoSlab-Bold.ttf */,
				0DEE490D95BD468A80E8EE45 /* RobotoSlab-ExtraBold.ttf */,
				8F71A24AD0C8471F96059CFD /* RobotoSlab-ExtraLight.ttf */,
				E5697234FBE340C996FC3872 /* RobotoSlab-Light.ttf */,
				4FA8E961C20D4A10AA872577 /* RobotoSlab-Medium.ttf */,
				78F89C14143E4C25B73BB673 /* RobotoSlab-Regular.ttf */,
				3E8E337FB7B0463CA92AE8A9 /* RobotoSlab-SemiBold.ttf */,
				9F8D91B51D304FD8898D7772 /* RobotoSlab-Thin.ttf */,
				D7CE8F732468427DA48144CE /* Ubuntu-Bold.ttf */,
				BD4DB3E8F4DB42379949139B /* Ubuntu-BoldItalic.ttf */,
				AF66074E67BB4746A54E0423 /* Ubuntu-Italic.ttf */,
				FD85814FF762475AB404C962 /* Ubuntu-Light.ttf */,
				C0976C326A7F42D29B21D4C8 /* Ubuntu-LightItalic.ttf */,
				BEF7F60138E54349961EF8D4 /* Ubuntu-Medium.ttf */,
				847C4B2E380C46A79E1D5076 /* Ubuntu-MediumItalic.ttf */,
				E519FF95F85843D0A8E33CDF /* Ubuntu-Regular.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				D70E58D4083B45270D6BDC17 /* Pods_thousandgreens.framework */,
				8F95E1174533835E409F54A7 /* Pods_thousandgreens_thousandgreens_dev.framework */,
				F7DD6E57746A13BC06F63558 /* Pods_thousandgreens_thousandgreens_qa.framework */,
				91AB0AB466F97787ACB4A2CC /* Pods_thousandgreens_thousandgreens_staging.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				C9BD183C2D3F785F00B654B3 /* GoogleService-Info.plist */,
				C9BD18462D3F955700B654B3 /* GoogleService-Info.plist */,
				C9E9E1D82D53372A001A10A8 /* GoogleService-Info.plist */,
				C93D7F092D59FF5800543A51 /* GoogleService-Info.plist */,
				C9E4A6042D64483A00EEEAA4 /* GoogleService-Info.plist */,
				13B07FAE1A68108700A75B9A /* thousandgreens */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* thousandgreensTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				160754A4AA194183AEBCF688 /* Resources */,
				C9BD17ED2D3F731D00B654B3 /* thousandgreens-dev-Info.plist */,
				C9BD18142D3F735C00B654B3 /* thousandgreens-qa-Info.plist */,
				C9BD183B2D3F736700B654B3 /* thousandgreens-staging-Info.plist */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* thousandgreens.app */,
				00E356EE1AD99517003FC87E /* thousandgreensTests.xctest */,
				C9BD17EC2D3F731D00B654B3 /* thousandgreens-dev.app */,
				C9BD18132D3F735C00B654B3 /* thousandgreens-qa.app */,
				C9BD183A2D3F736700B654B3 /* thousandgreens-staging.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				A434C99B5B3924ED207235F5 /* Pods-thousandgreens.debug.xcconfig */,
				09724154E5EB82845F45C968 /* Pods-thousandgreens.release.xcconfig */,
				4668F51D503F446CAF3A0B7C /* Pods-thousandgreens-thousandgreens-dev.debug.xcconfig */,
				218BB0C595188A4C26DFCDB8 /* Pods-thousandgreens-thousandgreens-dev.release.xcconfig */,
				9BA1863D86A085E162C5FFA2 /* Pods-thousandgreens-thousandgreens-qa.debug.xcconfig */,
				D016A774497D8E37B4909671 /* Pods-thousandgreens-thousandgreens-qa.release.xcconfig */,
				67BC3C1CBEC2839D1E2C7C53 /* Pods-thousandgreens-thousandgreens-staging.debug.xcconfig */,
				AA098513B4C14ADDA3EEF518 /* Pods-thousandgreens-thousandgreens-staging.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* thousandgreensTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "thousandgreensTests" */;
			buildPhases = (
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = thousandgreensTests;
			productName = thousandgreensTests;
			productReference = 00E356EE1AD99517003FC87E /* thousandgreensTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* thousandgreens */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "thousandgreens" */;
			buildPhases = (
				AC080031CA54643F0C18D347 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				2CD22ADEDA2E5956835157CA /* [CP] Embed Pods Frameworks */,
				CC083EE48015BC6C675FCBF0 /* [CP] Copy Pods Resources */,
				ED42BDD325930393B325F782 /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = thousandgreens;
			packageProductDependencies = (
				DA6B46926A24BB858E81CC9C /* Mapbox */,
			);
			productName = thousandgreens;
			productReference = 13B07F961A680F5B00A75B9A /* thousandgreens.app */;
			productType = "com.apple.product-type.application";
		};
		C9BD17C72D3F731D00B654B3 /* thousandgreens-dev */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C9BD17E92D3F731D00B654B3 /* Build configuration list for PBXNativeTarget "thousandgreens-dev" */;
			buildPhases = (
				5DE3AF8A8587A685FC8985CE /* [CP] Check Pods Manifest.lock */,
				C9BD17C92D3F731D00B654B3 /* Sources */,
				C9BD17CC2D3F731D00B654B3 /* Frameworks */,
				C9BD17CE2D3F731D00B654B3 /* Resources */,
				C9BD17E42D3F731D00B654B3 /* Bundle React Native code and images */,
				3F47814A340641EF40A24B18 /* [CP] Embed Pods Frameworks */,
				5A1F38AC419DEB85AD126FA8 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "thousandgreens-dev";
			packageProductDependencies = (
				63CE544EBF4FF3A8B4DE5C18 /* Mapbox */,
			);
			productName = thousandgreens;
			productReference = C9BD17EC2D3F731D00B654B3 /* thousandgreens-dev.app */;
			productType = "com.apple.product-type.application";
		};
		C9BD17EE2D3F735C00B654B3 /* thousandgreens-qa */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C9BD18102D3F735C00B654B3 /* Build configuration list for PBXNativeTarget "thousandgreens-qa" */;
			buildPhases = (
				B348B6B28F490DECED51567C /* [CP] Check Pods Manifest.lock */,
				C9BD17F02D3F735C00B654B3 /* Sources */,
				C9BD17F32D3F735C00B654B3 /* Frameworks */,
				C9BD17F52D3F735C00B654B3 /* Resources */,
				C9BD180B2D3F735C00B654B3 /* Bundle React Native code and images */,
				F30C96C245861A41DC9716FF /* [CP] Embed Pods Frameworks */,
				CC357E42A0B4AAE2CC4A42D3 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "thousandgreens-qa";
			packageProductDependencies = (
				C6DE59D43EFD817D2E39DEC1 /* Mapbox */,
			);
			productName = thousandgreens;
			productReference = C9BD18132D3F735C00B654B3 /* thousandgreens-qa.app */;
			productType = "com.apple.product-type.application";
		};
		C9BD18152D3F736700B654B3 /* thousandgreens-staging */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C9BD18372D3F736700B654B3 /* Build configuration list for PBXNativeTarget "thousandgreens-staging" */;
			buildPhases = (
				606548C2940116DC0D699ECC /* [CP] Check Pods Manifest.lock */,
				C9BD18172D3F736700B654B3 /* Sources */,
				C9BD181A2D3F736700B654B3 /* Frameworks */,
				C9BD181C2D3F736700B654B3 /* Resources */,
				C9BD18322D3F736700B654B3 /* Bundle React Native code and images */,
				0D543061DDC4FBAAAF199752 /* [CP] Embed Pods Frameworks */,
				D450BAAB41E0BD9F783DF602 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "thousandgreens-staging";
			packageProductDependencies = (
				8B8EEC5C2B142080970FD2A4 /* Mapbox */,
			);
			productName = thousandgreens;
			productReference = C9BD183A2D3F736700B654B3 /* thousandgreens-staging.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "thousandgreens" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			packageReferences = (
				3677F3AEA0914535C3FC1191 /* XCRemoteSwiftPackageReference "maplibre-gl-native-distribution" */,
			);
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* thousandgreens */,
				00E356ED1AD99517003FC87E /* thousandgreensTests */,
				C9BD17C72D3F731D00B654B3 /* thousandgreens-dev */,
				C9BD17EE2D3F735C00B654B3 /* thousandgreens-qa */,
				C9BD18152D3F736700B654B3 /* thousandgreens-staging */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C9E9E1D92D53372A001A10A8 /* GoogleService-Info.plist in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				83E1DD8DDF4033511757E14C /* PrivacyInfo.xcprivacy in Resources */,
				76EEA6D98A10425E9226E658 /* RobotoSlab-Black.ttf in Resources */,
				4B2077275A514D56822DAE9D /* RobotoSlab-Bold.ttf in Resources */,
				B674A409604246B0A937EB38 /* RobotoSlab-ExtraBold.ttf in Resources */,
				DCF5FE319B904A9B954F1D31 /* RobotoSlab-ExtraLight.ttf in Resources */,
				C9CE169BD0C9484B894ACE61 /* RobotoSlab-Light.ttf in Resources */,
				201A40C9DF2843E69600F5DC /* RobotoSlab-Medium.ttf in Resources */,
				AB8CAAC070A54D3D8F62BE2B /* RobotoSlab-Regular.ttf in Resources */,
				EA02175F32E542ED85C689FF /* RobotoSlab-SemiBold.ttf in Resources */,
				F2DCD838D7F34C60886DB17E /* RobotoSlab-Thin.ttf in Resources */,
				33070840E37F4648BCAB5F34 /* Ubuntu-Bold.ttf in Resources */,
				11C4379CA1654C7EBA8DB687 /* Ubuntu-BoldItalic.ttf in Resources */,
				90887EFE8D024BC88F53BF6B /* Ubuntu-Italic.ttf in Resources */,
				36297CFD53014CB696138E55 /* Ubuntu-Light.ttf in Resources */,
				71CFF469B0EC439BAF0E2B73 /* Ubuntu-LightItalic.ttf in Resources */,
				872E5AB0B71E4375AC415ED3 /* Ubuntu-Medium.ttf in Resources */,
				2531627E095B4571B1153D2D /* Ubuntu-MediumItalic.ttf in Resources */,
				CB61686A69554D28BAA25B85 /* Ubuntu-Regular.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9BD17CE2D3F731D00B654B3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C9BD18472D3F955700B654B3 /* GoogleService-Info.plist in Resources */,
				C9BD17CF2D3F731D00B654B3 /* LaunchScreen.storyboard in Resources */,
				C9BD17D12D3F731D00B654B3 /* Images.xcassets in Resources */,
				C9BD17D22D3F731D00B654B3 /* PrivacyInfo.xcprivacy in Resources */,
				C9BD17D32D3F731D00B654B3 /* RobotoSlab-Black.ttf in Resources */,
				C9BD17D42D3F731D00B654B3 /* RobotoSlab-Bold.ttf in Resources */,
				C9BD17D52D3F731D00B654B3 /* RobotoSlab-ExtraBold.ttf in Resources */,
				C9BD17D62D3F731D00B654B3 /* RobotoSlab-ExtraLight.ttf in Resources */,
				C9BD17D72D3F731D00B654B3 /* RobotoSlab-Light.ttf in Resources */,
				C9BD17D82D3F731D00B654B3 /* RobotoSlab-Medium.ttf in Resources */,
				C9BD17D92D3F731D00B654B3 /* RobotoSlab-Regular.ttf in Resources */,
				C9BD17DA2D3F731D00B654B3 /* RobotoSlab-SemiBold.ttf in Resources */,
				C9BD17DB2D3F731D00B654B3 /* RobotoSlab-Thin.ttf in Resources */,
				C9BD17DC2D3F731D00B654B3 /* Ubuntu-Bold.ttf in Resources */,
				C9BD17DD2D3F731D00B654B3 /* Ubuntu-BoldItalic.ttf in Resources */,
				C9BD17DE2D3F731D00B654B3 /* Ubuntu-Italic.ttf in Resources */,
				C9BD17DF2D3F731D00B654B3 /* Ubuntu-Light.ttf in Resources */,
				C9BD17E02D3F731D00B654B3 /* Ubuntu-LightItalic.ttf in Resources */,
				C9BD17E12D3F731D00B654B3 /* Ubuntu-Medium.ttf in Resources */,
				C9BD17E22D3F731D00B654B3 /* Ubuntu-MediumItalic.ttf in Resources */,
				C9BD17E32D3F731D00B654B3 /* Ubuntu-Regular.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9BD17F52D3F735C00B654B3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C9E4A6052D64483A00EEEAA4 /* GoogleService-Info.plist in Resources */,
				C9BD17F62D3F735C00B654B3 /* LaunchScreen.storyboard in Resources */,
				C9BD17F82D3F735C00B654B3 /* Images.xcassets in Resources */,
				C9BD17F92D3F735C00B654B3 /* PrivacyInfo.xcprivacy in Resources */,
				C9BD17FA2D3F735C00B654B3 /* RobotoSlab-Black.ttf in Resources */,
				C9BD17FB2D3F735C00B654B3 /* RobotoSlab-Bold.ttf in Resources */,
				C9BD17FC2D3F735C00B654B3 /* RobotoSlab-ExtraBold.ttf in Resources */,
				C9BD17FD2D3F735C00B654B3 /* RobotoSlab-ExtraLight.ttf in Resources */,
				C9BD17FE2D3F735C00B654B3 /* RobotoSlab-Light.ttf in Resources */,
				C9BD17FF2D3F735C00B654B3 /* RobotoSlab-Medium.ttf in Resources */,
				C9BD18002D3F735C00B654B3 /* RobotoSlab-Regular.ttf in Resources */,
				C9BD18012D3F735C00B654B3 /* RobotoSlab-SemiBold.ttf in Resources */,
				C9BD18022D3F735C00B654B3 /* RobotoSlab-Thin.ttf in Resources */,
				C9BD18032D3F735C00B654B3 /* Ubuntu-Bold.ttf in Resources */,
				C9BD18042D3F735C00B654B3 /* Ubuntu-BoldItalic.ttf in Resources */,
				C9BD18052D3F735C00B654B3 /* Ubuntu-Italic.ttf in Resources */,
				C9BD18062D3F735C00B654B3 /* Ubuntu-Light.ttf in Resources */,
				C9BD18072D3F735C00B654B3 /* Ubuntu-LightItalic.ttf in Resources */,
				C9BD18082D3F735C00B654B3 /* Ubuntu-Medium.ttf in Resources */,
				C9BD18092D3F735C00B654B3 /* Ubuntu-MediumItalic.ttf in Resources */,
				C9BD180A2D3F735C00B654B3 /* Ubuntu-Regular.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9BD181C2D3F736700B654B3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C93D7F0A2D59FF5800543A51 /* GoogleService-Info.plist in Resources */,
				C9BD181D2D3F736700B654B3 /* LaunchScreen.storyboard in Resources */,
				C9BD181F2D3F736700B654B3 /* Images.xcassets in Resources */,
				C9BD18202D3F736700B654B3 /* PrivacyInfo.xcprivacy in Resources */,
				C9BD18212D3F736700B654B3 /* RobotoSlab-Black.ttf in Resources */,
				C9BD18222D3F736700B654B3 /* RobotoSlab-Bold.ttf in Resources */,
				C9BD18232D3F736700B654B3 /* RobotoSlab-ExtraBold.ttf in Resources */,
				C9BD18242D3F736700B654B3 /* RobotoSlab-ExtraLight.ttf in Resources */,
				C9BD18252D3F736700B654B3 /* RobotoSlab-Light.ttf in Resources */,
				C9BD18262D3F736700B654B3 /* RobotoSlab-Medium.ttf in Resources */,
				C9BD18272D3F736700B654B3 /* RobotoSlab-Regular.ttf in Resources */,
				C9BD18282D3F736700B654B3 /* RobotoSlab-SemiBold.ttf in Resources */,
				C9BD18292D3F736700B654B3 /* RobotoSlab-Thin.ttf in Resources */,
				C9BD182A2D3F736700B654B3 /* Ubuntu-Bold.ttf in Resources */,
				C9BD182B2D3F736700B654B3 /* Ubuntu-BoldItalic.ttf in Resources */,
				C9BD182C2D3F736700B654B3 /* Ubuntu-Italic.ttf in Resources */,
				C9BD182D2D3F736700B654B3 /* Ubuntu-Light.ttf in Resources */,
				C9BD182E2D3F736700B654B3 /* Ubuntu-LightItalic.ttf in Resources */,
				C9BD182F2D3F736700B654B3 /* Ubuntu-Medium.ttf in Resources */,
				C9BD18302D3F736700B654B3 /* Ubuntu-MediumItalic.ttf in Resources */,
				C9BD18312D3F736700B654B3 /* Ubuntu-Regular.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		0D543061DDC4FBAAAF199752 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-staging/Pods-thousandgreens-thousandgreens-staging-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-staging/Pods-thousandgreens-thousandgreens-staging-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-staging/Pods-thousandgreens-thousandgreens-staging-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		2CD22ADEDA2E5956835157CA /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens/Pods-thousandgreens-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens/Pods-thousandgreens-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-thousandgreens/Pods-thousandgreens-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		3F47814A340641EF40A24B18 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-dev/Pods-thousandgreens-thousandgreens-dev-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-dev/Pods-thousandgreens-thousandgreens-dev-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-dev/Pods-thousandgreens-thousandgreens-dev-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		5A1F38AC419DEB85AD126FA8 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-dev/Pods-thousandgreens-thousandgreens-dev-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-dev/Pods-thousandgreens-thousandgreens-dev-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-dev/Pods-thousandgreens-thousandgreens-dev-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		5DE3AF8A8587A685FC8985CE /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-thousandgreens-thousandgreens-dev-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		606548C2940116DC0D699ECC /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-thousandgreens-thousandgreens-staging-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		AC080031CA54643F0C18D347 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-thousandgreens-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		B348B6B28F490DECED51567C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-thousandgreens-thousandgreens-qa-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		C9BD17E42D3F731D00B654B3 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		C9BD180B2D3F735C00B654B3 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		C9BD18322D3F736700B654B3 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		CC083EE48015BC6C675FCBF0 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens/Pods-thousandgreens-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens/Pods-thousandgreens-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-thousandgreens/Pods-thousandgreens-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		CC357E42A0B4AAE2CC4A42D3 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-qa/Pods-thousandgreens-thousandgreens-qa-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-qa/Pods-thousandgreens-thousandgreens-qa-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-qa/Pods-thousandgreens-thousandgreens-qa-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		D450BAAB41E0BD9F783DF602 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-staging/Pods-thousandgreens-thousandgreens-staging-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-staging/Pods-thousandgreens-thousandgreens-staging-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-staging/Pods-thousandgreens-thousandgreens-staging-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		ED42BDD325930393B325F782 /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		F30C96C245861A41DC9716FF /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-qa/Pods-thousandgreens-thousandgreens-qa-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-qa/Pods-thousandgreens-thousandgreens-qa-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-thousandgreens-thousandgreens-qa/Pods-thousandgreens-thousandgreens-qa-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* thousandgreensTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9BD17C92D3F731D00B654B3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C9BD17CA2D3F731D00B654B3 /* AppDelegate.mm in Sources */,
				C9BD17CB2D3F731D00B654B3 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9BD17F02D3F735C00B654B3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C9BD17F12D3F735C00B654B3 /* AppDelegate.mm in Sources */,
				C9BD17F22D3F735C00B654B3 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C9BD18172D3F736700B654B3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C9BD18182D3F736700B654B3 /* AppDelegate.mm in Sources */,
				C9BD18192D3F736700B654B3 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* thousandgreens */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = thousandgreensTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/thousandgreens.app/thousandgreens";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = thousandgreensTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/thousandgreens.app/thousandgreens";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A434C99B5B3924ED207235F5 /* Pods-thousandgreens.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = thousandgreens/thousandgreens.entitlements;
				CURRENT_PROJECT_VERSION = 132;
				DEVELOPMENT_TEAM = 5YR93VXR5M;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = thousandgreens/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.4.2;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.thousand-greens";
				PRODUCT_NAME = thousandgreens;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 09724154E5EB82845F45C968 /* Pods-thousandgreens.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = thousandgreens/thousandgreens.entitlements;
				CURRENT_PROJECT_VERSION = 132;
				DEVELOPMENT_TEAM = 5YR93VXR5M;
				INFOPLIST_FILE = thousandgreens/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.4.2;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.thousand-greens";
				PRODUCT_NAME = thousandgreens;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		C9BD17EA2D3F731D00B654B3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4668F51D503F446CAF3A0B7C /* Pods-thousandgreens-thousandgreens-dev.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = thousandgreens/thousandgreens.entitlements;
				CURRENT_PROJECT_VERSION = 131;
				DEVELOPMENT_TEAM = 5YR93VXR5M;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "thousandgreens-dev-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.4.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.thousand-greens";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		C9BD17EB2D3F731D00B654B3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 218BB0C595188A4C26DFCDB8 /* Pods-thousandgreens-thousandgreens-dev.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = thousandgreens/thousandgreens.entitlements;
				CURRENT_PROJECT_VERSION = 131;
				DEVELOPMENT_TEAM = 5YR93VXR5M;
				INFOPLIST_FILE = "thousandgreens-dev-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.4.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.thousand-greens";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		C9BD18112D3F735C00B654B3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9BA1863D86A085E162C5FFA2 /* Pods-thousandgreens-thousandgreens-qa.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = thousandgreens/thousandgreens.entitlements;
				CURRENT_PROJECT_VERSION = 120;
				DEVELOPMENT_TEAM = 5YR93VXR5M;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "thousandgreens-qa-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.75.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
					"\"-Xlinker -no_deduplicate\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.thousand-greens";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		C9BD18122D3F735C00B654B3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D016A774497D8E37B4909671 /* Pods-thousandgreens-thousandgreens-qa.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = thousandgreens/thousandgreens.entitlements;
				CURRENT_PROJECT_VERSION = 120;
				DEVELOPMENT_TEAM = 5YR93VXR5M;
				INFOPLIST_FILE = "thousandgreens-qa-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.75.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
					"\"-Xlinker -no_deduplicate\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.thousand-greens";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		C9BD18382D3F736700B654B3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 67BC3C1CBEC2839D1E2C7C53 /* Pods-thousandgreens-thousandgreens-staging.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = thousandgreens/thousandgreens.entitlements;
				CURRENT_PROJECT_VERSION = 120;
				DEVELOPMENT_TEAM = 5YR93VXR5M;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "thousandgreens-staging-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.75.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.thousand-greens";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		C9BD18392D3F736700B654B3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AA098513B4C14ADDA3EEF518 /* Pods-thousandgreens-thousandgreens-staging.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = thousandgreens/thousandgreens.entitlements;
				CURRENT_PROJECT_VERSION = 120;
				DEVELOPMENT_TEAM = 5YR93VXR5M;
				INFOPLIST_FILE = "thousandgreens-staging-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.75.3;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.thousand-greens";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "thousandgreensTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "thousandgreens" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "thousandgreens" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C9BD17E92D3F731D00B654B3 /* Build configuration list for PBXNativeTarget "thousandgreens-dev" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C9BD17EA2D3F731D00B654B3 /* Debug */,
				C9BD17EB2D3F731D00B654B3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C9BD18102D3F735C00B654B3 /* Build configuration list for PBXNativeTarget "thousandgreens-qa" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C9BD18112D3F735C00B654B3 /* Debug */,
				C9BD18122D3F735C00B654B3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C9BD18372D3F736700B654B3 /* Build configuration list for PBXNativeTarget "thousandgreens-staging" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C9BD18382D3F736700B654B3 /* Debug */,
				C9BD18392D3F736700B654B3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		3677F3AEA0914535C3FC1191 /* XCRemoteSwiftPackageReference "maplibre-gl-native-distribution" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/maplibre/maplibre-gl-native-distribution";
			requirement = {
				kind = exactVersion;
				version = 5.12.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		63CE544EBF4FF3A8B4DE5C18 /* Mapbox */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3677F3AEA0914535C3FC1191 /* XCRemoteSwiftPackageReference "maplibre-gl-native-distribution" */;
			productName = Mapbox;
		};
		8B8EEC5C2B142080970FD2A4 /* Mapbox */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3677F3AEA0914535C3FC1191 /* XCRemoteSwiftPackageReference "maplibre-gl-native-distribution" */;
			productName = Mapbox;
		};
		C6DE59D43EFD817D2E39DEC1 /* Mapbox */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3677F3AEA0914535C3FC1191 /* XCRemoteSwiftPackageReference "maplibre-gl-native-distribution" */;
			productName = Mapbox;
		};
		DA6B46926A24BB858E81CC9C /* Mapbox */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3677F3AEA0914535C3FC1191 /* XCRemoteSwiftPackageReference "maplibre-gl-native-distribution" */;
			productName = Mapbox;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
