# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, '15.6' # Set the minimum iOS version to match Her<PERSON> requirements

prepare_react_native_project!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

# Define shared pods here
def share_pods
  # Firebase dependencies
  pod 'Firebase/Core'
  pod 'Firebase/Messaging'
  
  # React Native Firebase Auth
  pod 'RNFBAuth', :path => '../node_modules/@react-native-firebase/auth'

  # Other dependencies
  pod 'rn-fetch-blob', :path => '../node_modules/rn-fetch-blob'

  # Rollbar
  pod 'RollbarReactNative', path: '../node_modules/rollbar-react-native'

  # New Relic
  pod 'NewRelicAgent', '~> 7.5.6'

  # React Native Document Picker

  # CleverTap
  pod 'clevertap-react-native', :path => '../node_modules/clevertap-react-native', modular_headers: true
  pod 'CleverTap-iOS-SDK', modular_headers: true
end

target 'thousandgreens' do
  config = use_native_modules!

  # Use Frameworks configuration
  use_frameworks! :linkage => :static

  # Pre-install hook for Mapbox integration
  pre_install do |installer|
    $RNMapboxMaps.pre_install(installer)

    # Add custom pre-install logic for react-native-document-picker
    installer.pod_targets.each do |pod|
      if pod.name.eql?('react-native-document-picker')
        def pod.build_type
          Pod::BuildType.static_library
        end
      end
    end
  end

  # React Native configuration
  use_react_native!(
    :path => config[:reactNativePath],
    # Absolute path to the application root
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  # Include shared pods
  share_pods

  # Define additional environment targets
  target 'thousandgreens-dev' do
    share_pods
  end
  
  target 'thousandgreens-staging' do
    share_pods
  end
  
  target 'thousandgreens-qa' do
    share_pods
  end

  # Post-install configurations
  post_install do |installer|
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
    )

    # Post-install hook for Mapbox integration
    $RNMapboxMaps.post_install(installer)
  end
end
