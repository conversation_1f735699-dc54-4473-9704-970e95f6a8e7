import { StyleSheet } from 'react-native';
import React, { useContext } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

import { RootStackParamList } from '../interface/type';
import { REQUESTS_MODULE_UNDER_MAINTENANCE_DESCRIPTION } from '../utils/constants/strings';
import { GlobalContext } from '../context/contextApi';
import { REQUESTS_MODULE_UNDER_MAINTENANCE } from '../utils/constants/strings';
import ModuleMaintenance from '../screens/moduleMaintainence/ModuleMaintenance';
import CreateRequestMap from '../screens/play/my-tg-group-members screen/CreateRequestMap';

type CreateRequestToAllWrapperProps = NativeStackScreenProps<RootStackParamList, 'CreateRequestMap'>;

const CreateRequestToAllWrapper = ({ route }: CreateRequestToAllWrapperProps) => {
    const { state } = useContext(GlobalContext);

    const isUnderMaintenance = state?.tealDotStatus?.maintenanceStatus?.request;
    if (isUnderMaintenance) {
        return (
            <ModuleMaintenance
                title={REQUESTS_MODULE_UNDER_MAINTENANCE}
                description={REQUESTS_MODULE_UNDER_MAINTENANCE_DESCRIPTION}
                isNestedScreen={true}
                headerTitle={'Create Request'}
            />
        );
    }

    return <CreateRequestMap route={route} />;
};

export default CreateRequestToAllWrapper;

const styles = StyleSheet.create({});
