import { StyleSheet } from 'react-native';
import React, { useContext } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

import { RootStackParamList } from '../interface/type';
import { GlobalContext } from '../context/contextApi';
import { REQUESTS_MODULE_UNDER_MAINTENANCE_DESCRIPTION } from '../utils/constants/strings';
import { REQUESTS_MODULE_UNDER_MAINTENANCE } from '../utils/constants/strings';
import ModuleMaintenance from '../screens/moduleMaintainence/ModuleMaintenance';
import CreateRequest from '../screens/requests/CreateRequest';

type CreateRequestWrapperProps = NativeStackScreenProps<RootStackParamList, 'Create Request'>;

const CreateRequestWrapper = ({ route }: CreateRequestWrapperProps) => {
    const { state } = useContext(GlobalContext);

    const isUnderMaintenance = state?.tealDotStatus?.maintenanceStatus?.request;
    if (isUnderMaintenance) {
        return (
            <ModuleMaintenance
                title={REQUESTS_MODULE_UNDER_MAINTENANCE}
                description={REQUESTS_MODULE_UNDER_MAINTENANCE_DESCRIPTION}
                isNestedScreen={true}
                headerTitle={'Create New Request'}
            />
        );
    }

    return <CreateRequest route={route} />;
};

export default CreateRequestWrapper;

const styles = StyleSheet.create({});
