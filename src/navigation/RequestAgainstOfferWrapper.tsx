import { StyleSheet } from 'react-native';
import React, { useContext } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

import { GlobalContext } from '../context/contextApi';
import { REQUESTS_MODULE_UNDER_MAINTENANCE_DESCRIPTION } from '../utils/constants/strings';
import ModuleMaintenance from '../screens/moduleMaintainence/ModuleMaintenance';
import { REQUESTS_MODULE_UNDER_MAINTENANCE } from '../utils/constants/strings';
import RequestAgainstOfferScreen from '../screens/play/RequestAgainstOfferScreen';
import { RootStackParamList } from '../interface/type';

type RequestAgainstOfferWrapperProps = NativeStackScreenProps<RootStackParamList, 'Request Against Offer'>;

const RequestAgainstOfferWrapper = ({ navigation, route }: RequestAgainstOfferWrapperProps) => {
    const { state } = useContext(GlobalContext);

    const isUnderMaintenance = state?.tealDotStatus?.maintenanceStatus?.request;
    if (isUnderMaintenance) {
        return (
            <ModuleMaintenance
                title={REQUESTS_MODULE_UNDER_MAINTENANCE}
                description={REQUESTS_MODULE_UNDER_MAINTENANCE_DESCRIPTION}
                isNestedScreen={true}
                headerTitle={'Request'}
            />
        );
    }

    return <RequestAgainstOfferScreen navigation={navigation} route={route} />;
};

export default RequestAgainstOfferWrapper;
