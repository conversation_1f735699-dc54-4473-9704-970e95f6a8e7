import React, { useEffect } from 'react';
import ClubsScreen from '../screens/map/ClubsScreen';
import AuthProvider from '../context/AuthContext';
import StoreProvider from '../context/StoreContext';
import LocationProvider from '../context/LocationContext';
import PersonalProfileScreen from '../screens/profile/PersonalProfileScreen';
import SideNavigation from '../components/layout/SideNavigation';
import MyThousandGreensScreen from '../screens/profile/MyThousandGreensScreen';
import RequestTokenAdjustmentScreen from '../screens/profile/RequestTokenAdjustmentScreen';
import CreatePostScreen from '../screens/feed/CreatePostScreen';
import EditPostScreen from '../screens/feed/EditPostScreen';
import PostDetailsScreen from '../screens/feed/PostDetailsScreen';
import EditRequestScreen from '../screens/play/EditRequestScreen';
import CreateOfferScreen from '../screens/play/CreateOfferScreen';
import EditOfferScreen from '../screens/play/EditOfferScreen';
import NotificationsContainer from '../components/layout/notifications/NotificationsContainer';
import GameScreen from '../screens/play/GameScreen';
import ImageShow from '../components/layout/forum/ImageShow';
import RequestAgainstOfferScreen from '../screens/play/RequestAgainstOfferScreen';
import AnnualConfirmationScreen from '../screens/annual-confirmation/AnnualConfirmationScreen';
import ConfirmClubsScreen from '../screens/annual-confirmation/ConfirmClubsScreen';
import ConfirmProfileScreen from '../screens/annual-confirmation/ConfirmProfileScreen';
import ThankYouScreen from '../screens/annual-confirmation/ThankYouScreen';
import EventScreen from '../screens/events/EventScreen';
import BenefitScreen from '../screens/benefits/BenefitScreen';
import Requests from '../screens/requests';
import RequestDetail from '../screens/requests/RequestDetail';
import CreateRequest from '../screens/requests/CreateRequest';
import ChatScreen from '../screens/chat/ChatScreen';
//import MyGolfClubs from '../screens/profile/MyGolfClubs';
import MyGolfClubs from '../screens/myGolfClub';
import GolfClubDetails from '../screens/myGolfClub/view/ClubDetails';
import ClubMemberDetails from '../screens/myGolfClub/view/ClubMember';
import EditMyClub from '../screens/myGolfClub/view/EditClub';
import AccountSettingsScreen from '../screens/profile/AccountSettingsScreen';
import FAQ from '../screens/FAQ';
import EventDetails from '../screens/events/EventDetails';
import BenefitDetails from '../screens/benefits/BenefitDetails';
import SearchFavClub from '../screens/favourite/FavSearch';
import PegboardScreen from '../screens/Pegboard/PegboardScreen';
import PegboardClubList from '../screens/Pegboard/PegboardListDetails';
import AddClub from '../screens/myGolfClub/addClub';
import MyContacts from '../screens/myContacts';
import AddContact from '../screens/myContacts/view/AddContact';
import EditContact from '../screens/myContacts/view/EditContact';
import LifeTimeHistory from '../screens/profile/LifeTimeHistory';
import FCMPopup from '../screens/auth/login/fcmPopup/view/FCMPopup';
import BlockerScreen from '../screens/register/BlockerScreen';
import RequestDetail1 from '../screens/requests/RequestDetail1';
import FreeAccessPopup from '../screens/auth/login/freeAccess/view/FreeAccessPopup';
import SuperHostPopup from '../screens/auth/login/superHost/view/SuperHostPopup';
import ReplaceClub from '../screens/myGolfClub/replaceClub/view/ReplaceClub';
import MyTGFriendsScreen from '../screens/my-TG-friends/view/MyTGFriendsScreen';
import StreamChatProvider from '../context/StreamChatContext';
import TGChat from '../screens/my-TG-Stream-Chat/view/TGChat';
import MessageScreen from '../screens/my-TG-Stream-Chat/view/MessageScreen';
import CreateNewChat from '../screens/my-TG-Stream-Chat/newChat/view/CreateNewChat';
import CreateNewGroupChat from '../screens/my-TG-Stream-Chat/newGroupChat/view/CreateNewGroupChat';
import GroupChatForm from '../screens/my-TG-Stream-Chat/newGroupChat/view/GroupChatForm';
import GroupInfo from '../screens/my-TG-Stream-Chat/groupInfo/view/GroupInfo';
import AllMembersListPreview from '../screens/my-TG-Stream-Chat/groupInfo/view/AllMembersListPreview';
import NetworkStatusProvider from '../context/NetworkStatusProvider';
import { GlobalProvider } from '../context/contextApi';
import BlockedChannelScreen from '../screens/my-TG-Stream-Chat/blockedChannel/view/BlockedChannelScreen';
import {
    UserProfileScreen,
    GroupInfoUserProfileScreen,
    ShowUserDp,
} from '../screens/my-TG-Stream-Chat/userProfile/view/index';
import ForwardMessage from '../screens/my-TG-Stream-Chat/forwardMessage/view/ForwardMessage';
import AddParticipantsScreen from '../screens/my-TG-Stream-Chat/groupInfo/view/AddParticipantsScreen';
import PlayedFriendScreen from '../screens/play/played-friend screen';
import GroupScreen from '../screens/myTgGroup/groups/view/Group';
import config from '../config';
import CreateGroupScreeen from '../screens/myTgGroup/createGroup/view/CreateGroupScreen';
import MyTgGroupInfo from '../screens/myTgGroup/MyTgGroupInfo/view/MyTgGroupInfo';
import MyTgGroupList from '../screens/myTgGroup/myTgGroups/view/MyGroupsList';
import AllGroupMemberList from '../screens/myTgGroup/MyTgGroupInfo/view/AllGroupMemberList';
import AccountDeleteFirstScreen from '../forms/profile/AccountDeleteFirstScreen';
import AccountDeleteSecondScreen from '../forms/profile/AccountDeleteSecondScreen';
import FreeAndSuperHostUser from '../screens/freeanduperhostuser/view/FreeAndSuperHostUser';
import { FREE_OLD_USER, SUPER_HOST } from '../screens/my-TG-Stream-Chat/client';
import OpenImageScreen from '../screens/my-TG-Stream-Chat/view/showMessageImages/OpenImageScreen';
import OpenVideoScreen from '../screens/my-TG-Stream-Chat/view/showMessageImages/OpenVideoScreen';
import VideoDetailScreen from '../screens/my-TG-Stream-Chat/view/showMessageImages/VideoDetailScreen';
import ViewAllGroups from '../screens/myTgGroup/viewAllGroup/view/ViewAllGroups';
import PendingMemberRequest from '../screens/myTgGroup/pendingMemberRequest/view/PendingMemberRequest';
import ReceivedRequest from '../screens/requests/ReceivedRequest';
import RequestedRequest from '../screens/requests/RequestedRequest';
import NotificationPreferences from '../screens/notificationPreferences/view/NotificationPreferences';
import ClubSettings from '../screens/myTgGroup/myTgsettings/view/ClubSettings';
import MyFriendInClubScreen from '../screens/play/my-friend-in-club screen/MyFriendInClubScreen';
import TgGroupMemberScreen from '../screens/play/my-tg-group-members screen/TgGroupMemberScreen';
import FavouriteClubListScreen from '../screens/myGolfClub/view/FavouriteClubListScreen';
import CreateRequestMap from '../screens/play/my-tg-group-members screen/CreateRequestMap';
import MyTGBottomTab from './MyTGNavigation';
import ClubsBottomTab from './ClubsNavigation';
import FriendsCategoryNav from '../screens/my-TG-friends/view/FriendsCategoryNav';
import OfferAndEventList from '../screens/my-TG-Stream-Chat/offersAndEvents/view/OfferAndEventList.';
import CreateNewEvents from '../screens/events/CreateNewEvents';
import CreatePegboardScreen from '../screens/Pegboard/CreatePegboardScreen';
import PegboardAddClub from '../screens/Pegboard/PegboardAddClub';
import EditAndAddClubScreen from '../screens/Pegboard/EditAndAddClubScreen';
import LeaderboardScreen from '../screens/Pegboard/LeaderboardScreen';
import FriendsPlayedInClubScreen from '../screens/Pegboard/FriendsPlayedInClub';
import GroupTopNav from '../screens/myTgGroup/groups/view/GroupTopNav';
import ViewAllSuggestedGroup from '../screens/myTgGroup/suggestedGroup/ViewAllSuggestedGroup';
import ClubVisibleInGroup from '../forms/profile/views/ClubVisibleInGroup';
import RequestedRequestDetails from '../screens/requests/RequestedRequestDetails';
import RequestFAQ from '../screens/requests/RequestFAQ';
import AddressForm from '../screens/Address/AddressForm';
import ClubValidationScreen from '../screens/club-validation/views/ClubValidationScreen';
import ClubDetailsScreen from '../screens/club-validation/views/ClubDetailsScreen';
import ClubQuestionScreen from '../screens/club-validation/views/ClubQuestionScreen';
import ClubThankYouScreen from '../screens/club-validation/views/ClubThankYouScreen';
import TGReferralScreen from '../screens/referral/view/TGReferralScreen';
import ReferralForm from '../screens/referral/view/ReferralForm';
import ReferralList from '../screens/referral/view/ReferralList';
import constants from '../utils/constants/constants';
import RecommendedClubScreen from '../screens/requests/RecommendedClubScreen';
import setClevertapUser from '../utils/clevertap/setUserProfile';
import RequestChatScreen from '../screens/requests/stream-chat/RequestChatScreen';
import ShareSheetScreen from '../screens/my-TG-Stream-Chat/view/sharesheet/shareSheetScreen';
import RecentChatPopup from '../screens/my-TG-Stream-Chat/TGChatComponent/RecentChatPopup';
import LongPressPopup from '../screens/my-TG-Stream-Chat/TGChatComponent/LongPressPopup';
import DeleteChannelConfirmationPopup from '../screens/my-TG-Stream-Chat/view/DeleteChannelConfirmationPopup';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import OfferRequestPopupScreen from '../screens/my-TG-Stream-Chat/offersAndEvents/view/OfferRequestPopupScreen';
import CustomSelector from '../screens/Address/CustomSelector';
import AttachmentPopup from '../screens/my-TG-Stream-Chat/TGChatComponent/AttachmentPopup';
import TripleDotPopupScreen from '../screens/my-TG-Stream-Chat/TGChatComponent/TrippleDotPopupScreen';
import DeleteConfirmationPopupScreen from '../screens/Pegboard/DeleteConfirmationPopupScreen';
import AddNewClubPopupScreen from '../screens/Pegboard/AddNewClubPopupScreen';
import CreatePoll from '../screens/my-TG-Stream-Chat/polls/CreatePoll';
import PollResults from '../screens/my-TG-Stream-Chat/polls/PollResults';
import PollComment from '../components/poll/PollComment';
import BottomTabNavigation from './BottomTabNavigation';
import Setting from '../screens/setting/view/Setting';
import MuteClubPopupScreen from '../components/homeScreenComponent/MuteClubPopupScreen';
import LogPlayerGameDetails from '../screens/logofflineGame/view/OfflineLogStep3';
import RequestConfirmScreen from '../screens/requests/view/RequestConfirmScreen';
import RequestHistoryScreen from '../screens/requests/view/history/view/RequestHistoryScreen';
import LogFinalPopup from '../screens/logofflineGame/view/LogFinalPopup';
import WarningScreen from '../screens/logofflineGame/view/WarningScreen';
import MapDetails from '../screens/map/view/MapDetails';
import GameReview from '../screens/map/view/gameReview/GameReview';
import OfferScreen from '../screens/offers/view/OfferScreen';
import OfferDetails from '../screens/offers/view/OfferDetails';
import UsersAllGameReviewScreen from '../screens/my-TG-Stream-Chat/userProfile/view/UsersAllGameReviewScreen';
import RequestDetailWrapper from './RequestDetailWrapper';
import CreateRequestWrapper from './CreateRequestWrapper';
import CreateRequestToAllWrapper from './CreateRequestToAllWrapper';
import RequestAgainstOfferWrapper from './RequestAgainstOfferWrapper';
import RequestChatDetailWrapper from './RequestChatDetailWrapper';

const Stack = createNativeStackNavigator();

const {
    MY_TG_GROUPLIST,
    CREATE_GROUP_SCREEEN,
    MY_TG_GROUP_INFO,
    ALL_GROUP_MEMBERLIST,
    MY_TG_CLUB_SETTINGS,
    CLUB_VALIDATION_SCREEN,
} = config.routes;

export default function MainStack({
    user,
    token,
    refreshUser,
    refreshToken,
    navigationRef,
    needsAnnualConfirmation,
    deferAnnualConfirmation,
    setDeferAnnualConfirmation,
    duesState,
    client,
    shareSheetImageUrl,
}) {
    useEffect(() => {
        setClevertapUser(user);
    }, [user]);

    return (
        <AuthProvider
            user={{
                user,
                token,
                refreshUser,
                refreshToken,
                needsAnnualConfirmation,
                deferAnnualConfirmation,
                setDeferAnnualConfirmation,
            }}>
            <GlobalProvider>
                <StreamChatProvider
                    user={user}
                    client={client}
                    shareSheetImageUrl={shareSheetImageUrl}
                    navigationRef={navigationRef}>
                    <NetworkStatusProvider>
                        <LocationProvider>
                            <StoreProvider user={user}>
                                <SideNavigation navigationRef={navigationRef} user={user}>
                                    <NotificationsContainer user={user} navigationRef={navigationRef}>
                                        <Stack.Navigator
                                            // initialRouteName="Feed"
                                            screenOptions={{
                                                headerShown: false,
                                            }}>
                                            {/*This condition is used to navigate user to the accountSettings after the user post renewal */}
                                            {!user?.visited_account_settings &&
                                                user?.profile_complete &&
                                                user?.membership_active && (
                                                    <Stack.Screen
                                                        name="AccountSettings"
                                                        component={AccountSettingsScreen}
                                                    />
                                                )}

                                            {user?.membership_active &&
                                                user?.clubs?.length === 1 &&
                                                user?.clubs[0]?.is_replaced &&
                                                user?.yearly_club_validation ==
                                                    constants.YEARLY_VALIDATION.VERIFICATION_REQUIRED && (
                                                    <Stack.Screen
                                                        name={config.routes.CLUB_THANK_YOU_SCREEN}
                                                        component={ClubThankYouScreen}
                                                    />
                                                )}

                                            {user?.membership_active &&
                                                user?.yearly_club_validation ===
                                                    constants.YEARLY_VALIDATION.VERIFICATION_REQUIRED &&
                                                !(user?.clubs?.length === 1 && user?.clubs[0]?.is_replaced) && (
                                                    <Stack.Screen
                                                        name={CLUB_VALIDATION_SCREEN}
                                                        component={ClubValidationScreen}
                                                    />
                                                )}
                                            {/* this condition is used for those user who have previously any plan and now
                                                    they got a new plan, for that scenario we introduce this condition to show user have free access or super host
                                                    */}
                                            {user?.membership_expires_on &&
                                                !user?.membership_active &&
                                                (duesState?.planToAssign?.key === SUPER_HOST ||
                                                    duesState?.planToAssign?.key === FREE_OLD_USER) && (
                                                    <Stack.Screen
                                                        name="FreeAndSuperHostUser"
                                                        component={FreeAndSuperHostUser}
                                                    />
                                                )}
                                            {user?.is_tg_founder && user?.signup_current_step !== 8 && (
                                                <Stack.Screen name="FCMPopup" component={FCMPopup} />
                                            )}
                                            {user?.is_tg_ambassador &&
                                                (user?.stripe_customer_info?.address?.postal_code === '' ||
                                                    user?.stripe_customer_info === null) && (
                                                    <Stack.Screen name="AddressForm" component={AddressForm} />
                                                )}

                                            {!user?.membership_active &&
                                                duesState?.planToAssign?.key === 'FREE_OLD_USER' &&
                                                !duesState?.membershipInfo?.isPostMembershipUser && (
                                                    <Stack.Screen name="FreeAccessPopup" component={FreeAccessPopup} />
                                                )}
                                            {!user?.membership_active &&
                                                duesState?.planToAssign?.key === 'FREE_OLD_USER' &&
                                                duesState?.membershipInfo?.isPostMembershipUser && (
                                                    <Stack.Screen name="BlockerScreen" component={BlockerScreen} />
                                                )}
                                            {!user?.membership_active &&
                                                duesState?.planToAssign?.key === 'SUPER_HOST' && (
                                                    <Stack.Screen name="SuperHostPopup" component={SuperHostPopup} />
                                                )}
                                            {!user?.membership_active &&
                                                duesState?.planToAssign?.key !== 'FREE_OLD_USER' &&
                                                duesState?.planToAssign?.key !== 'SUPER_HOST' && (
                                                    <Stack.Screen name="BlockerScreen" component={BlockerScreen} />
                                                )}
                                            <Stack.Screen
                                                name={config.routes.BOTTOM_TAB_NAVIGATION}
                                                component={BottomTabNavigation}
                                                initialParams={{ mainTab: 0 }}
                                            />
                                            <Stack.Screen
                                                name={'ClubsScreen'}
                                                component={ClubsScreen}
                                                initialParams={{ mainTab: 0 }}
                                            />
                                            <Stack.Screen
                                                name={config.routes.MY_TG}
                                                component={MyTGBottomTab}
                                                initialParams={{ mainTab: 0 }}
                                            />
                                            <Stack.Screen name="Create Post" component={CreatePostScreen} />
                                            <Stack.Screen name="Edit Post" component={EditPostScreen} />
                                            <Stack.Screen name="Post Details" component={PostDetailsScreen} />
                                            <Stack.Screen name="Clubs" component={ClubsBottomTab} />
                                            <Stack.Screen name="Settings" component={Setting} />
                                            <Stack.Screen name={config.routes.SETTING} component={Setting} />
                                            <Stack.Screen
                                                name="My Thousand Greens"
                                                component={MyThousandGreensScreen}
                                            />

                                            <Stack.Screen
                                                name="Request Token Adjustment"
                                                component={RequestTokenAdjustmentScreen}
                                            />
                                            <Stack.Screen name="Personal Profile" component={PersonalProfileScreen} />
                                            <Stack.Screen name="Account Settings" component={AccountSettingsScreen} />
                                            <Stack.Screen name="My Referrals" component={TGReferralScreen} />
                                            <Stack.Screen
                                                name={config.routes?.REFERRAL_FORM}
                                                component={ReferralForm}
                                            />
                                            <Stack.Screen
                                                name={config.routes?.REFERRAL_LIST}
                                                component={ReferralList}
                                            />
                                            <Stack.Screen name="Events" component={EventScreen} />
                                            <Stack.Screen name="Event Details" component={EventDetails} />
                                            <Stack.Screen name="Benefits" component={BenefitScreen} />
                                            <Stack.Screen name="Benefit Details" component={BenefitDetails} />
                                            <Stack.Screen name="Requests" component={Requests} />
                                            <Stack.Screen name="Create Request" component={CreateRequestWrapper} />
                                            <Stack.Screen name="ChatDetails" component={RequestChatDetailWrapper} />

                                            <Stack.Screen name="Edit Request" component={EditRequestScreen} />
                                            <Stack.Screen name="Offers" component={OfferScreen} />
                                            <Stack.Screen name="Create Offer" component={CreateOfferScreen} />
                                            <Stack.Screen name="Edit Offer" component={EditOfferScreen} />
                                            <Stack.Screen
                                                name="Request Against Offer"
                                                component={RequestAgainstOfferWrapper}
                                            />
                                            <Stack.Screen name="Game Details" component={GameScreen} />
                                            <Stack.Screen name="Request Details" component={RequestDetail} />
                                            <Stack.Screen name="Request Details1" component={RequestDetail1} />
                                            <Stack.Screen
                                                name={config.routes.REQUESTED_REQUEST_DETAILS}
                                                component={RequestedRequestDetails}
                                            />

                                            <Stack.Screen name="CreateNewGroupChat" component={CreateNewGroupChat} />
                                            <Stack.Screen name="GroupChatForm" component={GroupChatForm} />
                                            <Stack.Screen name="GroupInfo" component={GroupInfo} />
                                            <Stack.Screen
                                                name="AllMembersListPreview"
                                                component={AllMembersListPreview}
                                            />
                                            <Stack.Screen
                                                name="Annual Confirmation"
                                                component={AnnualConfirmationScreen}
                                            />
                                            <Stack.Screen name="Confirm Clubs" component={ConfirmClubsScreen} />
                                            <Stack.Screen name="My Golf Clubs" component={MyGolfClubs} />
                                            <Stack.Screen
                                                name="RecentChatPopup"
                                                component={RecentChatPopup}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen
                                                name="LongPressPopup"
                                                component={LongPressPopup}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen
                                                name="DeleteChannelConfirmationPopup"
                                                component={DeleteChannelConfirmationPopup}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen name="Golf Club Details" component={GolfClubDetails} />
                                            <Stack.Screen name="Confirm Profile" component={ConfirmProfileScreen} />
                                            <Stack.Screen name="FAQ" component={FAQ} />
                                            <Stack.Screen name="PlayedFriendScreen" component={PlayedFriendScreen} />
                                            <Stack.Screen name="Thank You" component={ThankYouScreen} />
                                            <Stack.Screen name="Search Favourit Clubs" component={SearchFavClub} />
                                            <Stack.Screen name="Pegboard" component={PegboardScreen} />
                                            <Stack.Screen name="PegboardClubList" component={PegboardClubList} />
                                            <Stack.Screen
                                                name={config.routes.LEADERBOARD_SCREEN}
                                                component={LeaderboardScreen}
                                            />
                                            <Stack.Screen name="EditMyClub" component={EditMyClub} />
                                            <Stack.Screen name="AddClub" component={AddClub} />
                                            <Stack.Screen name="ReplaceClub" component={ReplaceClub} />
                                            <Stack.Screen name="MyContacts" component={MyContacts} />
                                            <Stack.Screen name="AddContact" component={AddContact} />
                                            <Stack.Screen name="EditContact" component={EditContact} />
                                            <Stack.Screen name="Club Member Details" component={ClubMemberDetails} />

                                            <Stack.Screen name="Image Show" component={ImageShow} />
                                            {/* <Stack.Screen
                                                    name="Landing_new"
                                                    component={LandingScreen}
                                                /> */}
                                            <Stack.Screen name="LifeTimeHistory" component={LifeTimeHistory} />
                                            <Stack.Screen name="MyTGFriendsScreen" component={MyTGFriendsScreen} />
                                            <Stack.Screen name="Chat" component={TGChat} />
                                            <Stack.Screen
                                                name="MessageScreen"
                                                component={MessageScreen}
                                                options={{ gestureEnabled: false }}
                                            />
                                            <Stack.Screen name="CreateNewChat" component={CreateNewChat} />
                                            <Stack.Screen
                                                name="BlockedChannelScreen"
                                                component={BlockedChannelScreen}
                                            />
                                            <Stack.Screen
                                                name="UserProfileScreen"
                                                component={UserProfileScreen}
                                                options={{ gestureEnabled: false }}
                                            />
                                            <Stack.Screen
                                                name="GroupInfoUserProfileScreen"
                                                component={GroupInfoUserProfileScreen}
                                                options={{ gestureEnabled: false }}
                                            />

                                            <Stack.Screen
                                                name={CREATE_GROUP_SCREEEN}
                                                component={CreateGroupScreeen}
                                                options={{ gestureEnabled: false }}
                                            />
                                            <Stack.Screen
                                                name={MY_TG_GROUP_INFO}
                                                component={MyTgGroupInfo}
                                                options={{ gestureEnabled: false }}
                                            />
                                            <Stack.Screen
                                                name={MY_TG_GROUPLIST}
                                                component={MyTgGroupList}
                                                options={{ gestureEnabled: false }}
                                            />
                                            <Stack.Screen
                                                name={ALL_GROUP_MEMBERLIST}
                                                component={AllGroupMemberList}
                                                options={{ gestureEnabled: false }}
                                            />
                                            <Stack.Screen
                                                name={MY_TG_CLUB_SETTINGS}
                                                component={ClubSettings}
                                                options={{ gestureEnabled: false }}
                                            />

                                            {/* <Stack.Screen
                                                    name="Chat"
                                                    component={StreamChatNavigation}
                                                /> */}
                                            <Stack.Screen name="ShowUserDp" component={ShowUserDp} />
                                            <Stack.Screen name="ForwardMessage" component={ForwardMessage} />
                                            <Stack.Screen
                                                name="AddParticipantsScreen"
                                                component={AddParticipantsScreen}
                                            />
                                            <Stack.Screen name={config.routes.GROUPS} component={GroupScreen} />
                                            <Stack.Screen
                                                name={config.routes.ACCOUNT_DELETE_FIRST_SCREEN}
                                                component={AccountDeleteFirstScreen}
                                            />
                                            <Stack.Screen
                                                name={config.routes.ACCOUNT_DELETE_SECOND_SCREEN}
                                                component={AccountDeleteSecondScreen}
                                            />
                                            <Stack.Screen name="OpenImageScreen" component={OpenImageScreen} />

                                            <Stack.Screen name="OpenVideoScreen" component={OpenVideoScreen} />
                                            {/* <Stack.Screen
                                                    name="Landing"
                                                    component={LandingScreen}
                                                    options={{
                                                        gestureEnabled: false,
                                                    }}
                                                /> */}

                                            <Stack.Screen name="VideoDetailScreen" component={VideoDetailScreen} />
                                            <Stack.Screen
                                                name={config.routes.VIEW_ALL_GROUPS}
                                                component={ViewAllGroups}
                                            />
                                            <Stack.Screen
                                                name={config.routes.PENDING_MEMBER_REQUEST}
                                                component={PendingMemberRequest}
                                            />
                                            <Stack.Screen
                                                name={config.routes.RECEIVED_REQUEST}
                                                component={ReceivedRequest}
                                            />
                                            <Stack.Screen
                                                name={config.routes.REQUESTED_REQUEST}
                                                component={RequestedRequest}
                                            />
                                            <Stack.Screen
                                                name={config.routes.NOTIFICATION_PREFERENCES}
                                                component={NotificationPreferences}
                                            />
                                            <Stack.Screen
                                                name={config.routes.MY_FRIEND_IN_CLUB}
                                                component={MyFriendInClubScreen}
                                            />
                                            <Stack.Screen
                                                name={config.routes.TG_GROUP_MEMBERS_SCREEN}
                                                component={TgGroupMemberScreen}
                                            />
                                            <Stack.Screen
                                                name={config.routes.FAVOURITE_CLUB_LIST_SCREEN}
                                                component={FavouriteClubListScreen}
                                            />
                                            <Stack.Screen
                                                name={config.routes.CREATE_REQUEST_MAP}
                                                component={CreateRequestToAllWrapper}
                                            />
                                            <Stack.Screen
                                                name={config.routes.SHARE_SHEET_SCREEN}
                                                component={ShareSheetScreen}
                                            />
                                            <Stack.Screen
                                                name={config.routes.FRIENDS_CATEGORY_NAV}
                                                component={FriendsCategoryNav}
                                            />
                                            <Stack.Screen
                                                name={config.routes.OFFER_AND_EVENT_LIST}
                                                component={OfferAndEventList}
                                            />
                                            <Stack.Screen
                                                name={config.routes.CREATE_NEW_EVENTS}
                                                component={CreateNewEvents}
                                            />
                                            <Stack.Screen
                                                name={config.routes.CREATE_PEGBOARD_SCREEN}
                                                component={CreatePegboardScreen}
                                            />
                                            <Stack.Screen
                                                name={config.routes.FRIENDS_PLAYED_IN_CLUB_SCREEN}
                                                component={FriendsPlayedInClubScreen}
                                            />
                                            <Stack.Screen
                                                name={config.routes.EDIT_ADD_CLUB_SCREEN}
                                                component={EditAndAddClubScreen}
                                            />
                                            <Stack.Screen
                                                name={config.routes.PEGBOARD_ADD_CLUB}
                                                component={PegboardAddClub}
                                            />
                                            <Stack.Screen name={config.routes.GROUP_TOP_NAV} component={GroupTopNav} />
                                            <Stack.Screen
                                                name={config.routes.VIEW_ALL_SUGGESTED_GROUP}
                                                component={ViewAllSuggestedGroup}
                                            />
                                            <Stack.Screen
                                                name={config.routes.CLUB_VISIBLE_IN_GROUP}
                                                component={ClubVisibleInGroup}
                                            />
                                            <Stack.Screen name={config.routes.REQUEST_FAQ} component={RequestFAQ} />
                                            <Stack.Screen
                                                name={config.routes.CLUB_QUESTION_SCREEN}
                                                component={ClubQuestionScreen}
                                            />
                                            <Stack.Screen
                                                name={config.routes.CLUB_DETAILS_SCREEN}
                                                component={ClubDetailsScreen}
                                            />
                                            <Stack.Screen
                                                name={config.routes.CLUB_THANK_YOU_SCREEN_OPEN}
                                                component={ClubThankYouScreen}
                                            />
                                            <Stack.Screen
                                                name={config.routes.RECOMMENDED_CLUB}
                                                component={RecommendedClubScreen}
                                            />
                                            <Stack.Screen
                                                name={config.routes.REQUEST_CHAT_SCREEN}
                                                component={RequestChatScreen}
                                            />
                                            <Stack.Screen name={config.routes.CREATE_POLL} component={CreatePoll} />
                                            <Stack.Screen name={config.routes.POLL_RESULTS} component={PollResults} />
                                            <Stack.Screen
                                                name={'TripleDotPopupScreen'}
                                                component={TripleDotPopupScreen}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen
                                                name={'DeleteConfirmationPopupScreen'}
                                                component={DeleteConfirmationPopupScreen}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen
                                                name={'AddNewClubPopupScreen'}
                                                component={AddNewClubPopupScreen}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen
                                                name={config.routes.OFFER_REQUEST_POPUP_SCREEN}
                                                component={OfferRequestPopupScreen}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen
                                                name={config.routes.CUSTOM_SELECTOR}
                                                component={CustomSelector}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen
                                                name={config.routes.ATTACHMENT_POPUP}
                                                component={AttachmentPopup}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen
                                                name={config.routes.POLL_COMMENT}
                                                component={PollComment}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen
                                                name={config.routes.MUTE_CLUB_POPUP_SCREEN}
                                                component={MuteClubPopupScreen}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen
                                                name={config.routes.REQUEST_CONFIRM_SCREEN}
                                                component={RequestConfirmScreen}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen
                                                name={config.routes.LOG_PLAYER_GAME_DETAILS}
                                                component={LogPlayerGameDetails}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen
                                                name={config.routes.LOG_FINAL_POPUP}
                                                component={LogFinalPopup}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen
                                                name={config.routes.WARNING_SCREEN}
                                                component={WarningScreen}
                                                options={{
                                                    presentation: 'transparentModal',
                                                    animationTypeForReplace: 'push', // or 'pop'
                                                    animation: 'fade',
                                                }}
                                            />
                                            <Stack.Screen name="EditAddressForm" component={AddressForm} />
                                            <Stack.Screen
                                                name={config.routes.REQUEST_DETAIL_SCREEN}
                                                component={RequestDetailWrapper}
                                            />
                                            <Stack.Screen
                                                name={config.routes.REQUEST_HISTORY_SCREEN}
                                                component={RequestHistoryScreen}
                                            />
                                            <Stack.Screen name={config.routes.GAME_REVIEW} component={GameReview} />
                                            <Stack.Screen name="MapDetails" component={MapDetails} />
                                            <Stack.Screen name={config.routes.OFFER_DETAILS} component={OfferDetails} />
                                            <Stack.Screen
                                                name={config.routes.USERS_ALL_GAME_REVIEW_SCREEN}
                                                component={UsersAllGameReviewScreen}
                                            />
                                        </Stack.Navigator>
                                    </NotificationsContainer>
                                </SideNavigation>
                            </StoreProvider>
                        </LocationProvider>
                    </NetworkStatusProvider>
                </StreamChatProvider>
            </GlobalProvider>
        </AuthProvider>
    );
}
