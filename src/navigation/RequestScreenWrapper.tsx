import { StyleSheet } from 'react-native'
import React, { useContext } from 'react'
import { NativeStackScreenProps } from '@react-navigation/native-stack';

import { REQUESTS_MODULE_UNDER_MAINTENANCE_DESCRIPTION } from '../utils/constants/strings';
import { GlobalContext } from '../context/contextApi';
import ModuleMaintenance from '../screens/moduleMaintainence/ModuleMaintenance';
import { REQUESTS_MODULE_UNDER_MAINTENANCE } from '../utils/constants/strings';
import RequestScreen from '../screens/requests/view/RequestScreen';
import { RootStackParamList } from '../interface/type';

type RequestScreenWrapperProps = NativeStackScreenProps<RootStackParamList, 'RequestScreen'>;

const RequestScreenWrapper = ({ navigation, route }: RequestScreenWrapperProps) => {
    const {state} = useContext(GlobalContext)
  
    const isUnderMaintenance = state?.tealDotStatus?.maintenanceStatus?.request;
  
    if (isUnderMaintenance) {
      return (
        <ModuleMaintenance
          title={REQUESTS_MODULE_UNDER_MAINTENANCE}
          description={REQUESTS_MODULE_UNDER_MAINTENANCE_DESCRIPTION}
          headerTitle={'Request'}
        />
      );
    }
  
    return <RequestScreen navigation={navigation} route={route} />;
  };
  

export default RequestScreenWrapper

const styles = StyleSheet.create({})