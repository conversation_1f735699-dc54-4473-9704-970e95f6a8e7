import { GET_GAME_REVIEW_BY_USER } from '../EndPoint';
import { fetcher } from '../fetcher';

interface GetGolfersGameReviewBody {
    userId: string;
    golferId: string;
    page: number;
    limit: number;
}

export const getGolfersGameReview = async (body: GetGolfersGameReviewBody) => {
    return fetcher({
        endpoint: GET_GAME_REVIEW_BY_USER,
        method: 'POST',
        body: body,
    }).then((res) => {
        return res;
    });
};
