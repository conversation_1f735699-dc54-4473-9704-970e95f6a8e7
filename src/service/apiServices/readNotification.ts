import { READ_NOTIFICATION } from '../EndPoint';
import { fetcher } from '../fetcher';

export const readNotifications = async (userId: string, notificationId?: string) => {
    const body: { userId: string; notificationId?: string; isMobile: boolean } = {
        userId: userId,
        isMobile: true,
    };
    if (notificationId) {
        body.notificationId = notificationId;
    }
    return await fetcher({
        endpoint: READ_NOTIFICATION,
        method: 'POST',
        body: body,
    }).then((response) => {
        return response;
    });
};
