import { allNotificationsV3 } from "../EndPoint";
import { fetcher } from "../fetcher";

export const getPlayingCardNotifications = async (userId: string) => {
    return await fetcher({
        endpoint: allNotificationsV3,
        method: 'POST',
        body: {
            userId: userId,
            lastCreatedAt: null,
            limit: 100,
            isRead: false,
        },
    }).then((response) => {
        return response;
    });
};