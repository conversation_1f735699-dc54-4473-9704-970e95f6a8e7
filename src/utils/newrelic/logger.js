import NewRelic from 'newrelic-react-native-agent';

/**
 * New Relic Logger Utility
 * Provides methods to send logs, errors, and custom events to New Relic
 */

class NewRelicLogger {
    /**
     * Log an error to New Relic
     * @param {string} name - Error name/title
     * @param {Error|string} error - Error object or message
     * @param {Object} attributes - Additional attributes
     */
    static logError(name, error, attributes = {}) {
        try {
            const errorData = {
                level: 'ERROR',
                timestamp: new Date().toISOString(),
                ...attributes
            };

            if (error instanceof Error) {
                errorData.message = error.message;
                errorData.stack = error.stack;
                errorData.name = error.name;
            } else {
                errorData.message = String(error);
            }

            NewRelic.recordError(name, errorData);
            
            // Also log as custom event for better visibility
            NewRelic.recordCustomEvent('AppError', {
                errorName: name,
                errorMessage: errorData.message,
                ...attributes
            });
        } catch (e) {
            console.error('Failed to log error to New Relic:', e);
        }
    }

    /**
     * Log a warning to New Relic
     * @param {string} name - Warning name/title
     * @param {string} message - Warning message
     * @param {Object} attributes - Additional attributes
     */
    static logWarning(name, message, attributes = {}) {
        try {
            const warningData = {
                level: 'WARNING',
                message: String(message),
                timestamp: new Date().toISOString(),
                ...attributes
            };

            NewRelic.recordError(name, warningData);
            
            // Also log as custom event
            NewRelic.recordCustomEvent('AppWarning', {
                warningName: name,
                warningMessage: message,
                ...attributes
            });
        } catch (e) {
            console.error('Failed to log warning to New Relic:', e);
        }
    }

    /**
     * Log an info message to New Relic
     * @param {string} name - Info name/title
     * @param {string} message - Info message
     * @param {Object} attributes - Additional attributes
     */
    static logInfo(name, message, attributes = {}) {
        try {
            NewRelic.recordCustomEvent('AppInfo', {
                infoName: name,
                infoMessage: String(message),
                level: 'INFO',
                timestamp: new Date().toISOString(),
                ...attributes
            });
        } catch (e) {
            console.error('Failed to log info to New Relic:', e);
        }
    }

    /**
     * Log a custom event to New Relic
     * @param {string} eventName - Event name
     * @param {Object} attributes - Event attributes
     */
    static logCustomEvent(eventName, attributes = {}) {
        try {
            NewRelic.recordCustomEvent(eventName, {
                timestamp: new Date().toISOString(),
                ...attributes
            });
        } catch (e) {
            console.error('Failed to log custom event to New Relic:', e);
        }
    }

    /**
     * Log API errors specifically
     * @param {string} endpoint - API endpoint
     * @param {Error|Object} error - Error object
     * @param {Object} requestData - Request data (optional)
     */
    static logAPIError(endpoint, error, requestData = {}) {
        try {
            const attributes = {
                endpoint,
                level: 'ERROR',
                category: 'API_ERROR',
                timestamp: new Date().toISOString(),
                ...requestData
            };

            if (error.response) {
                attributes.statusCode = error.response.status;
                attributes.statusText = error.response.statusText;
                attributes.responseData = JSON.stringify(error.response.data);
            }

            if (error.message) {
                attributes.message = error.message;
            }

            NewRelic.recordError('API Error', attributes);
            
            NewRelic.recordCustomEvent('APIError', {
                endpoint,
                errorMessage: error.message || 'Unknown API error',
                statusCode: error.response?.status,
                ...attributes
            });
        } catch (e) {
            console.error('Failed to log API error to New Relic:', e);
        }
    }

    /**
     * Log navigation events
     * @param {string} screenName - Screen name
     * @param {Object} params - Navigation parameters
     */
    static logNavigation(screenName, params = {}) {
        try {
            NewRelic.recordCustomEvent('Navigation', {
                screenName,
                timestamp: new Date().toISOString(),
                ...params
            });
        } catch (e) {
            console.error('Failed to log navigation to New Relic:', e);
        }
    }

    /**
     * Log user actions
     * @param {string} action - Action name
     * @param {Object} details - Action details
     */
    static logUserAction(action, details = {}) {
        try {
            NewRelic.recordCustomEvent('UserAction', {
                action,
                timestamp: new Date().toISOString(),
                ...details
            });
        } catch (e) {
            console.error('Failed to log user action to New Relic:', e);
        }
    }

    /**
     * Set user attributes for the session
     * @param {Object} userAttributes - User attributes
     */
    static setUserAttributes(userAttributes) {
        try {
            Object.keys(userAttributes).forEach(key => {
                NewRelic.setAttribute(key, userAttributes[key]);
            });
        } catch (e) {
            console.error('Failed to set user attributes in New Relic:', e);
        }
    }
}

export default NewRelicLogger;
