import NewRelicLogger from './logger';

/**
 * Test functions to verify New Relic logging is working
 * Use these functions to test if errors and logs are being sent to New Relic
 */

export const testNewRelicLogging = () => {
    console.log('Testing New Relic logging...');
    
    // Test 1: Log a test error
    NewRelicLogger.logError('Test Error', new Error('This is a test error for New Relic'), {
        testType: 'manual_test',
        timestamp: new Date().toISOString()
    });
    
    // Test 2: Log a test warning
    NewRelicLogger.logWarning('Test Warning', 'This is a test warning for New Relic', {
        testType: 'manual_test'
    });
    
    // Test 3: Log a test info
    NewRelicLogger.logInfo('Test Info', 'This is a test info message for New Relic', {
        testType: 'manual_test'
    });
    
    // Test 4: Log a custom event
    NewRelicLogger.logCustomEvent('TestEvent', {
        eventType: 'manual_test',
        description: 'Testing custom event logging'
    });
    
    // Test 5: Test console.error (should be captured by global handler)
    console.error('Test console.error - should appear in New Relic logs');
    
    // Test 6: Test console.warn (should be captured by global handler)
    console.warn('Test console.warn - should appear in New Relic logs');
    
    console.log('New Relic logging tests completed. Check your New Relic dashboard in a few minutes.');
};

export const testAPIError = () => {
    // Simulate an API error
    const mockAPIError = {
        message: 'Network request failed',
        response: {
            status: 500,
            statusText: 'Internal Server Error',
            data: { error: 'Database connection failed' }
        }
    };
    
    NewRelicLogger.logAPIError('/api/test-endpoint', mockAPIError, {
        method: 'GET',
        testType: 'manual_test'
    });
    
    console.log('API error test sent to New Relic');
};

export const testUserAction = () => {
    NewRelicLogger.logUserAction('test_button_click', {
        screenName: 'TestScreen',
        buttonId: 'test_button',
        testType: 'manual_test'
    });
    
    console.log('User action test sent to New Relic');
};

// Export all test functions
export default {
    testNewRelicLogging,
    testAPIError,
    testUserAction
};
