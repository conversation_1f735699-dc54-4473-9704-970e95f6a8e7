# New Relic Logging Integration

## Overview
This integration ensures that all errors, warnings, and custom events in your React Native app are properly sent to New Relic for monitoring and debugging.

## What's Been Added

### 1. Enhanced Global Error Handler (`App.tsx`)
- Captures `console.error` and `console.warn` calls
- Sends them to New Relic with proper formatting
- <PERSON>les unhandled promise rejections
- Captures uncaught exceptions

### 2. Updated Error Boundary (`ErrorBoundry.js`)
- Now sends React component errors to both Rollbar and New Relic
- Includes component stack traces

### 3. New Relic Logger Utility (`src/utils/newrelic/logger.js`)
- `NewRelicLogger.logError()` - Log errors with context
- `NewRelicLogger.logWarning()` - Log warnings
- `NewRelicLogger.logInfo()` - Log info messages
- `NewRelicLogger.logAPIError()` - Specifically for API errors
- `NewRelicLogger.logCustomEvent()` - Custom events
- `NewRelicLogger.logNavigation()` - Navigation tracking
- `NewRelicLogger.logUserAction()` - User interaction tracking

### 4. Enhanced Configuration (`index.js`)
- Increased log level to VERBOSE for better capture
- Enabled distributed tracing
- Enabled offline storage
- Enabled background reporting

## Testing the Integration

### Method 1: Use Test Functions
```javascript
import { testNewRelicLogging } from '../utils/newrelic/testLogger';

// In any component or screen
const testLogging = () => {
    testNewRelicLogging();
};

// Add a button to trigger the test
<Button title="Test New Relic Logging" onPress={testLogging} />
```

### Method 2: Manual Testing
```javascript
import NewRelicLogger from '../utils/newrelic/logger';

// Test error logging
NewRelicLogger.logError('Test Error', new Error('This is a test'));

// Test warning logging
NewRelicLogger.logWarning('Test Warning', 'This is a test warning');

// Test console methods (should be automatically captured)
console.error('This error should appear in New Relic');
console.warn('This warning should appear in New Relic');
```

## Where to Find Logs in New Relic

1. **Go to your New Relic dashboard**
2. **Navigate to "Logs"** section
3. **Look for these event types:**
   - `Console Error` - from console.error calls
   - `Console Warning` - from console.warn calls
   - `React Error Boundary` - from React component errors
   - `Unhandled Promise Rejection` - from unhandled promises
   - `AppError` - from NewRelicLogger.logError()
   - `AppWarning` - from NewRelicLogger.logWarning()
   - `APIError` - from API error logging

4. **Check "Events" section for:**
   - Custom events logged via NewRelicLogger
   - User actions and navigation events

## Usage Examples

### Log API Errors
```javascript
import NewRelicLogger from '../utils/newrelic/logger';

try {
    const response = await fetch('/api/data');
} catch (error) {
    NewRelicLogger.logAPIError('/api/data', error, {
        method: 'GET',
        userId: user.id
    });
}
```

### Log User Actions
```javascript
NewRelicLogger.logUserAction('button_click', {
    buttonId: 'submit_form',
    screenName: 'ProfileScreen',
    userId: user.id
});
```

### Log Navigation
```javascript
NewRelicLogger.logNavigation('ProfileScreen', {
    userId: user.id,
    previousScreen: 'HomeScreen'
});
```

## Troubleshooting

### If logs don't appear in New Relic:

1. **Check your app tokens** in `.env` files
2. **Verify network connectivity** - logs are sent over HTTPS
3. **Wait 5-10 minutes** - New Relic has some delay in processing
4. **Check the log level** - ensure it's set to VERBOSE
5. **Test in production build** - some features work better in release mode

### Common Issues:

1. **Development vs Production**: Some logging features work better in production builds
2. **Network Issues**: Logs are queued and sent when network is available
3. **Token Issues**: Verify your New Relic app tokens are correct for each platform

## Integration with Existing Code

The integration is designed to work alongside your existing Rollbar setup. Both systems will receive error reports, giving you redundancy and different perspectives on the same issues.

## Next Steps

1. Test the integration using the provided test functions
2. Gradually replace `console.log` statements with appropriate NewRelicLogger methods
3. Add user action tracking to important user interactions
4. Monitor your New Relic dashboard for the new log entries
