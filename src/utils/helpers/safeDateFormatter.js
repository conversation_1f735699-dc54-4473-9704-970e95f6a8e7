import moment from 'moment';

/**
 * Safely formats a date using moment.js
 * @param {any} date - The date to format
 * @param {string} format - The format string (default: 'Do MMMM YYYY')
 * @param {boolean} isUTC - Whether to use UTC formatting
 * @returns {string} - Formatted date string or empty string if invalid
 */
export const safeFormatDate = (date, format = 'Do MMMM YYYY', isUTC = false) => {
    if (!date || date === null || date === undefined) {
        return '';
    }

    try {
        if (isUTC) {
            return moment.utc(date).format(format);
        } else {
            return moment(date).format(format);
        }
    } catch (error) {
        console.warn('Error formatting date:', error, 'Date:', date);
        return '';
    }
};

/**
 * Safely formats a date range
 * @param {any} startDate - The start date
 * @param {any} endDate - The end date
 * @param {string} format - The format string (default: 'Do MMMM YYYY')
 * @param {boolean} isUTC - Whether to use UTC formatting
 * @returns {string} - Formatted date range string or empty string if invalid
 */
export const safeFormatDateRange = (startDate, endDate, format = 'Do MMMM YYYY', isUTC = false) => {
    if (!startDate || startDate === null || startDate === undefined) {
        return '';
    }

    try {
        const start = safeFormatDate(startDate, format, isUTC);
        
        if (!endDate || endDate === null || endDate === undefined) {
            return start;
        }

        const end = safeFormatDate(endDate, format, isUTC);
        
        if (start === end) {
            return start;
        } else {
            return `${start} - ${end}`;
        }
    } catch (error) {
        console.warn('Error formatting date range:', error);
        return '';
    }
};

/**
 * Safely creates a moment object
 * @param {any} date - The date to convert
 * @param {boolean} isUTC - Whether to use UTC
 * @returns {moment|null} - Moment object or null if invalid
 */
export const safeMoment = (date, isUTC = false) => {
    if (!date || date === null || date === undefined) {
        return null;
    }

    try {
        if (isUTC) {
            return moment.utc(date);
        } else {
            return moment(date);
        }
    } catch (error) {
        console.warn('Error creating moment object:', error, 'Date:', date);
        return null;
    }
};

/**
 * Safely checks if a date is valid
 * @param {any} date - The date to check
 * @returns {boolean} - True if date is valid
 */
export const isValidDate = (date) => {
    if (!date || date === null || date === undefined) {
        return false;
    }

    try {
        return moment(date).isValid();
    } catch (error) {
        return false;
    }
}; 