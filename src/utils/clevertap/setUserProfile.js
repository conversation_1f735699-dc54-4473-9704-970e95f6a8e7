import constants from '../constants/constants';
import rollbar from '../rollbar/rollbar';
import NewReli<PERSON>Logger from '../newrelic/logger';
// const CleverTap = require('clevertap-react-native');

const setClevertapUser = (user) => {
    try {
        
        const clubNames = user?.clubs?.map((club) => club.club.name).join(',');
        
        // CleverTap.profileSet({
        //     [constants?.CLEVERTAP?.PROFILE.EMAIL]: user.email,
        //     [constants?.CLEVERTAP?.PROFILE.USERNAME]: user.username,
        //     [constants?.CLEVERTAP?.PROFILE.NAME]: user.first_name + ' ' + user.last_name,
        //     [constants?.CLEVERTAP?.PROFILE.PHONE]: user.phone,
        //     [constants?.CLEVERTAP?.PROFILE.GENDER]: user.gender == 'female' ? 'F' : 'M',
        //     [constants?.CLEVERTAP?.PROFILE.GOLF_INDEX]: user.handicap,
        //     [constants?.CLEVERTAP?.PROFILE.CLUBS]: clubNames,
        //     [constants?.CLEVERTAP?.PROFILE.DATE_OF_JOINING]: user.activated_date,
        //     [constants?.CLEVERTAP?.PROFILE.CURRENT_MEMBERSHIP_PLAN]: user.membership_plan_id,
        //     [constants?.CLEVERTAP?.PROFILE.TG_AMBASSADOR]: user.is_tg_ambassador,
        //     [constants?.CLEVERTAP?.PROFILE.TIER]: user.tier,
        //     [constants?.CLEVERTAP?.PROFILE.USER_VISIBILITY]: user?.visibleToPublic
        //         ? 'All Thousand Greens Members'
        //         : 'My TG community only',
        //     [constants?.CLEVERTAP?.PROFILE.ACCOUNT_MUTED]: user.muted,
        // });
    } catch (error) {
        rollbar.error('Error for set user login', error);
        NewRelicLogger.logError('CleverTap User Profile Error', error, {
            userId: user?.id,
            source: 'setClevertapUser'
        });
    }
};

export default setClevertapUser;
