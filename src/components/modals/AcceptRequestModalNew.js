import React, { useState, useContext, useEffect } from 'react';
import { View, TouchableOpacity, Text, TouchableWithoutFeedback, Modal } from 'react-native';
import { Calendar } from 'react-native-calendars';
import moment from 'moment';
import Moment from 'moment';

import { colors } from '../../theme/theme';
import useClient from '../../hooks/useClient';
import { AuthContext } from '../../context/AuthContext';
import { ACCEPT_REQUEST_AS_HOST } from '../../service/EndPoint';
import { calculateGuestTimeRestrictionDates } from '../../utils/helpers/CreateClubHelper';
import { FETCH_INTERNATIONAL_CLUB_DURATION, FETCH_LOACAL_CLUB_DURATION } from '../../graphql/queries/clubDuration';
import useQuery from '../../hooks/useQuery';
import { fetcher } from '../../service/fetcher';
import showToast from '../toast/CustomToast';
import { GlobalContext } from '../../context/contextApi';

const CREATE_GAME = `mutation createGame($game: game_insert_input!) {
    insert_game_one(object: $game) {
      game_id
    }
}`;

const ACCEPT_REQUEST = `mutation acceptRequest($accepted_request: accepted_request_insert_input!) {
    insert_accepted_request_one(object: $accepted_request) {
      host_id
      status
      date
    }
  }
`;

export default function AcceptRequestModalNew({ modal, setModal, callBack = () => {} }) {
    const {state, actions} = useContext(GlobalContext)
    const [date, setDate] = useState();
    const [disabledDates, setDisabledDates] = useState();
    const [loading, setLoading] = useState(false);
    const { user } = useContext(AuthContext);
    const [error, setError] = useState();
    const client = useClient();
    const [club, setClub] = useState();
    const [startDate, setStartDate] = useState(modal.startDate);
    const [endDate, setEndDate] = useState(modal.endDate);

    console.log('params club', club);
    let localClub = useQuery(FETCH_LOACAL_CLUB_DURATION);

    let internationalClub = useQuery(FETCH_INTERNATIONAL_CLUB_DURATION);
    let month =
        club?.country_code == user?.phone_number_details?.countryCode
            ? localClub?.data?.system_setting[0]?.value?.value
            : internationalClub?.data?.system_setting[0]?.value?.value;

    console.log('club?.country_code', club?.country_code);
    console.log('user?.phone_number_details?.countryCode', user?.phone_number_details?.countryCode);

    useEffect(() => {
        // if (month)
        callClubUserData(modal?.request?.club_id);
    }, [month]);

    async function callClubUserData(club_id) {
        const { view_club_search } = await client.request(`{
            view_club_search(where: {id: {_eq: ${club_id}}}) {
                id
                name
                user_array
                guest_time_restrictions
                closure_period
                guest_fee,
                country_code
            }
        }`);
        console.log('view_club_search', view_club_search);
        const club = view_club_search?.length > 0 ? view_club_search[0] : null;
        setClub(club);
        if (club?.closure_period) {
            const dates = calculateGuestTimeRestrictionDates(
                club?.closure_period,
                club?.guest_time_restrictions,
                month * 12,
            );
            setDisabledDates(dates);
        }
    }

    function getDates() {
        let disabled = {};

        if (disabledDates && disabledDates.length > 0) {
            disabled = disabledDates.reduce((dates, date) => {
                return {
                    ...dates,
                    [date]: {
                        disabled: true,
                        disableTouchEvent: true,
                    },
                };
            }, {});
        }

        if (date) {
            return {
                [date.format('YYYY-MM-DD')]: {
                    selected: true,
                    selectedColor: colors.darkteal,
                    textColor: 'white',
                },
                ...disabled,
            };
        } else {
            return disabled;
        }
    }

    // Function to accept request as a host
    async function createGame() {
        actions.setAppLoader(true);
        setModal();
        if (date) {
            setLoading(true);

            const { request } = modal;

            const acceptRequestParams = {
                userId: user?.id,
                gameDate: moment(date).format('YYYY-MM-DD'),
                requestId: request?.request_id,
            };

            fetcher({
                endpoint: ACCEPT_REQUEST_AS_HOST,
                method: 'POST',
                body: acceptRequestParams,
            })
                .then(async (res) => {
                    if (res?.status) {
                        setLoading(false);
                        await modal.onRefresh();
                        setModal();
                        callBack();
                        actions.setAppLoader(false);
                    } else {
                        showToast({});
                        actions.setAppLoader(false);
                    }
                })
                .catch(() => {
                    showToast({});
                    actions.setAppLoader(false);
                });
        } else {
            setError('Date is required');
        }
    }

    return (
        <Modal visible={true} transparent={true}>
            <View
                style={{
                    position: 'absolute',
                    left: 0,
                    right: 0,
                    top: 0,
                    bottom: 0,
                    paddingHorizontal: 30,
                    justifyContent: 'center',
                    alignItems: 'center',
                    zIndex: 100,
                }}>
                <TouchableWithoutFeedback onPress={() => setModal()}>
                    <View
                        style={{
                            position: 'absolute',
                            left: 0,
                            right: 0,
                            top: 0,
                            bottom: 0,
                            backgroundColor: 'rgba(0,0,0,0.5)',
                        }}
                    />
                </TouchableWithoutFeedback>
                <View
                    style={{
                        backgroundColor: 'white',
                        borderRadius: 10,
                        width: '100%',
                        overflow: 'hidden',
                    }}>
                    <Text
                        style={{
                            fontFamily: 'RobotoSlab-Regular',
                            padding: 30,
                            paddingBottom: 15,
                            fontSize: 20,
                            textAlign: 'center',
                        }}>
                        Select Date
                    </Text>
                    <Calendar
                        current={
                            modal.minDate && new Date(modal.minDate) > new Date() ? new Date(modal.minDate) : new Date()
                        }
                        minDate={
                            modal.minDate && new Date(modal.minDate) > new Date() ? new Date(modal.minDate) : new Date()
                        }
                        maxDate={Moment()
                            .add(
                                club?.country_code?.toLowerCase() ===
                                    user?.phone_number_details?.countryCode?.toLowerCase()
                                    ? localClub?.data?.system_setting[0]?.value?.value * 12
                                    : internationalClub?.data?.system_setting[0]?.value?.value * 12,
                                'months',
                            )
                            .format('YYYY-MM-DD')}
                        markedDates={getDates()}
                        // markingType={'period'}
                        hideExtraDays={true}
                        disableMonthChange={true}
                        onDayPress={(day) => {
                            setDate(Moment(day.dateString));
                        }}
                    />
                    {error && !date && (
                        <Text
                            style={{
                                fontFamily: 'Ubuntu-Regular',
                                color: 'red',
                                textAlign: 'center',
                                marginTop: 20,
                            }}>
                            A selected date is required
                        </Text>
                    )}
                    <View
                        style={{
                            flexDirection: 'row',
                            padding: 30,
                            justifyContent: 'space-between',
                        }}>
                        <TouchableOpacity
                            onPress={() => {
                                setModal();
                            }}
                            style={{
                                paddingHorizontal: 30,
                                paddingVertical: 10,
                                borderRadius: 10,
                            }}>
                            <Text
                                style={{
                                    fontFamily: 'Ubuntu-Regular',
                                    fontSize: 16,
                                    color: colors.darkgray,
                                }}>
                                Cancel
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => {
                                createGame();
                            }}
                            style={{
                                backgroundColor: colors.darkteal,
                                paddingHorizontal: 30,
                                paddingVertical: 10,
                                borderRadius: 10,
                            }}>
                            <Text
                                style={{
                                    fontFamily: 'Ubuntu-Regular',
                                    fontSize: 16,
                                    color: 'white',
                                }}>
                                Accept Request
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );
}
