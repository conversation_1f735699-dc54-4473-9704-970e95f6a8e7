import React from 'react';
import { Modal, View, TouchableOpacity, SafeAreaView, StyleSheet } from 'react-native';
import { WebView } from 'react-native-webview';
import { colors } from '../../theme/theme';
import { CrossIcon } from '../../assets/svg';

const WebViewModal = ({ visible, url, onClose }) => {
    return (
        <Modal
            visible={visible}
            animationType="slide"
            presentationStyle="fullScreen"
        >
            <SafeAreaView style={styles.container}>
                <View style={styles.header}>
                    <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                        <CrossIcon />
                    </TouchableOpacity>
                </View>
                <WebView
                    source={{ uri: url }}
                    style={styles.webview}
                    startInLoadingState={true}
                    javaScriptEnabled={true}
                    domStorageEnabled={true}
                />
            </SafeAreaView>
        </Modal>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.white,
    },
    header: {
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        paddingHorizontal: 15,
        borderBottomWidth: 1,
        borderBottomColor: colors.borderColor,
    },
    closeButton: {
        padding: 5,
    },
    webview: {
        flex: 1,
    },
});

export default WebViewModal; 