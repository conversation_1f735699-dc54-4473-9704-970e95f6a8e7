import React, { useContext, useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

// Context, components imports
import { AuthContext } from '../../context/AuthContext';
import TGText from '../fields/TGText';
import TierCard from './TierCard';
import TealButtonNew from '../buttons/TealButtonNew';

// Assets, Theme, Utils and types imports
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';
import {
    ClubGolfIconTeal,
    CreateRequestIconWhite,
    MapLocationIconNew,
    MemberIcon,
    ProfileTickIcon,
} from '../../assets/svg';
import { Club, MapClubDetail, FilterState } from '../../interface';
import { RootStackParamList } from '../../interface/type';
import { HIGHLY_REQUESTED, UNEXPLORED } from '../../utils/constants/strings';
import { GlobalContext } from '../../context/contextApi';
import TGCustomIconButton from '../buttons/TGCustomIconButton';
import getIsClubUnmute from './actions/getIsClubUnMute';
import { apiServices } from '../../service/apiServices';
import { getClubMembersForUser } from '../../utils/helpers/CreateClubHelper';

function MapClubDetailNew({
    club,
    closed,
    filter,
    setSelectedClub,
}: {
    club: MapClubDetail;
    closed: () => void;
    filter: FilterState;
    setSelectedClub: (club: MapClubDetail) => void;
}) {
    const { user } = useContext(AuthContext);
    const { state, actions } = useContext(GlobalContext);
    const { mapCurrentClub } = state;
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const [contactOnlyClub, setContactOnlyClub] = useState(false);
    const [ownClub, setOwnClub] = useState<Club[]>([]);
    const [memberCount, setMemberCount] = useState(0);
    const {
        clubMemberCount = 0,
        clubs = {} as MapClubDetail['clubs'],
        totalMemberCount = 0,
        requestCreated = 0,
        request = '',
        isEligibleForCreateRequest = false,
    } = useMemo(() => {
        if (club) {
            // Safely return the `club` properties
            return {
                clubMemberCount: club?.clubMemberCount,
                clubs: club.clubs,
                totalMemberCount: club.totalMemberCount,
                requestCreated: club.requestCreated,
                request: club.request,
                isEligibleForCreateRequest: club?.isEligibleForCreateRequest,
            };
        }
        return {}; // Safely return an empty object
    }, [club]) || {}; // Add fallback to ensure the destructuring always works

    useEffect(() => {
        findClubMember(clubs?.id);
        let temporaryClub: Club[] = [];
        user?.clubs?.map((club: any) => {
            temporaryClub.push(club?.club_id);
        });
        setOwnClub(temporaryClub);
    }, [user]);

    useLayoutEffect(() => {
        handleSetContactClub();
    }, [club]);

    const isShowCreateRequest = () => {
        if (mapCurrentClub?.properties?.color?.includes('blue')) {
            return isEligibleForCreateRequest;
            //@ts-ignore
        } else if (!['grey', 'grey_contact', 'teal', 'teal_contact'].includes(mapCurrentClub?.properties?.color)) {
            return true;
        }
        return false;
    };

    const handleSetContactClub = () => {
        if (
            //@ts-ignore
            !ownClub.includes(clubs?.id) && // if the user is not a part of this club
            (club?.contacts || club?.friends) && // and the user has contacts or friends
            ((!user?.visibleToPublic && state.clubDetails?.properties?.color === 'teal_contact') || //this means it is a MY TG club
                !clubMemberCount) // and there is no member in this club
        ) {
            setContactOnlyClub(true);
        } else {
            setContactOnlyClub(false);
        }
    };

    async function findClubMember(club_id: number) {
        //@ts-ignore
        const { view_club_search } = await client.request(`{
            view_club_search(where: {id: {_eq: ${club_id}}}) {
                id
                name
                user_array
                guest_time_restrictions
                closure_period
                guest_fee
            }
        }`);
        const { tier, visibleToPublic, clubs } = user;
        const user_data = {
            tier,
            visibleToPublic,
            clubs,
        };
        if (view_club_search.length) {
            let extraClubInfo = view_club_search[0];
            const members = getClubMembersForUser({
                user_data,
                user_array: extraClubInfo.user_array,
                filters: {
                    all_ages: true,
                    englishFluency: ['native', 'fluent', 'understandable', 'basic'],
                    gender: 'both',
                    handicap: ['< 5', '5-10', '> 10'],
                    //@ts-ignore
                    max_age: 100,
                    //@ts-ignore
                    min_age: 0,
                    pace: ['fast', 'average', 'leisure'],
                    playAsCouple: false,
                },
                //@ts-ignore
                club_id,
                clubTier: state.mapCurrentClub?.lowest_visible_tier,
            });
            setMemberCount(members.length);
        } else {
            setMemberCount(0);
        }
    }

    const canCreateRequest = async () => {
        actions.setAppLoader(true);
        const isClubMuted = getIsClubUnmute(club, user?.clubs);
        if (club) {
            try {
                const res = await apiServices.checkCanCreateRequest(user, true);
                if (res?.canCreate) {
                    if (!isClubMuted) {
                        actions.setShouldDetailBottomSheetOpen(false);
                        // Use requestAnimationFrame instead of setTimeout for better performance
                        requestAnimationFrame(() => {
                            actions?.setSelectedClub(club);
                            navigation.navigate('Create Request', {
                                onGoBack: (data) => {
                                    actions.setShouldDetailBottomSheetOpen(true);
                                },
                                club: club,
                                memberCount,
                                category: filter.category,
                                setSelectedClubState: setSelectedClub,
                            });
                        });
                    }
                } else {
                    if (!isClubMuted) {
                        // Close the bottom sheet first
                        actions.setShouldDetailBottomSheetOpen(false);

                        // Use requestAnimationFrame to ensure proper timing for navigation
                        requestAnimationFrame(() => {
                            navigation.navigate('DeleteChannelConfirmationPopup', {
                                popupSubText: res?.message || 'You are not eligible to create a request at this time.',
                                firstBtnLabel: 'Cancel',
                                secondBtnLabel: 'Ok',
                                handleYesButton: () => {
                                    // Reopen the bottom sheet after popup is dismissed
                                    requestAnimationFrame(() => {
                                        actions.setShouldDetailBottomSheetOpen(true);
                                    });
                                },
                                handleFirstBtnPress: () => {
                                    // Reopen the bottom sheet after popup is dismissed
                                    requestAnimationFrame(() => {
                                        actions.setShouldDetailBottomSheetOpen(true);
                                    });
                                },
                            });
                        });
                    }
                }
            } catch (error) {
                console.error('Error checking if can create request:', error);
            } finally {
                actions.setAppLoader(false);
            }
        }
    };

    return (
        <>
            {/* Highly Requested Label */}
            {club?.clubs?.club_demand_type ? (
                <View style={styles.highlyRequestedContainer}>
                    <View style={styles.highlyRequestedBadge}>
                        <Text style={styles.highlyRequestedText}>
                            {club?.clubs?.club_demand_type === 1
                                ? HIGHLY_REQUESTED
                                : club?.clubs?.club_demand_type === 2 && UNEXPLORED}
                        </Text>
                    </View>
                </View>
            ) : null}

            <View style={styles.detailsContainer}>
                <TierCard clubType={clubs?.club_type} tier={clubs?.lowest_visible_tier} />
                <View style={styles.topDetailSection}>
                    <View style={styles.headerWrapper}>
                        <ClubGolfIconTeal />
                        <View style={styles.clubNameWrapper}>
                            <Text ellipsizeMode={'tail'} numberOfLines={1} style={styles.clubNameStyle}>
                                {clubs?.name}
                            </Text>
                        </View>
                    </View>
                </View>
                <View style={styles.topDetailSection}>
                    <View style={styles.clubWrapper}>
                        <MapLocationIconNew />
                        <Text numberOfLines={2} style={styles.clubText}>
                            {clubs?.address}
                        </Text>
                    </View>
                </View>
                {!contactOnlyClub && <View style={styles.divider} />}
                {((club?.active_user_count && club?.active_user_count > 0) || totalMemberCount > 0) &&
                    !contactOnlyClub && (
                        <View style={styles.box2}>
                            <MemberIcon />
                            <TGText style={styles.text3}>
                                Total Member
                                {`${totalMemberCount > 1 ? 's' : ''}`} :{' '}
                                <Text style={{ color: colors.lightBlack }}>{totalMemberCount}</Text>
                            </TGText>
                        </View>
                    )}
                {clubMemberCount > 0 && !contactOnlyClub && (
                    <View style={[styles.box2, { marginTop: Spacing.SCALE_8 }]}>
                        <ProfileTickIcon />
                        <TGText style={styles.text3}>
                            Requests Accepted :{' '}
                            <Text style={{ color: colors.brightGreen }}>
                                {request === '0.00' ? 'NA' : `${request}% of ${requestCreated} Requests`}
                            </Text>
                        </TGText>
                    </View>
                )}

                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        marginTop: Spacing.SCALE_24,
                    }}>
                    <TealButtonNew
                        text={'View Club Details'}
                        onPress={() => {
                            // closed();
                            actions.setShouldDetailBottomSheetOpen(false);
                            requestAnimationFrame(() => {
                                navigation.navigate('MapDetails');
                            });
                        }}
                        btnStyle={[
                            styles.btnContainerStyle,
                            { width: isShowCreateRequest() && club?.clubs?.club_type === 0 ? '48%' : '100%' },
                        ]}
                        textStyle={styles.btnTextStyle}
                    />
                    {
                        //@ts-ignore
                        isShowCreateRequest() && club?.clubs?.club_type === 0 && (
                            <TGCustomIconButton
                                text="Create Request"
                                btnStyle={styles.buttonStyle}
                                onPress={canCreateRequest}
                                //@ts-ignore
                                Icon={CreateRequestIconWhite}
                                iconHeight={Size.SIZE_20}
                                iconWidth={Size.SIZE_20}
                                textStyle={{ color: colors.white }}
                                loading={false}
                            />
                        )
                    }
                </View>
            </View>
        </>
    );
}

const styles = StyleSheet.create({
    detailsContainer: {
        flexDirection: 'column',
        padding: Spacing.SCALE_16,
        backgroundColor: colors.whiteRGB,
        borderRadius: Size.SIZE_12,
        zIndex: 1,
        height: '100%',
    },
    highlyRequestedContainer: {
        position: 'absolute',
        top: 8,
        left: 0,
        zIndex: 2,
        alignItems: 'center',
        justifyContent: 'center',
    },
    highlyRequestedBadge: {
        backgroundColor: colors.lightTealNew,
        paddingHorizontal: Spacing.SCALE_12,
        paddingVertical: Spacing.SCALE_5,
        borderRadius: Size.SIZE_4,
        borderWidth: 1,
        borderColor: colors.whiteRGB,
        marginLeft: Spacing.SCALE_11,
    },
    highlyRequestedText: {
        color: colors.darkteal,
        fontSize: Typography.FONT_SIZE_10,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_10,
    },
    topDetailSection: {
        flexDirection: 'row',
        marginTop: Spacing.SCALE_12,
    },
    detailSection: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
    },
    detailSection1: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    see_offer: {
        paddingVertical: 2.5,
        paddingHorizontal: 10,
        borderColor: 'teal',
        borderWidth: 1,
        borderRadius: 5,
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: 10,
        height: 25,
    },
    tierNameWrapper: {
        paddingHorizontal: Spacing.SCALE_4,
        paddingVertical: Spacing.SCALE_2,
        backgroundColor: 'rgba(9, 128, 137, 0.1)',
        borderRadius: Size.SIZE_2,
    },
    clubNameStyle: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_18,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
    },
    popupHeaderWrapper: {
        borderRadius: 4,
        shadowColor: 'rgba(0, 0, 0, 0.06)',
        shadowOffset: { width: 2, height: 4 },
        shadowOpacity: 1,
        shadowRadius: 4,
        backgroundColor: '#FFFFFF',
        paddingHorizontal: 10,
    },
    btnContainerStyle: {
        height: Size.SIZE_40,
        paddingVertical: 0,
        backgroundColor: colors.whiteRGB,
        borderWidth: 1,
        borderColor: colors.darkgray,
    },
    btnTextStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
        lineHeight: Size.SIZE_18,
        color: colors.lightBlack,
    },
    favouriteWrapper: {
        borderWidth: Size.SIZE_1,
        borderColor: 'rgba(9, 128, 137, 1)',
        borderRadius: Size.SIZE_4,
        alignItems: 'center',
        marginTop: Spacing.SCALE_14,
        padding: Spacing.SCALE_13,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    clubWrapper: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_10,
    },
    clubText: {
        color: colors.fadeBlack,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
        lineHeight: Size.SIZE_18,
        width: Size.SIZE_280,
    },
    text: {
        color: 'rgba(9, 128, 137, 1)',
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Medium',
    },
    box1: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: Spacing.SCALE_10,
    },
    box2: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        columnGap: Spacing.SCALE_10,
    },
    text1: {
        color: 'rgba(51, 51, 51, 1)',
        paddingLeft: 5,
        fontSize: Typography.FONT_SIZE_12,
    },
    text3: {
        fontWeight: '400',
        fontSize: Typography.FONT_SIZE_13,
        fontFamily: 'Ubuntu-Regular',
        color: colors.fadeBlack,
    },
    text4: {
        color: colors.dark_charcoal,
        fontWeight: '500',
        fontSize: Typography.FONT_SIZE_12,
        fontFamily: 'Ubuntu-Medium',
    },
    btnWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    clubNameWrapper: {
        width: Size.SIZE_230,
    },
    headerWrapper: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_8,
        alignItems: 'center',
    },
    divider: {
        height: 1,
        backgroundColor: colors.lightGrey,
        marginVertical: Spacing.SCALE_8,
    },
    buttonStyle: {
        width: '48%',
        height: Size.SIZE_40,
        borderRadius: Size.SIZE_8,
        backgroundColor: colors.tealRgb,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 0,
    },
});

export default MapClubDetailNew;
