import React, { useContext } from 'react';
import { Text, Pressable } from 'react-native';
import CheckIcon from '../../assets/images/check.svg';
import CheckboxTealSquare from '../../assets/svg/CheckboxTealSquare.svg';
import SquareBoxTeal from '../../assets/svg/SquareBoxTeal.svg';

import { AuthContext } from '../../context/AuthContext';
import { FormContext } from '../../forms/FormContext';
import { colors } from '../../theme/theme';

export default function Checkbox({
    name,
    label,
    marginBottom = 15,
    textStyle,
    useTierVisibilityEffect,
    disabled = false,
    showpolicy,
    child,
    oncheckAccess,
    oncheckTolerance,
    onCheckFraud,
    customHeight,
    width = '100%',
    customWidth,
    checkStyle,
    isParentClickable = false,
    mapFilter = false,
    type = '',
    containerStyle,
    iconType = 1

}) {
    const { form, updateForm, updateFields } = useContext(FormContext);
    const { user } = useContext(AuthContext);

    function onChecked() {
        if (!disabled) {
            if (useTierVisibilityEffect) {
                if (!form[name]) {
                    if (name === 'visibleToOlive') {
                        updateFields({
                            visibleToSage: true,
                            visibleToMoss: true,
                            visibleToOlive: true
                        })
                    }
                    if (name === 'visibleToMoss') {
                        updateFields({
                            visibleToSage: true,
                            visibleToMoss: true
                        })
                    }
                    if (name === 'visibleToSage') {
                        updateFields({
                            visibleToSage: true,
                        })
                    }
                } else if (name === 'visibleToOlive' || (name === 'visibleToMoss' && !form?.visibleToOlive) ||
                    (name === 'visibleToSage' && !form?.visibleToOlive && !form?.visibleToMoss)) {
                    updateForm(name, form[name] ? false : true)
                }

            } else if (mapFilter) {
                mapFilterCheckUncheck(name, !form[name])
            } else if (name == 'personal_use_access') {
                updateForm(name, form[name] ? false : true)
                form[name] ? oncheckAccess(false) : oncheckAccess(true)
            } else if (name == 'zero_tolerance') {
                updateForm(name, form[name] ? false : true)
                form[name] ? oncheckTolerance(false) : oncheckTolerance(true)
            }
            else if (name == 'fraud') {
                updateForm(name, form[name] ? false : true)
                form[name] ? onCheckFraud(false) : onCheckFraud(true)
            } else {
                updateForm(name, form[name] ? false : true)
            }
        }
    }

    function mapFilterCheckUncheck(name, active) {
        console.log('filter daata', form);
        if (!active && Object.values(form).filter((val) => val && typeof val === 'boolean').length > 2) {
            updateForm(name, false)
        } else updateForm(name, true)
    }

    return (
        <Pressable
            onPress={() => isParentClickable && onChecked()}
            style={{
                width,
                flexDirection: 'row',
                marginBottom,
                ...containerStyle,
                backgroundColor:
                    containerStyle && form[name]
                        ? 'rgba(9, 128, 137,0.1)'
                        : 'transparent',
            }}>
            <Pressable
                onPress={onChecked}
                style={[
                    {
                        height: customHeight ? customHeight : 16,
                        width: customWidth ? customWidth : 16,
                        borderRadius: 5,
                        backgroundColor: form[name]
                            ? colors.darkteal
                            : type === ''
                            ? 'rgba(0,0,0,0.1)'
                            : 'transparent',
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginTop: 3,
                        borderColor: form[name]
                            ? 'transparent'
                            : type === ''
                            ? 'transparent'
                            : colors.fadeBlack,
                        borderWidth: form[name] ? 0 : 1,
                    },
                    checkStyle,
                ]}>
                {iconType === 1
                    ? form[name] && <CheckIcon height={10} width={10} />
                    : null}
                {iconType === 2 ? (
                    form[name] ? (
                        <CheckboxTealSquare height={20} width={20} />
                    ) : (
                        <SquareBoxTeal height={20} width={20} />
                    )
                ) : null}
            </Pressable>
            {showpolicy ? (
                child
            ) : (
                <Text
                    style={[
                        {
                            paddingLeft: 10,
                            fontFamily: 'Ubuntu-Regular',
                            color: colors.darkgray,
                            fontSize: 15,
                        },
                        textStyle,
                    ]}>
                    {label}
                </Text>
            )}
        </Pressable>
    );
}
