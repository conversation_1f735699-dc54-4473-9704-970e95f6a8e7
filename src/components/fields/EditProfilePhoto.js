import React, { useContext, useState } from 'react';
import { View, Image, TouchableOpacity, ActivityIndicator, StyleSheet } from 'react-native';
import { Image as ImageCompressor } from 'react-native-compressor';
import { launchImageLibrary } from 'react-native-image-picker';
import firebase from '@react-native-firebase/app';
import '@react-native-firebase/auth';
import '@react-native-firebase/storage';

import useClient from '../../hooks/useClient';
import { AuthContext } from '../../context/AuthContext';
import { UPDATE_USER } from '../../graphql/mutations/user';
import { DefaultNameProfile } from '../../utils/helpers/PersonalProfileHelper';
import { checkCameraPermission } from '../../components/permissionToast/PermissionToastComponent';
import EditProfileSvg from '../../assets/images/EditProfileSvg.svg';
import { Size, Typography } from '../../utils/responsiveUI';
import useThumbnail from '../../hooks/useThumbnail';
import constants from '../../utils/constants/constants';

async function uploadImage({ fileName, uri, path }) {
    const storageRef = firebase.storage().ref().child(`profilephotos/${firebase.auth()?.currentUser?.uid}-${fileName}`);
    await storageRef.putFile(uri);
    return storageRef.getDownloadURL();
}

export default function EditProfilePhoto(props) {
    const [photo, setPhoto] = useState(props.photo);
    const [pickerOpen, setPickerOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const { refreshUser, user } = useContext(AuthContext);
    const client = useClient();
    const {thumbnailUrl} = useThumbnail(user.profilePhoto, constants.ImageSize[128])

    async function uploadPhoto(file) {
        setLoading(true);

        // Compress the image size before uploading
        const compressedUrl = await ImageCompressor.compress(file?.uri, {
            compressionMethod: 'manual',
            maxWidth: 1280,
            quality: 0.8,
        });
        file.uri = compressedUrl;
        try {
            const photoURL = await uploadImage(file);
            await client.request(UPDATE_USER, {
                user_id: firebase.auth()?.currentUser?.uid,
                user: {
                    profilePhoto: photoURL,
                },
            });
            await refreshUser();
            setLoading(false);
        } catch (error) {
            setLoading(false);
        }
    }

    const showGallery = async () => {
        const isPermission = await checkCameraPermission();
        if (!pickerOpen && isPermission) {
            setPickerOpen(true);
            launchImageLibrary(
                {
                    tintColor: 'white',
                    maxHeight: 1024,
                    maxWidth: 1024,
                },
                async (response) => {
                    setPickerOpen(false);
                    if (response.didCancel) {
                        ('User cancelled image picker');
                    } else if (response.error) {
                        'ImagePicker Error: ', response.error;
                    } else if (response.customButton) {
                        'User tapped custom button: ', response.customButton;
                    } else {
                        const [fileData] = response?.assets;
                        setPhoto(fileData.uri);
                        uploadPhoto(fileData);
                    }
                },
            );
        }
    };

    return (
        <View>
            {thumbnailUrl ? (
                <Image source={{ uri: thumbnailUrl }} style={styles.profileContainerStyle} />
            ) : (
                <DefaultNameProfile
                    fName={props?.user?.first_name}
                    lName={props?.user?.last_name}
                    containerStyle={styles.profileContainerStyle}
                    textStyle={styles.initialsNameStyle}
                    screenName={'Profile'}
                />
            )}
            <TouchableOpacity onPress={showGallery} style={styles.editBtnStyle}>
                <EditProfileSvg height={15} width={15} />
            </TouchableOpacity>
            {loading && (
                <View style={styles.loaderStyle}>
                    <ActivityIndicator color="white" />
                </View>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    profileContainerStyle: {
        height: Size.SIZE_90,
        width: Size.SIZE_90,
        borderRadius: Size.SIZE_12,
    },
    initialsNameStyle: {
        fontSize: Typography.FONT_SIZE_30,
        textTransform: 'uppercase',
    },
    editBtnStyle: {
        backgroundColor: 'white',
        position: 'absolute',
        zIndex: 110,
        right: -10,
        top: -10,
        width: 30,
        height: 30,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 5,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        borderRadius: 50,
        elevation: 5,
    },
    loaderStyle: {
        borderRadius: 10,
        backgroundColor: 'rgba(0,0,0,0.5)',
        zIndex: 50,
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
