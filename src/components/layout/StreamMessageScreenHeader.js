import { useNavigation } from '@react-navigation/native';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import {
    Pressable,
    StyleSheet,
    Text,
    View,
    Image,
    TouchableOpacity,
} from 'react-native';

import { Back } from '../../assets/images/svg';
import { AuthContext } from '../../context/AuthContext';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import {
    CHANNEL_TITLE_LIMIT,
    MY_TG_GROUP,
    ONE_TO_ONE,
    SYSTEM,
    USER_CREATED_GROUP,
} from '../../screens/my-TG-Stream-Chat/client';
import TrippleDot from '../../assets/svg/TrippleDot.svg';
import GiftBoxIconTeal from '../../assets/svg/GiftBoxIconTeal.svg';
import TrippleDotPopup from '../../screens/my-TG-Stream-Chat/TGChatComponent/TrippleDotPopup';
import { colors } from '../../theme/theme';
import { GlobalContext } from '../../context/contextApi';
import getInitials from '../../utils/helpers/getInitials';
import { getOneToOneChannelName } from '../../screens/my-TG-Stream-Chat/action';
import routes from '../../config/routes';
import { fetcher } from '../../service/fetcher';
import { GET_EVENT_COUNT, GET_OFFER_COUNT } from '../../service/EndPoint';
import config from '../../config';
import useThumbnail from '../../hooks/useThumbnail';
import constants from '../../utils/constants/constants';

const StreamMessageScreenHeader = ({
    online,
    isdissabled = false,
    navigateToScreen,
    channel = {}
}) => {
    const { user } = useContext(AuthContext);
    const { state, actions } = useContext(GlobalContext);
    const [offerCount, setOfferCount] = useState(0);
    const [eventCount, setEventCount] = useState(0);
    const [profile, setProfile] = useState();
    const { otherUser } = state;
    const {thumbnailUrl} = useThumbnail(profile, constants.ImageSize[128])

    // Get offer count
    const getOfferCount = () => {
        return new Promise((resolve, reject) => {
            const payload = {
                groupId: channel?.data?.id,
                userId: user?.id,
            };
            try {
                fetcher({
                    endpoint: GET_OFFER_COUNT,
                    method: 'POST',
                    body: payload,
                }).then((response) => {
                    resolve(response || {});
                });
            } catch (e) {
                reject(e);
            }
        });
    };

    // GEt event count
    const getEventCount = () => {
        return new Promise((resolve, reject) => {
            const payload = {
                groupId: channel?.data?.id,
                userId: user?.id,
            };
            try {
                fetcher({
                    endpoint: GET_EVENT_COUNT,
                    method: 'POST',
                    body: payload,
                }).then((response) => {
                    resolve(response || {});
                });
            } catch (e) {
                reject(e);
            }
        });
    };

    useEffect(() => {
        const handleGetCount = async () => {
            const response = await getOfferCount();
            setOfferCount(response?.count);
            const eventCountResponse = await getEventCount();
            setEventCount(eventCountResponse?.count);
        };
        handleGetCount();
    }, []);

    const { members, type, isFriends } = useMemo(() => {
        const { state: { members } = {}, data: { type, isFriends } = {} } = channel;
        return { members, type, isFriends };
    }, []);

    useEffect(() => {
        setProfile(handleAvatar(channel));
    }, [
        channel?.data?.name,
        channel?.data?.image,
        user?.full_name,
        user?.first_name,
        user?.username,
        user?.profilePhoto,
        channel,
        user,
        otherUser?.user?.image,
        otherUser?.user?.name,
    ]);

    // Get screen name or chat screen name
    const handleScreenName = () => {
        if (channel?.type === SYSTEM || channel?.type === USER_CREATED_GROUP) {
            return channel?.data?.name?.length > CHANNEL_TITLE_LIMIT
                ? `${channel?.data?.name.substring(0, CHANNEL_TITLE_LIMIT)}...`
                : channel?.data?.name;
        } else {
            return CustomPreviewTitle(channel);
        }
    };
    const CustomPreviewTitle = (channel) => {
        const name = getOneToOneChannelName(channel, user, state?.allFriendsId);
        return name?.length > CHANNEL_TITLE_LIMIT
            ? `${name.substring(0, CHANNEL_TITLE_LIMIT)}...`
            : name;
    };

    const handleAvatar = (channel) => {
        const { type, data, state } = channel;
        let img;
        if (type === ONE_TO_ONE) {
            let otherImage = Object.values(state?.members).find(
                (member) => member?.user_id !== user?.id,
            );
            if (otherImage?.user?.image) {
                img = otherImage.user?.image;
                return img;
            }
        } else {
            img = data?.image;
            return img;
        }
    };

    const TrippleDotHandler = () => {
        if (
            isFriends ||
            (state?.channelMembers?.length === 1 && type === ONE_TO_ONE)
        ) {
            return null;
        } else if (
            channel?.data?.frozen === false &&
            channel?.type === ONE_TO_ONE
        ) {
            return (
                <TouchableOpacity
                    style={styles.trippleDotWrapper}
                    onPress={() => {
                        navigation.push(config.routes.TRIPLE_DOT_POPUP_SCREEN, { selectedChannel: channel })
                    }}>
                    <TrippleDot height={Size.SIZE_20} width={Size.SIZE_5} />
                </TouchableOpacity>
            );
        } else if (
            channel?.data?.frozen === true &&
            channel?.data?.frozen &&
            channel?.data?.blockedBy?.includes(user?.id)
        ) {
            return (
                <TouchableOpacity
                    style={styles.trippleDotWrapper}
                    onPress={() => {
                        navigation.push(config.routes.TRIPLE_DOT_POPUP_SCREEN, { selectedChannel: channel })
                    }}>
                    <TrippleDot height={Size.SIZE_20} width={Size.SIZE_5} />
                </TouchableOpacity>
            );
        } else if (
            channel.data?.type === MY_TG_GROUP &&
            (offerCount > 0 || eventCount > 0)
        ) {
            return (
                <TouchableOpacity
                    style={styles.trippleDotWrapper}
                    onPress={() => {
                        navigation.navigate(routes.OFFER_AND_EVENT_LIST, {
                            channel: channel?.data,
                        });
                    }}>
                    <View style={styles.GiftIconWrapper}>
                        <GiftBoxIconTeal
                            height={Size.SIZE_30}
                            width={Size.SIZE_30}
                        />
                    </View>
                </TouchableOpacity>
            );
        }
        return null;
    };

    const handleOnPress = () => {
        if (!isdissabled) {
            if (channel?.type !== ONE_TO_ONE) {
                if (channel?.type == MY_TG_GROUP) {
                    navigation.navigate(config.routes.MY_TG_GROUP_INFO, {
                        group: {
                            streamId: channel?.data?.id,
                            name: channel?.data?.name
                        },
                        previousScreen: 'chat'
                    })
                }
                else {
                    navigation.navigate('GroupInfo')
                }
            } else {
                navigation.navigate('UserProfileScreen', { selectedUser: {} });
            }
        }
    };
    const navigation = useNavigation();

    const handleChannelIcon = () => {
        if (state?.channelMembers?.length === 1 && type === ONE_TO_ONE)
            return (
                <>
                    <TouchableOpacity>
                        {
                            <View style={styles.imageWrapper2}>
                                <Text style={styles.imageTextStyle1}>
                                    {'?'}
                                </Text>
                                {online?.user?.online &&
                                    !channel?.data?.frozen && (
                                        <View
                                            style={
                                                styles.onlineDotStyle
                                            }></View>
                                    )}
                            </View>
                        }
                    </TouchableOpacity>
                    <View style={styles.screenNameWrapper}>
                        <Text style={styles.deleteUserText}>
                            {'Deleted User'}
                        </Text>
                    </View>
                </>
            );
        else
            return (
                <>
                    <TouchableOpacity onPress={handleOnPress}>
                        {profile ? (
                            <View style={styles.imageWrapper}>
                                <Image
                                    source={{ uri: thumbnailUrl }}
                                    style={styles.img}
                                />
                                {online?.user?.online &&
                                    !channel?.data?.frozen && (
                                        <View
                                            style={
                                                styles.onlineDotStyle
                                            }></View>
                                    )}
                            </View>
                        ) : (
                            <View style={styles.imageWrapper1}>
                                <Text style={styles.imageTextStyle}>
                                    {getInitials(handleScreenName())}
                                </Text>
                                {online?.user?.online &&
                                    !channel?.data?.frozen && (
                                        <View
                                            style={
                                                styles.onlineDotStyle
                                            }></View>
                                    )}
                            </View>
                        )}
                    </TouchableOpacity>
                    <View style={styles.screenNameWrapper}>
                        <Text style={styles.text}>{handleScreenName()}</Text>
                        {online?.user?.online && !channel?.data?.frozen && (
                            <Text style={styles.onlinText}>Online</Text>
                        )}
                    </View>
                </>
            );
    };

    return (
        <>
            <View style={styles.container}>
                <View style={styles.headerWrapper}>
                    <Pressable
                        style={styles.backButtonWrapper}
                        onPress={() => {
                            if (navigateToScreen) {
                                if (navigateToScreen === config.routes.MY_TG) {
                                    navigation.reset({
                                        index: 0,
                                        routes: [{ name: config.routes.BOTTOM_TAB_NAVIGATION, params: { screen: 'TGChat' } }],
                                    });
                                } else {
                                    navigation.goBack();
                                }
                            } else {
                                navigation.goBack();
                            }
                        }}
                    >
                        <Back />
                    </Pressable>

                    <TouchableOpacity
                        style={{ flexDirection: 'row', flex: 1 }}
                        onPress={handleOnPress}>
                        {handleChannelIcon()}
                    </TouchableOpacity>

                </View>
                <TrippleDotHandler />
            </View>
        </>
    );
};

export default StreamMessageScreenHeader;

const styles = StyleSheet.create({
    container: {
        height: Size.SIZE_80,
        width: '100%',
        backgroundColor: 'rgba(255, 255, 255, 1)',
        paddingHorizontal: Spacing.SCALE_16,
        flexDirection: 'row',
    },
    text: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_20,
        color: 'rgba(51, 51, 51, 1)',
    },
    deleteUserText: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_20,
        color: 'rgba(204,204,204,1)',
    },
    headerWrapper: {
        flex: 1,
        display: 'flex',
        flexDirection: 'row',
        marginTop: Spacing.SCALE_30,
        alignItems: 'center',
    },
    backButtonWrapper: {
        width: 30,
        height: 40,
        justifyContent: 'center',
    },
    screenNameWrapper: {
        justifyContent: 'center',
        marginLeft: Spacing.SCALE_15,
    },
    imageWrapper: {
        width: Spacing.SCALE_40,
        height: Spacing.SCALE_40,
        borderRadius: Size.SIZE_100,
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: Spacing.SCALE_15,
        borderColor: 'red',
    },
    imageWrapper1: {
        width: Spacing.SCALE_40,
        height: Spacing.SCALE_40,
        borderRadius: Size.SIZE_100,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(9, 128, 137, 1)',
        marginLeft: Spacing.SCALE_15,
        borderColor: 'red',
    },
    imageWrapper2: {
        width: Spacing.SCALE_40,
        height: Spacing.SCALE_40,
        borderRadius: Size.SIZE_100,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.lightGrey,
        marginLeft: Spacing.SCALE_15,
        borderColor: 'red',
    },
    imageTextStyle: {
        textAlign: 'center',
        fontSize: 25,
        color: 'rgba(255, 255, 255, 1)',
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
        textTransform: 'capitalize',
    },
    imageTextStyle1: {
        textAlign: 'center',
        fontSize: 25,
        color: colors.lightShadeGray,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
        textTransform: 'capitalize',
    },
    onlinText: {
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_18,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Medium',
        color: 'rgba(153, 153, 153, 1)',
    },
    onlineDotStyle: {
        width: Size.SIZE_8,
        height: Size.SIZE_8,
        borderRadius: 50,
        backgroundColor: 'rgba(46, 147, 96, 1)',
        position: 'absolute',
        right: 0,
        bottom: 0,
    },
    image: {
        width: Size.SIZE_40,
        height: Size.SIZE_40,
        borderRadius: 100,
    },
    trippleDotWrapper: {
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: Spacing.SCALE_30,
        width: Spacing.SCALE_50,
    },
    avatarImageBackground: {
        width: Spacing.SCALE_40,
        height: Spacing.SCALE_40,
        borderRadius: Size.SIZE_100,
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: Spacing.SCALE_15,
        borderColor: 'red',
        backgroundColor: 'rgba(9, 128, 137, 1)',
    },
    img: {
        width: Size.SIZE_40,
        height: Size.SIZE_40,
        borderRadius: 100,
    },
    GiftIconWrapper: {
        backgroundColor: colors.tealRGBAColor,
        borderRadius: 50,
        width: 45,
        height: 45,
        alignItems: 'center',
        justifyContent: 'center',
    },
});
