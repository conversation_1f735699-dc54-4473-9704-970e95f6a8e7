import React, { useContext, useEffect, useState } from 'react';
import { View, TouchableOpacity, StyleSheet, Alert, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/core';

import { Check, Cross, Delete, Edit, Golf, Message, MessageDark, User } from '../../../assets/images/svg';
import { AuthContext } from '../../../context/AuthContext';
import TGText from '../../fields/TGText';
import CustomRM from './customRM';
import useClient from '../../../hooks/useClient';
import { UPDATE_USER } from '../../../graphql/mutations/user';
import DateRangeFieldNew1 from '../../fields/DateRangeFieldNew1';
import useQuery from '../../../hooks/useQuery';
import { FETCH_INTERNATIONAL_CLUB_DURATION, FETCH_LOACAL_CLUB_DURATION } from '../../../graphql/queries/clubDuration';
import { calculateGuestTimeRestrictionDates } from '../../../utils/helpers/CreateClubHelper';
import { handleTimeFormat } from '../../timeFormatComponent/handleTimeFormat';
import showToast from '../../toast/CustomToast';
import dateFormatter from '../../../utils/helpers/dateFormatter';
import FirstRequestLabel from '../../../screens/requests/FirstRequestLabel';
import { Spacing } from '../../../utils/responsiveUI';

export default function RequestCardNew({
    request,
    type,
    markGameCompleted,
    hasUnreadMessages,
    onDeclineRequest,
    acceptRequestModalState,
    onRefresh,
    rangeModal,
    setScreenLoader,
    _openRequestRefresh,
}) {
    const { user } = useContext(AuthContext);
    const navigation = useNavigation();
    const [acceptRequestModal, setAcceptRequestModal] = acceptRequestModalState;
    const [isAccepted, setIsAccepted] = useState(false);
    const [isVisible, setisVisible] = useState(false);
    const client = useClient();
    const [dateRangeModal, setDateRangeModal] = rangeModal;
    const [club, setClub] = useState();
    const [disabledDates, setDisabledDates] = useState();

    const handleModalClose = () => {
        setisVisible(false);
    };

    const handleContinue = () => {
        setisVisible(false);
        if (isAccepted) {
            client.request(UPDATE_USER, {
                user_id: user?.id,
                user: {
                    additional_settings: {
                        showAcceptRequestPopup: false,
                        showCreateRequestPopup: user?.additional_settings?.showCreateRequestPopup,
                    },
                },
            });
        }
        setTimeout(() => {
            acceptRequest();
        }, 100);
    };
    const showRequestPopup = () => {
        if (user?.additional_settings?.showAcceptRequestPopup) {
            setisVisible(true);
        } else acceptRequest();
    };

    async function acceptRequest() {
        Alert.alert(
            'Accept Request new',
            `Have you confirmed the logistics for #${request.game_id} with ${request?.requestor_full_name}?`,
            [
                {
                    text: 'Cancel',
                    onPress: () => {
                        Alert.alert(
                            'You must finalize the logistics before accepting the request',
                            '',
                            [
                                {
                                    text: 'Cancel',
                                    onPress: () => console.log('Cancel Pressed'),
                                    style: 'cancel',
                                },
                                {
                                    text: ` Chat with ${request?.requestor_full_name} `,
                                    onPress: async () => {
                                        goToChatScreen();
                                    },
                                },
                            ],
                            { cancelable: false },
                        );
                    },
                    style: 'cancel',
                },
                {
                    text: 'Yes',
                    onPress: async () => {
                        setAcceptRequestModal({ request, onRefresh });
                    },
                },
            ],
            { cancelable: false },
        );
    }

    async function showChatWarning(request) {
        Alert.alert(
            'Accepting Requests',
            `${
                !request?.has_messages && !request?.requester_has_message
                    ? 'Both host and requester must exchange at least one message to confirm logistics before accepting'
                    : 'You need to confirm the logistics with the requester via chat here before you can Accept'
            }`,
            [
                {
                    text: 'Cancel',
                    onPress: () => console.log('Cancel Pressed'),
                    style: 'cancel',
                },
                {
                    text: 'Start Chat',
                    onPress: () => goToChatScreen(),
                },
            ],
        );
    }

    function goToChatScreen() {
        const {
            sendbird_channel_id,
            request_id,
            game_id,
            requestor_user_id,
            requestor_full_name,
            host_user_id,
            stream_channel_id,
            has_messages,
        } = request;
        if (host_user_id && requestor_user_id && host_user_id !== requestor_user_id) {
            navigation.navigate('ChatDetails', {
                type,
                request_id,
                game_id,
                channel_url: sendbird_channel_id,
                streamChannelId: stream_channel_id,
                requestor_user_id,
                requestor_full_name,
                host_user_id,
                has_messages,
            });
        } else {
            showToast({});
        }
    }

    const requestDate =
        (request?.status === 'completed' || type.includes('accepted')) && request?.game_date
            ? handleTimeFormat(request?.game_date) || handleTimeFormat(request?.start_date)
            : dateFormatter(request?.start_date, request?.end_date);

    let localClub = useQuery(FETCH_LOACAL_CLUB_DURATION);

    let internationalClub = useQuery(FETCH_INTERNATIONAL_CLUB_DURATION);
    let month =
        club?.country_code == user?.phone_number_details?.countryCode
            ? localClub?.data?.system_setting[0]?.value?.value
            : internationalClub?.data?.system_setting[0]?.value?.value;

    useEffect(() => {
        setScreenLoader(true);
        const getClubDetails = async () => {
            const { view_club_search } = await client.request(`{
            view_club_search(where: {id: {_eq: ${request?.club_id}}}) {
                id
                name
                user_array
                guest_time_restrictions
                closure_period
                guest_fee,
                country_code
            }
        }`);
            const club = view_club_search?.length > 0 ? view_club_search[0] : null;
            setClub(club);
            if (club?.closure_period && month) {
                setScreenLoader(false);
                const dates = calculateGuestTimeRestrictionDates(
                    club?.closure_period,
                    club?.guest_time_restrictions,
                    month,
                );
                setDisabledDates(dates);
            }
        };
        getClubDetails();
        setScreenLoader(false);
    }, [month]);

    return (
        <>
            <TouchableOpacity
                onPress={() =>
                    navigation.navigate('Request Details', {
                        request,
                        actionType: type,
                        _onRefresh: onRefresh,
                        _openRequestRefresh,
                        _openRequestRefresh,
                    })
                }
                style={styles.container}>
                {request?.is_first_request && request?.requestor_user_id !== user?.id && <FirstRequestLabel />}
                <View
                    style={{
                        flexDirection: 'row',
                        marginTop: Spacing.SCALE_12,
                    }}>
                    <Golf />
                    <View style={{ flex: 1, marginLeft: 12 }}>
                        <TGText style={{ fontSize: 16, color: '#000000' }}>{request?.club_name}</TGText>
                        <TGText
                            style={{
                                fontSize: 12,
                                fontFamily: 'Ubuntu-Light',
                                marginTop: 5,
                            }}>
                            {requestDate}
                        </TGText>
                    </View>

                    <View style={styles.labelGameId}>
                        <TGText>#{request?.game_id}</TGText>
                    </View>
                    {type?.includes('received-accepted') &&
                        (!request?.requestor_completed ? (
                            <Pressable
                                style={{
                                    paddingLeft: 20,
                                    margin: 0,
                                    padding: 0,
                                    height: 20,
                                }}>
                                <DateRangeFieldNew1
                                    setModal={setDateRangeModal}
                                    club_id={request?.club_id}
                                    placeholder="Type date you want to play."
                                    start_date={request?.start_date}
                                    end_date={request?.end_date}
                                    game_id={request?.game_id}
                                    request_id={request?.request_id}
                                />
                            </Pressable>
                        ) : (
                            <></>
                        ))}
                </View>
                {!type.includes('requested-open') && (
                    <View
                        style={{
                            flexDirection: 'row',
                            marginVertical: 12,
                        }}>
                        <View
                            style={{
                                flex: 1,
                                flexDirection: 'row',
                                alignItems: 'center',
                            }}>
                            <User />
                            <TGText style={{ marginLeft: 12, color: '#000000' }}>
                                {request?.requestor_full_name ||
                                    request?.game_host_full_name ||
                                    user?.first_name + ' ' + user?.last_name}
                            </TGText>
                        </View>
                        {type.includes('history') && (
                            <TGText style={{ marginLeft: 12 }}>
                                Status:{' '}
                                <TGText
                                    style={{
                                        color: '#000000',
                                        textTransform: 'capitalize',
                                    }}>
                                    {request?.status}
                                </TGText>
                            </TGText>
                        )}
                    </View>
                )}
                <View style={{ flexDirection: 'row', marginTop: 16 }}>
                    <Pressable
                        onPress={() => {
                            if (!request?.host_completed && !request?.requestor_completed) onDeclineRequest();
                            else {
                                const message = request?.host_completed
                                    ? "This request can't be deleted as the host has already marked the game as complete"
                                    : "This Request can't be Declined as the Requestor has already marked the game as Completed.";
                                alert(message);
                            }
                        }}
                        style={[
                            styles.button,
                            {
                                marginRight: 5,
                                opacity: !request?.host_completed && !request?.requestor_completed ? 1 : 0.3,
                            },
                        ]}>
                        {type.includes('received') && !type.includes('history') ? <Cross /> : <Delete />}
                    </Pressable>
                    {!type.includes('history') && (
                        <TouchableOpacity
                            style={[
                                styles.button,
                                {
                                    flex: type.includes('accepted') ? 5 : 1,
                                    flexDirection: 'row',
                                    backgroundColor: type.includes('requested-open') ? '#EBEBEB' : '#2E9360',
                                    marginHorizontal: 5,
                                },
                            ]}
                            onPress={() => {
                                if (type.includes('requested-open'))
                                    navigation.navigate('Create Request', {
                                        request,
                                    });
                                else if (type.includes('received-open')) {
                                    if (request?.has_messages && request?.requester_has_message) {
                                        // acceptRequest();
                                        showRequestPopup();
                                    } else {
                                        showChatWarning(request);
                                    }
                                } else if (type.includes('accepted')) {
                                    markGameCompleted();
                                }
                            }}>
                            {type.includes('requested-open') ? <Edit /> : <Check />}
                            {type.includes('accepted') && (
                                <TGText
                                    style={{
                                        color: '#FFFFFF',
                                        fontSize: 14,
                                        fontFamily: 'Ubuntu-Regular',
                                    }}>
                                    {' '}
                                    {' Mark Completed'}
                                </TGText>
                            )}
                        </TouchableOpacity>
                    )}

                    <TouchableOpacity
                        onPress={() => {
                            if (type.includes('requested'))
                                navigation.navigate('Request Details', {
                                    request,
                                    actionType: type,
                                    _onRefresh: onRefresh,
                                });
                            else {
                                goToChatScreen();
                            }
                        }}
                        style={[
                            styles.button,
                            {
                                marginLeft: 5,
                                backgroundColor: request?.hosts || request?.has_messages ? '#098089' : '#EBEBEB',
                            },
                        ]}>
                        {request?.hosts || request?.has_messages ? <Message /> : <MessageDark />}
                        {hasUnreadMessages?.length > 0 && (
                            <View
                                style={{
                                    position: 'absolute',
                                    zIndex: 20,
                                    backgroundColor: 'red',
                                    height: 10,
                                    width: 10,
                                    borderRadius: 5,
                                    top: -2.5,
                                    right: -2.5,
                                }}
                            />
                        )}
                    </TouchableOpacity>
                </View>
            </TouchableOpacity>

            {/* Request modal  */}
            {user?.additional_settings?.showAcceptRequestPopup && (
                <CustomRM
                    isVisible={isVisible}
                    label={'Request'}
                    heading="Are you sure you want to accept the request?"
                    data={[
                        `This is a no-obligation system.  You don't have to accept anything that doesn't work for you`,
                        `Clarify that logistics work before accepting`,
                        `Learn enough about the requester so that you are sure you want to spend a few hours with them`,
                        `We are not big fans of "unaccompanied" play.  Accept if you can host.  If setting up unaccompanied play, remember that you are responsible for guest conduct`,
                    ]}
                    isChecked={isAccepted}
                    handleChecked={() => setIsAccepted(!isAccepted)}
                    handleContinue={handleContinue}
                    handleCancel={handleModalClose}
                    handleBack={handleModalClose}
                />
            )}
        </>
    );
}

const styles = StyleSheet.create({
    container: {
        marginHorizontal: 16,
        marginVertical: 8,
        alignItems: 'flex-start',
        borderRadius: 8,

        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,

        elevation: 3,
        padding: 16,
        backgroundColor: '#fff',
    },
    button: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#E05E52',
        borderRadius: 8,
        height: 35,
    },
    labelGameId: {
        borderBottomColor: '#808080',
        borderBottomWidth: 0.5,
        height: 20,
    },
});
