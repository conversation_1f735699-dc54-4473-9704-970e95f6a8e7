import React, { useContext, useEffect, useState } from 'react';
import {
    SafeAreaView,
    Text,
    View,
    TouchableOpacity,
    ActivityIndicator,
    Dimensions,
    StatusBar,
    StyleSheet,
    Platform,
} from 'react-native';
import { SideDrawerContext } from './SideNavigation';
import SearchIcon from '../../assets/svg/updatedSearchIcon.svg';
import MyFriendMapIcon from '../../assets/svg/MyFriendMapIcon.svg';
import FilterIcon from '../../assets/images/svg/icon-filter.svg';
import Cross from '../../assets/images/cross.svg';
import { Back } from '../../assets/images/svg';

import NotificationsIcon from '../../assets/images/notifications.svg';
import NotificationsActiveIcon from '../../assets/images/notifications-new.svg';
import SearchInput from '../search/SearchInput';
import TrippleDot from '../../assets/svg/TrippleDot.svg';
import { NotificationsContext } from './notifications/NotificationsContainer';
import { useSubscription } from 'react-apollo';
import gql from 'graphql-tag';
import { AuthContext } from '../../context/AuthContext';
import LogoNew from './LogoNew';
import CreatePostIcon from '../../assets/svg/AddPostIcon.svg';
import { colors } from '../../theme/theme';
import HeaderAddFriendIcon from '../../assets/svg/HeaderAddFriendIcon.svg';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import { useNavigation } from '@react-navigation/native';
import config from '../../config';
import { GlobalContext } from '../../context/contextApi';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ALL, CLUB_MEMBER_COUNT_FILTER } from '../../utils/constants/strings';

const { width } = Dimensions.get('window');

function MyTGFriendsScreenHeader({
    title,
    searchingState = [null, () => {}],
    filterState = [null, () => {}],
    showFilter = false,
    searchInputState = [null, () => {}],
    setResultsVisible = () => {},
    openFilterModal,
    searchLoading,
    disableSearch,
    onClick,
    popupState,
    headerName,
    setActiveSearchIcon,
    activeSearchIcon,
    showMapIcon,
    showSearchIcon = false,
    setOpenSearchBar = () => {},
    onTripleDotPress = () => {},
}) {
    const insets = useSafeAreaInsets();
    const openDrawer = useContext(SideDrawerContext);
    const openNotificationsDrawer = useContext(NotificationsContext);
    const { user } = useContext(AuthContext);
    const [searching, setSearching] = searchingState;
    const [searchInput, setSearchInput] = searchInputState;
    const [addFriendModal, setAddFriendModal] = popupState;
    const navigation = useNavigation();

    const [filter, setFilter] = filterState;
    const { actions } = useContext(GlobalContext);

    return (
        <>
            <StatusBar barStyle="dark-content" />
            <View style={[styles.safeAreaStyle, { paddingTop: Platform.OS === 'ios' ? Spacing.SCALE_30 : insets.top }]}>
                <View style={styles.container}>
                    <TouchableOpacity
                        onPress={() => {
                            navigation.goBack();
                        }}
                        style={styles.drawerIconWrapper}>
                        <Back height={25} />
                    </TouchableOpacity>
                    <Text style={styles.headerWrapper}>{title}</Text>
                    <View style={styles.box}>
                        {showFilter && (
                            <TouchableOpacity
                                onPress={() => {
                                    openFilterModal();
                                }}
                                style={{ marginRight: 10 }}>
                                <FilterIcon
                                    fill={filter ? colors.darkteal : colors.darkCharcoal}
                                    width={20}
                                    height={20}
                                />
                            </TouchableOpacity>
                        )}

                        {/* Search Icon */}
                        {showSearchIcon && (
                            <TouchableOpacity
                                onPress={() => {
                                    setOpenSearchBar((prev) => !prev);
                                }}
                                style={{ marginRight: 20 }}>
                                <SearchIcon width={20} height={20} />
                            </TouchableOpacity>
                        )}

                        {/** Add Friend Icon */}
                        <TouchableOpacity style={{ marginRight: 20 }} onPress={() => setAddFriendModal(true)}>
                            <CreatePostIcon />
                        </TouchableOpacity>

                        {/* Show map option in all friend header */}
                        {showMapIcon && (
                            <TouchableOpacity
                                onPress={() => {
                                    // set Map filter in map screen after redirection to update filter data
                                    actions.setMapFilterState({
                                        friendsAndContact: true,
                                        clubMemberCount: ALL,
                                        clubPercentage: ALL,
                                    });
                                    // Navigation method to navigate on map screen
                                    navigation.navigate(config.routes.BOTTOM_TAB_NAVIGATION, {
                                        screen: config.routes.CLUBS,
                                        params: { category: 'Friends' },
                                    });
                                }}
                                style={{ marginRight: 20 }}>
                                <MyFriendMapIcon height={20} width={20} />
                            </TouchableOpacity>
                        )}

                        {/* Map icon */}
                        <TouchableOpacity onPress={onTripleDotPress}>
                            <TrippleDot height={18} width={20} />
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </>
    );
}

export default MyTGFriendsScreenHeader;

const styles = StyleSheet.create({
    safeAreaStyle: {
        width: '100%',
        zIndex: 100,
        backgroundColor: 'white',
        paddingTop: Spacing.SCALE_10,
    },
    container: {
        width: '100%',
        alignItems: 'center',
        height: 60,
        flexDirection: 'row',
        paddingHorizontal: Spacing.SCALE_15,
    },
    drawerIconWrapper: {
        zIndex: 100,
    },
    headerWrapper: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_18,
        left: 70,
        right: 0,
        position: 'absolute',
        top: Platform.OS === 'ios' ? Spacing.SCALE_15 : Spacing.SCALE_17,
        fontWeight: '500',
        color: 'rgba(51, 51, 51, 1)',
        lineHeight: Size.SIZE_20,
    },
    box: {
        position: 'absolute',
        zIndex: 50,
        flexDirection: 'row',
        right: 15,
        top: 22,
        alignItems: 'center',
    },
});
