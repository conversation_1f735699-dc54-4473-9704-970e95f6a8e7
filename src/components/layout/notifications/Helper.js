import dynamicLinks from '@react-native-firebase/dynamic-links';
import PushNotification from 'react-native-push-notification';

import { requestDetailsURL } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';
import { ADMIN_USER_ID } from '../../../utils/constants/strings';
import config from '../../../config';
import rollbar from '../../../utils/rollbar/rollbar';
import { getStreamChannel } from '../../../screens/myTgGroup/helperFunctions/querryChannel';

export const ids = {
    request: 'request_id',
    event: 'event_id',
    offer: 'offer_id',
    'favorite-club-offer': 'offer_id',
    benefit: 'benefit_id',
};

export async function buildLink() {
    const link = await dynamicLinks().buildShortLink({
        link: 'https://thousandgreens-web-dev.herokuapp.com/dashboard/request/************************************/de41cdf4-615d-41e2-9619-d93658867a79?type=request-chat&id=ed397a32-9d35-478d-9ece-b530c7c89710&hostId=de41cdf4-615d-41e2-9619-d93658867a79&channelURL=sendbird_group_channel_132087195_c8d00d044183bc571ab265880bd8c2c700a86f6c',
        // domainUriPrefix is created in your Firebase console
        domainUriPrefix: 'https://thousandgreensdev.page.link',
        // optional setup which updates Firebase analytics campaign
        // "banner". This also needs setting up before hand
        analytics: {
            campaign: 'banner',
        },
    });
    console.log('buildLink', link);
    return link;
}

export function navigateToRequestChat(navigation, request_id, host_id, channel_url) {}

export function navigateToRequest(navigation, params, _onRefresh, actions, chatClient = null, setChannel = () => {}) {
    const { type = '', user_id, request_id, channel_url, data = true, host_id } = params;

    if (request_id)
        fetcher({
            method: 'POST',
            endpoint: requestDetailsURL,
            body: {
                user_id,
                request_id,
                data,
            },
        }).then((res) => {
            if (res?.code === 200) {
                actions?.setAppLoader(false);
                const request = res?.data[0];
                const actionType = res?.tab.replace('/', '-').toLowerCase();
                if (type === config.notificationTypeConstants.REQUEST_CHAT_NEW_MESSAGE || type === config.notificationTypeConstants.REQUEST_CHAT) {
                    try {
                        const { game_id, requestor_user_id, requestor_full_name, is_first_request = false } = request;

                        let host_user_id = request?.host_user_id;

                        if (request?.hosts && request?.hosts?.length) {
                            const host = request?.hosts?.filter((item) => {
                                return item?.sendbird_channel_id == channel_url;
                            });
                            host_user_id = host?.[0]?.host_user_id;
                        }

                        const requestParams = {
                            type,
                            request_id,
                            streamChannelId: channel_url,
                            requestor_user_id,
                            requestor_full_name,
                            host_user_id,
                            game_id,
                            is_first_request,
                            name: request?.club_name,
                        };
                        // create channel before navigation
                        const loadChannel = async () => {
                            await getStreamChannel(chatClient, host_id, channel_url, setChannel, actions);
                        };
                        loadChannel();

                        navigation.navigate('ChatDetails', requestParams);
                    } catch (error) {
                        rollbar.error('navigateToRequest -->', error);
                    }
                } else if (type === 'profile-details') {
                    navigation.navigate('Personal Profile');
                } else {
                    navigation.navigate('RequestDetailScreen', {
                        request: request,
                        type: actionType,
                        isMyRequest: request.requestor_user_id === user_id,
                    });
                    // navigation.navigate('Request Details', { request, actionType, _onRefresh });
                }
            } else {
                actions?.setAppLoader(false);
                alert(res.error);
                //   navigation.navigate('Requests')
            }
        });
    //temperary hide below code
    else {
        actions?.setAppLoader(false);
        if (params?.type == 'friend-request-accepted') {
            // This condition will call when new offer is created
            // Set current friend tab
            actions?.setFriendTabNavigation('All Friends');
            navigation.navigate(config.routes.MY_TG_FRIENDS_SCREEN, { mainTab: 0 });
        } else if (params?.type == 'friend-request-received') {
            actions?.setFriendTabNavigation('Received');
            navigation.navigate(config.routes.MY_TG_FRIENDS_SCREEN, { mainTab: 2 });
        } else if (params?.type == 'friend-request-declined') {
            actions?.setFriendTabNavigation('Sent');
            navigation.navigate(config.routes.MY_TG_FRIENDS_SCREEN, { mainTab: 2 });
        } else if (params?.type === 'favorite-club-offer') {
            navigation.navigate('Offers');
        } else if (params?.type === 'friend-joined-my-tg-group') {
            navigation.navigate('MyTgGroupInfo', { group: { streamId: params?.groupStreamId } });
        } else {
            // This condition will call when new offer is created
            actions.setCurrentTab(2);
            // Set current friend tab
            actions?.setFriendTabNavigation('Sent');
            navigation.navigate(config.routes.MY_TG_FRIENDS_SCREEN, { mainTab: 1 });
        }
    }
}

export const getRouteName = (type, id) => {
    if (type) {
        switch (type) {
            case 'event':
                return id ? 'Event Details' : 'Events';
            case 'benefit':
                return id ? 'Benefit Details' : 'Benefits';
            case 'offer':
            case 'favorite-club-offer':
                return 'Offers';
            case 'admin-profile-edit':
            case 'profile-details':
                return 'Personal Profile';
            case 'admin-clubs-edit':
            case 'club-information':
                return 'My Golf Clubs';
            case 'my-thousand-greens':
                return 'My Thousand Greens';
            default:
                return null;
        }
    } else return null;
};

export async function showNotification(message, client, user, state) {
    try {
        let messageData = {};
        let isMentionedYou;

        /**
         * This if else condition has been used to get to any user mention you in a message or not
         * If message contains message?.data?.id key then if condition will call and get message data from stream otherwise else condition will call
         */
        if (message?.data?.id) {
            messageData = await client?.getMessage(message?.data?.id);
            isMentionedYou =
                messageData?.message?.text?.includes(`@${user?.full_name}`) ||
                messageData?.message?.text?.includes(`@${user?.username}`);
        } else {
            let {
                notification: { body },
            } = message;
            isMentionedYou = body?.includes(`@${user?.full_name}`) || body?.includes(`@${user?.username}`);
        }

        // Do not send stream notification is the user is mentioned in the chat.
        // In case of mention, notification will be sent from firebase itself.
        if (message && message.notification) {
            if (isMentionedYou) {
                if (!message?.category) {
                    PushNotification.localNotification({
                        channelId: 'thousand-greens',
                        invokeApp: true,
                        alertAction: 'view',
                        category: '',
                        title: message.notification.title,
                        message: message.notification.body,
                        userInfo: message.data ? message.data : {},
                        playSound: false,
                        soundName: 'default',
                    });
                } else {
                    return true;
                }
            } else {
                PushNotification.localNotification({
                    channelId: 'thousand-greens',
                    invokeApp: true,
                    alertAction: 'view',
                    category: '',
                    title: message.notification.title,
                    message: message.notification.body,
                    userInfo: message.data ? message.data : {},
                    playSound: false,
                    soundName: 'default',
                });
            }
        } else {
            if (isMentionedYou) {
                return true;
            } else {
                const senderName =
                    messageData?.message?.user?.id === ADMIN_USER_ID
                        ? messageData?.message?.user?.name
                        : messageData?.message?.user?.username;

                PushNotification.localNotification({
                    channelId: 'thousand-greens',
                    invokeApp: true,
                    alertAction: 'view',
                    category: '',
                    title: 'New message from ' + senderName,
                    message: messageData.message.text,
                    userInfo: message.data ? message.data : {},
                    playSound: false,
                    soundName: 'default',
                });
            }
        }
    } catch (error) {
        console.log('error', error);
    }
}

export async function showNotificationInBackground(message, client, user, state) {
    // Do not send stream notification is the user is mentioned in the chat.
    // In case of mention, notification will be sent from firebase itself.
    try {
        let messageData = {};
        let isMentionedYou;

        /**
         * This if else condition has been used to get to any user mention you in a message or not
         * If message contains message?.data?.id key then if condition will call and get message data from stream otherwise else condition will call
         */
        if (message?.data?.id) {
            messageData = await client?.getMessage(message?.data?.id);
            isMentionedYou =
                messageData?.message?.text?.includes(`@${user?.full_name}`) ||
                messageData?.message?.text?.includes(`@${user?.username}`);
        } else {
            let {
                notification: { body },
            } = message;
            isMentionedYou = body?.includes(`@${user?.full_name}`) || body?.includes(`@${user?.username}`);
        }

        // Do not send stream notification is the user is mentioned in the chat.
        // In case of mention, notification will be sent from firebase itself.
        if (message && message.notification) {
            if (isMentionedYou) {
                if (!message?.category) {
                    PushNotification.localNotification({
                        channelId: 'thousand-greens',
                        invokeApp: true,
                        alertAction: 'view',
                        category: '',
                        title: message.notification.title,
                        message: message.notification.body,
                        userInfo: message.data ? message.data : {},
                        playSound: false,
                        soundName: 'default',
                    });
                } else {
                    return true;
                }
            } else {
                PushNotification.localNotification({
                    channelId: 'thousand-greens',
                    invokeApp: true,
                    alertAction: 'view',
                    category: '',
                    title: message.notification.title,
                    message: message.notification.body,
                    userInfo: message.data ? message.data : {},
                    playSound: false,
                    soundName: 'default',
                });
            }
        } else {
            if (isMentionedYou) {
                return true;
            } else {
                const senderName =
                    messageData?.message?.user?.id === ADMIN_USER_ID
                        ? messageData?.message?.user?.name
                        : messageData?.message?.user?.username;

                PushNotification.localNotification({
                    channelId: 'thousand-greens',
                    invokeApp: true,
                    alertAction: 'view',
                    category: '',
                    title: 'New message from ' + senderName,
                    message: messageData.message.text,
                    userInfo: message.data ? message.data : {},
                    playSound: false,
                    soundName: 'default',
                });
            }
        }
    } catch (error) {
        console.log('error', error);
    }
}
