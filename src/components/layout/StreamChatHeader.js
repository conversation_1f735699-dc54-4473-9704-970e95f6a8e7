import React, { useContext, useEffect, useMemo, useState } from 'react';
import {
    SafeAreaView,
    Text,
    View,
    TouchableOpacity,
    Dimensions,
    StatusBar,
    StyleSheet,
    Platform,
    Image,
} from 'react-native';
import { useSubscription } from 'react-apollo';
import { useNavigation } from '@react-navigation/native';
import gql from 'graphql-tag';
import CleverTap from 'clevertap-react-native';

import { SideDrawerContext } from './SideNavigation';
import FilterIcon from '../../assets/images/svg/icon-filter.svg';
import SearchIcon from '../../assets/svg/updatedSearchIcon.svg';
import { NotificationsContext } from './notifications/NotificationsContainer';
import { AuthContext } from '../../context/AuthContext';
import LogoNew from './LogoNew';
import NewGroupIconGrey from '../../assets/images/svg/NewGroupIconGrey.svg';
import PlusSquareIconBlack from '../../assets/svg/AddPostIcon.svg';
import MyFriendMapIcon from '../../assets/svg/MyFriendMapIcon.svg';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import NotificationsActiveIcon from '../../assets/images/notifications-new.svg';
import NotificationsIcon from '../../assets/images/notifications.svg';
import TrippleDot from '../../assets/svg/TrippleDot.svg';
import { GlobalContext } from '../../context/contextApi';
import { Back } from '../../assets/images/svg';
import { CHAT, GROUPS } from '../../utils/constants/strings';
import { colors } from '../../theme/theme';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import StreamHeader from './StreamHeader';
import constants, { tiers } from '../../utils/constants/constants';
import config from '../../config';

const FETCH_NOTIFICATIONS = gql`
    subscription fetchNotifications($user_id: uuid!) {
        notification(order_by: { created_at: desc }, where: { user_id: { _eq: $user_id } }) {
            created_at
            data
            id
            message
            type
            read
        }
    }
`;

const { width } = Dimensions.get('window');

function StreamChatHeader({
    title,
    searchingState = [null, () => {}],
    filterState = [null, () => {}],
    showFilter = false,
    searchInputState = [null, () => {}],
    openFilterModal,
    setSearchData,
    showTripleDot = true,
    screenName = '',
    showNotificationIcon = false,
    showSearchIcon = false,
    showPlusIcon = false,
    showMapIcon = false,
    onPlusIconPress = () => {},
    onMapIconPress = () => {},
    setOpenSearchBar = () => {},
    setOpenBottomSheet = () => {},
    handleToggleRecentPopup = () => {},
}) {
    const insets = useSafeAreaInsets();
    const openDrawer = useContext(SideDrawerContext);
    const openNotificationsDrawer = useContext(NotificationsContext);
    const { user } = useContext(AuthContext);
    const { actions, state } = useContext(GlobalContext);
    const navigation = useNavigation();

    const [filter, setFilter] = filterState;

    const { archiveState } = useMemo(() => {
        const { archiveState } = state;
        return { archiveState };
    }, [state.archiveState]);

    const { data: notifications } = useSubscription(FETCH_NOTIFICATIONS, {
        shouldResubscribe: true,
        variables: {
            user_id: user?.id,
        },
    });
    const [hasUnreadNotifications, setHasUnreadNotifications] = useState(false);

    useEffect(() => {
        if (notifications) {
            setHasUnreadNotifications(notifications.notification.filter(({ read }) => !read).length > 0);
        }
    }, [notifications]);

    const showSearch =
        [
            'Feed',
            'Offers',
            'My Pegboards',
            'Requests',
            'Create Post',
            'Edit Post',
            'Profile',
            'GroupChat',
            'Events',
            'FAQ',
            'My Contacts',
            'Clubs',
        ].indexOf(title) < 0;

    const handleOnPress = () => {
        if (screenName === GROUPS) {
            navigation.navigate('CreateGroupScreeen');
            setSearchData('');
        } else {
            setSearchData('');
            navigation.navigate('CreateNewChat');
            actions?.setCurrentScreenName(null);
        }
    };

    const showCreatePost = ['Feed'].indexOf(title) === 0;
    const handleProfilePress = () => {
        CleverTap.recordEvent(constants.CLEVERTAP.OWN_PROFILE_HEADER, {
            'User email': user?.email,
            'Current Account Status': user?.muted ? 'Muted' : 'Unmuted',
            Membership: user?.membership_plan?.name,
            Tier: tiers[user?.tier],
        });
        navigation.navigate(config.routes.SETTING);
    };

    if (archiveState) {
        return <StreamHeader screenName={`Archived Chat`} onPressGoBack={() => actions?.archiveListAction(false)} />;
    } else
        return (
            <>
                <StatusBar barStyle="dark-content" />
                <View
                    style={[
                        styles.safeAreaStyle,
                        { paddingTop: Platform.OS === 'ios' ? Spacing.SCALE_40 : insets.top + Spacing.SCALE_20 },
                    ]}>
                    <View style={styles.container}>
                        {title === CHAT ? (
                            <TouchableOpacity style={styles.profileIcon} onPress={handleProfilePress}>
                                {user?.profilePhoto ? (
                                    <Image source={{ uri: user?.profilePhoto }} style={styles.profileIcon} />
                                ) : (
                                    <Text
                                        style={{
                                            fontSize: Typography.FONT_SIZE_18,
                                            color: colors.whiteRGB,
                                            fontFamily: 'Ubuntu-Medium',
                                        }}>
                                        {user?.full_name[0]}
                                    </Text>
                                )}
                            </TouchableOpacity>
                        ) : (
                            <TouchableOpacity onPress={() => navigation.goBack()} style={styles.drawerIconWrapper}>
                                <Back />
                            </TouchableOpacity>
                        )}
                        <Text style={styles.headerWrapper}>{title}</Text>
                        <View style={styles.box}>
                            {/* Search Icon */}
                            {showSearchIcon && (
                                <TouchableOpacity
                                    onPress={() => {
                                        setOpenSearchBar((prev) => {
                                            if (prev) {
                                                setSearchData('');
                                            }
                                            return !prev;
                                        });
                                    }}
                                    style={{ marginRight: 20 }}>
                                    <SearchIcon width={20} height={20} />
                                </TouchableOpacity>
                            )}
                            {/* PlusSquar Icon */}
                            {showPlusIcon && (
                                <TouchableOpacity onPress={onPlusIconPress} style={{ marginRight: 20 }}>
                                    <PlusSquareIconBlack width={22} height={22} />
                                </TouchableOpacity>
                            )}
                            {/* Map Icon */}
                            {showMapIcon && (
                                <TouchableOpacity onPress={onMapIconPress}>
                                    <MyFriendMapIcon width={22} height={22} />
                                </TouchableOpacity>
                            )}

                            {/* Filter Icon */}
                            {showFilter && (
                                <TouchableOpacity
                                    onPress={() => {
                                        openFilterModal();
                                    }}
                                    style={{ marginRight: 20 }}>
                                    <FilterIcon fill={filter ? '#098089' : '#333'} width={20} height={20} />
                                </TouchableOpacity>
                            )}
                            <TouchableOpacity style={{ marginRight: !screenName ? 10 : 0 }} onPress={handleOnPress}>
                                {screenName === GROUPS ? null : <NewGroupIconGrey height={22} width={22} />}
                            </TouchableOpacity>
                            {showTripleDot && screenName !== GROUPS && (
                                <TouchableOpacity style={styles.trippleDotWrapper} onPress={handleToggleRecentPopup}>
                                    <TrippleDot height={Size.SIZE_20} width={Size.SIZE_5} />
                                </TouchableOpacity>
                            )}
                            {showNotificationIcon && (
                                <TouchableOpacity
                                    onPress={() => openNotificationsDrawer(true)}
                                    style={{ marginLeft: Spacing.SCALE_15 }}>
                                    {hasUnreadNotifications ? (
                                        <NotificationsActiveIcon height={23} width={23} />
                                    ) : (
                                        <NotificationsIcon height={23} width={23} />
                                    )}
                                </TouchableOpacity>
                            )}
                            {showTripleDot && screenName === GROUPS && (
                                <TouchableOpacity
                                    style={styles.trippleDotWrapperGroup}
                                    onPress={() => setOpenBottomSheet(true)}>
                                    <TrippleDot height={Size.SIZE_20} width={Size.SIZE_5} />
                                </TouchableOpacity>
                            )}
                        </View>
                    </View>
                </View>
            </>
        );
}

export default StreamChatHeader;

const styles = StyleSheet.create({
    safeAreaStyle: {
        width: '100%',
        zIndex: 100,
        backgroundColor: 'rgba(255, 255, 255, 1)',
    },
    container: {
        width: '100%',
        alignItems: 'center',
        height: 60,
        flexDirection: 'row',
        paddingHorizontal: 15,
    },
    archiveContainer: {
        width: '100%',
        alignItems: 'center',
        height: Platform.OS === 'android' ? 60 : Size.SIZE_80,
        flexDirection: 'row',
        paddingHorizontal: Spacing.SCALE_15,
    },
    drawerIconWrapper: {
        zIndex: 100,
    },
    archiveDrawerIconWrapper: {
        zIndex: 100,
        marginTop: Platform.OS === 'android' ? 0 : Spacing.SCALE_20,
    },
    headerWrapper: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_18,
        left: Spacing.SCALE_70,
        right: 0,
        position: 'absolute',
        top: Platform.OS === 'ios' ? Spacing.SCALE_15 : Spacing.SCALE_15,
        fontWeight: '500',
        color: colors.lightBlack,
        textAlign: 'auto',
    },
    archiveHeaderWrapper: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_18,
        left: 60,
        right: 0,
        position: 'absolute',
        top: Platform.OS === 'ios' ? Spacing.SCALE_40 : Spacing.SCALE_15,
        fontWeight: '500',
        color: colors.lightBlack,
    },
    box: {
        position: 'absolute',
        zIndex: 50,
        flexDirection: 'row',
        right: 15,
        top: 22,
        alignItems: 'center',
        flex: 1,
        width: 'auto',
        justifyContent: 'space-around',
    },
    trippleDotWrapper: {
        marginRight: Spacing.SCALE_10,
        paddingLeft: 10,
    },
    trippleDotWrapperGroup: {
        marginLeft: Spacing.SCALE_20,
        marginRight: Spacing.SCALE_10,
    },
    profileIcon: {
        width: Size.SIZE_34,
        height: Size.SIZE_34,
        borderRadius: Size.SIZE_50,
        backgroundColor: colors.tealRgb,
        alignItems: 'center',
        justifyContent: 'center',
    },
});
