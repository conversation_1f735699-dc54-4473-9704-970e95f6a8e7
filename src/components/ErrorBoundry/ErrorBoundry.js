import React, { Component } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import rollbar from '../../utils/rollbar/rollbar';
import NewRelic from 'newrelic-react-native-agent';
import { colors } from '../../theme/theme';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';

class ErrorBoundary extends Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        // Send to Rollbar (existing)
        rollbar.error(error, errorInfo);

        // Send to New Relic
        try {
            NewRelic.recordError('React Error Boundary', {
                message: error.message,
                stack: error.stack,
                componentStack: errorInfo.componentStack,
                level: 'ERROR',
                source: 'ErrorBoundary'
            });
        } catch (e) {
            console.error('Failed to send error to New Relic:', e);
        }
    }

    render() {
        if (this.state.hasError) {
            return (
                <View
                    style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: Spacing.SCALE_16
                    }}>
                    <Text style={styles.headline}>Oops! Something went wrong</Text>
                    <Text style={styles.description}>We're having trouble loading this page. Please try again.</Text>
                </View>
            );
        }

        return this.props.children;
    }
}

export default ErrorBoundary;

const styles = StyleSheet.create({
    headline: {
        padding: Spacing.SCALE_10,
        marginBottom: Spacing.SCALE_10,
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_21,
        fontWeight: '500',
        color: colors.fadeBlack,
        lineHeight: Typography.FONT_SIZE_14,
    },
    description: {
        textAlign: 'center',
        marginBottom: Spacing.SCALE_20,
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '400',
        color: colors.fadeBlack,
    },
});
