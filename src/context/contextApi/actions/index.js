export const AppActions = (dispatch) => ({
    selectedFriendsAction: async (data) => {
        dispatch({ type: 'SELECTED_FRIENDS', data });
    },
    blockUserResAction: async (data) => {
        dispatch({ type: 'BLOCKE_USER', data });
    },
    updatedChannelAction: async (data) => {
        dispatch({ type: 'UPDATED_CHANNEL', data });
    },
    channelMembersAction: async (data) => {
        dispatch({ type: 'CHANNEL_MEMBERS', data });
    },
    userProfile: async (data) => {
        dispatch({ type: 'USER_PROFILE', data });
    },
    archiveListAction: async (data) => {
        dispatch({ type: 'SHOW_ARCHIVE_LIST', data });
    },
    isMySelfRemoveAction: async (data) => {
        dispatch({ type: 'IS_MY_SELF_REMOVE', data });
    },
    selectedMessageIdAction: async (data) => {
        dispatch({ type: 'SELECTED_MESSAGE_ID', data });
    },
    setOtherUser: async (data) => {
        dispatch({ type: 'SET_OTHER_USER', data });
    },
    setCurrentScreenName: async (data) => {
        dispatch({ type: 'SET_CURRENT_SCREEN_NAME', data });
    },
    setClubDetails: async (data) => {
        dispatch({ type: 'SET_CLUB_DETAILS', data });
    },
    setSelectedClub: async (data) => {
        dispatch({ type: 'SET_SELECTED_CLUB', data });
    },
    setAllClubs: async (data) => {
        dispatch({ type: 'SET_ALL_CLUBS', data });
    },
    setBoundariesChangeClubs: async (data) => {
        dispatch({ type: 'SET_BOUNDARIES_CHANGE_CLUBS', data });
    },
    setAllClubsData: async (data) => {
        dispatch({ type: 'SET_ALL_CLUBS_DATA', data });
    },
    setMyTgGroupDetails: async (data) => {
        dispatch({ type: 'SET_MY_TG_GROUP_DETAILS', data });
    },
    setMyTgGroupMembers: async (data) => {
        dispatch({ type: 'SET_MY_TG_GROUP_MEMBERS', data });
    },
    setAllFriendsId: async (data) => {
        dispatch({ type: 'SET_ALL_FRIENDS_ID', data });
    },
    setRefreshPage: async (data) => {
        dispatch({ type: 'SET_REFRESH_PAGE', data });
    },
    setPendingRequests: async (data) => {
        dispatch({ type: 'SET_PENDING_JOIN_REQUESTS', data });
    },
    setUnreadChannels: async (data) => {
        dispatch({ type: 'SET_UNREAD_CHANNELS', data });
    },
    setRequestsCount: async (data) => {
        dispatch({ type: 'SET_REQUEST_COUNT', data });
    },
    setUnreadMessageStatus: async (data) => {
        dispatch({ type: 'SET_UNREAD_MESSAGE_STATUS', data });
    },
    setFavClub: async (data) => {
        dispatch({ type: 'SET_FAV_CLUB', data });
    },
    setCurrentTab: async (data) => {
        dispatch({ type: 'SET_CURRENT_TAB', data });
    },
    setMapFilterState: async (data) => {
        dispatch({ type: 'SET_MAP_FILTER', data });
    },
    setClubName: async (data) => {
        dispatch({ type: 'SET_CLUB_NAME', data });
    },
    setFriendTabNavigation: async (data) => {
        dispatch({ type: 'SET_FRIEND_TAB', data });
    },
    setSeeOfferClub: async (data) => {
        dispatch({ type: 'SET_SEE_OFFER_CLUB', data });
    },
    setMapCurrentState: async (data) => {
        dispatch({ type: 'SET_MAP_CURRENT_STATE', data });
    },
    setMapCurrentFilter: async (data) => {
        dispatch({ type: 'SET_MAP_CURRENT_FILTER', data });
    },
    setOpenAllFriendSearchBar: async (data) => {
        dispatch({ type: 'SET_OPEN_ALL_FRIEND_SEARCH', data });
    },
    setAllMemberScreenLoader: async (data) => {
        dispatch({ type: 'SET_ALL_MEMBER_SCREEN_LOADER', data });
    },
    setMapCurrentClub: async (data) => {
        dispatch({ type: 'SET_MAP_CURRENT_CLUB', data });
    },
    setComeFromNotification: async (data) => {
        dispatch({ type: 'SET_COME_FROM_NOTIFICATION', data });
    },
    setPegboardDetails: async (data) => {
        dispatch({ type: 'SET_PEGBOARD_DETAILS', data });
    },
    setZoomLevelState: async (data) => {
        dispatch({ type: 'SET_ZOOM_LEVEL', data });
    },
    setIsFirstTimeInMap: async (data) => {
        dispatch({ type: 'SET_FIRST_TIME_IN_MAP', data });
    },
    setMyClubs: async (data) => {
        dispatch({ type: 'SET_MY_CLUBS', data });
    },
    setChannelOnLongPress: async (data) => {
        dispatch({ type: 'SET_LONG_PRESS_CHANNEL', data });
    },
    setDeleteChannelPopupState: async (data) => {
        dispatch({ type: 'SET_DELETE_CHANNEL_POPUP', data });
    },
    setDeletePopupLoader: async (data) => {
        dispatch({ type: 'SET_DELETE_POPUP_LOADER', data });
    },
    setCountry: async (data) => {
        dispatch({ type: 'SET_COUNTRY', data });
    },
    setState: async (data) => {
        dispatch({ type: 'SET_STATE', data });
    },
    setCity: async (data) => {
        dispatch({ type: 'SET_CITY', data });
    },
    setIsCrossIconToggle: async (data) => {
        dispatch({ type: 'SET_CROSS_ICON_TOGGLE', data });
    },
    setPoll: async (data) => {
        dispatch({ type: 'SET_POLL_DATA', data });
    },
    setGuideStep: async (data) => {
        dispatch({ type: 'SET_GUIDE_STEP', data });
    },
    setStartGuide: async (data) => {
        dispatch({ type: 'SET_START_GUIDE', data });
    },
    setNotifications: async (data) => {
        dispatch({ type: 'SET_NOTIFICATIONS', data });
    },
    setStats: async (data) => {
        dispatch({ type: 'SET_STATS', data });
    },
    setMutePopup: async (data) => {
        dispatch({ type: 'SET_MUTE_POPUP', data });
    },
    setIsFirstTimeVisitHome: async (data) => {
        dispatch({ type: 'SET_IS_FIRST_TIME_VISIT_HOME', data });
    },
    setIsMenuBottomSheetOpen: async (data) => {
        dispatch({ type: 'SET_MENU_BOTTOM_SHEET_OPEN', data });
    },
    setMenuBottomSheetOpenIndex: async (data) => {
        dispatch({ type: 'SET_MENU_BOTTOM_SHEET_OPEN_INDEX', data });
    },
    setExpanded: async (data) => {
        dispatch({ type: 'SET_EXPANDED_NOTIFICATION', data });
    },
    setTealDotStatus: async (data) => {
        dispatch({ type: 'SET_TEAL_DOT_STATUS', data });
    },
    setAppLoader: async (data) => {
        dispatch({ type: 'SET_APP_LOADER', data });
    },
    setSelectedOffer: async (data) => {
        dispatch({ type: 'SET_SELECTED_OFFER', data });
    },
    setOfflineLogGameStep: async (data) => {
        dispatch({ type: 'SET_OFFLINE_LOG_GAME_STEP', data });
    },
    setReceivedOpen: async (data) => {
        dispatch({ type: 'SET_RECEIVED_OPEN', data });
    },
    setRequestedOpen: async (data) => {
        dispatch({ type: 'SET_REQUESTED_OPEN', data });
    },
    setRequestedAccepted: async (data) => {
        dispatch({ type: 'SET_REQUESTED_ACCEPTED', data });
    },
    setReceivedAccepted: async (data) => {
        dispatch({ type: 'SET_RECEIVED_ACCEPTED', data });
    },
    setAllAcceptedRequests: async (data) => {
        dispatch({ type: 'SET_ALL_ACCEPTED_REQUESTS', data });
    },
    setAppSkeltonLoader: async (data) => {
        dispatch({ type: 'SET_APP_SKELTON_LOADER', data });
    },
    setAllRequestHostsData: async (data) => {
        dispatch({ type: 'SET_ALL_REQUEST_HOSTS_DATA', data });
    },
    setUnreadChannelsObject: async (data) => {
        dispatch({ type: 'SET_UNREAD_CHANNELS_OBJECT', data });
    },
    setIsMapViewExpanded: async (data) => {
        dispatch({ type: 'SET_IS_MAP_VIEW_EXPANDED', data });
    },
    setScreen: async (data) => {
        dispatch({ type: 'SET_SCREEN', data });
    },
    setIsMapSearchActive: async (data) => {
        dispatch({ type: 'SET_IS_MAP_SEARCH_ACTIVE', data });
    },
    setMapBoundaries: async (data) => {
        dispatch({ type: 'SET_MAP_BOUNDARIES', data });
    },
    setGameReview: async (data) => {
        dispatch({ type: 'SET_GAME_REVIEW', data });
    },
    setShouldDetailBottomSheetOpen: async (data) => {
        dispatch({ type: 'SET_SHOULD_DETAIL_BOTTOM_SHEET_OPEN', data });
    },
    setFilterActive: async (data) => {
        dispatch({ type: 'SET_FILTER_ACTIVE', data });
    },
    setHomeScreenPopupState: async (data) => {
        dispatch({ type: 'HOME_SCREEN_POPUP_STATE', data });
    },
});
