import gql from 'graphql-tag';


const FETCH_USER = `
query getUser($user_id: uuid) {
  user(where: {id: {_eq: $user_id}}) {
    id
    net_games_value
    is_tg_founder
    membership_plan_id
    membership_plan{
      name
      key
    }
    membership_active
    username
    show_visibility_popup
    membership_expires_on
    created_at
    last_muted_prompt
    deleted_at
    annual_confirmation
    visited_account_settings
    is_tutorial_viewed
    show_visibility_popup_time
    birthYear
    email
    englishFluency
    facebook
    gender
    linkedin
    pace
    handicap
    about_yourself
    full_name
    first_name
    last_name
    playAsCouple
    phone
    phone_number_details
    tier
    profilePhoto
    tokens
    last_active_session
    profile_complete
    registration_complete
    account_activated
    visibleInNetwork
    visibleToPublic
    tierVisibility
    visibleToLowerTiers
    is_legacy_user
    legacy_password_reset
    notificationSettings
    verificationMethod
    muted
    opt_in_email
    verified
    declined
    opt_in_email
    mute_other_notifications
    association_id
    user_association_number
    ftr_count
    is_tg_ambassador
    tg_ambassador_visibility
    stripe_customer_info
    yearly_club_validation
    association {
      id
      name
    }
    additional_settings
    profilePublic
    socialPublic
    mute_until
    visible_to_favorite_clubs
    verified_on
    signup_current_step
    favorite_clubs {
      club_id
    }
    clubs(where: {club: {club_type: {_eq: 0}}}) {
      proximity
      visible_to_non_friends
      paymentMethod
      otherInstructions
      muted
      restricted_membership
      is_visibile_to_lower_tiers
      visible_to_tiers
      favorite_restricted
      club_id
      gender_preference
      is_yearly_validated
      is_replaced
      visibleInNetwork
      club {
        id
        name
        hasGuestRestrictions
        has_membership_privileges
        guest_time_restrictions
        guestFee
        dressCode
        caddieRequired
        caddieFee
        notes
        new
        lowest_visible_tier
        closurePeriods
        club_demand_type
      }
    }
    playedClubs
    referral
  }
}
`;
const GET_USER_SUBS = gql`
subscription getUser($user_id: uuid) {
  user(where: {id: {_eq: $user_id}}) {
    id
    username
    created_at
    deleted_at
    annual_confirmation
    birthYear
    email
    englishFluency
    facebook
    gender
    linkedin
    pace
    handicap
    about_yourself
    first_name
    last_name
    playAsCouple
    phone
    phone_number_details
    tier
    profilePhoto
    tokens
    last_active_session
    profile_complete
    registration_complete
    account_activated
    visibleInNetwork
    visibleToPublic
    tierVisibility
    visibleToLowerTiers
    is_legacy_user
    legacy_password_reset
    notificationSettings
    verificationMethod
    muted
    verified
    declined
    additional_settings
    visible_to_favorite_clubs
    verified_on
    signup_current_step
    mute_other_notifications
    association_id
    user_association_number
    association {
      id
      name
    }
    profilePublic
    socialPublic
    mute_until
    favorite_clubs {
      club_id
    }
    clubs {
      proximity
      paymentMethod
      otherInstructions
      muted
      restricted_membership
      favorite_restricted
      club_id
      club {
        id
        name
        hasGuestRestrictions
        has_membership_privileges
        guest_time_restrictions
        guestFee
        dressCode
        caddieRequired
        caddieFee
        notes
        new
        lowest_visible_tier
        closurePeriods,
      }
    }
    playedClubs
  }
}
`;

export { FETCH_USER, GET_USER_SUBS };
