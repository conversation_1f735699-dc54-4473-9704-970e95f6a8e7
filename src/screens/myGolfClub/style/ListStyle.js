import {StyleSheet} from 'react-native';

import {colors} from '../../../theme/theme';
import {Size, Spacing, Typography} from '../../../utils/responsiveUI';

export default StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.darkteal,
    },
    listContainer: {
        flex: 1,
        backgroundColor: colors.whiteF6,
        borderTopRightRadius: Size.SIZE_16,
        borderTopLeftRadius: Size.SIZE_16,
        marginTop: Spacing.SCALE_16,
    },
    cardContainer: {
        flex: 1,
        paddingHorizontal: Spacing.SCALE_16,
        borderRadius: Size.SIZE_8,
        marginBottom: Spacing.SCALE_12,
        backgroundColor: colors.whiteColor,
        height: Size.SIZE_74,
        shadowColor: colors.shadowColor1,
        shadowOffset: {width: 0, height: 3},
        shadowOpacity: 0.2,
        shadowRadius: 2,
        elevation: 5,
        justifyContent: 'center',
        flexDirection: 'row',
        alignItems: 'center',
    },
    listStyle: {
        justifyContent: 'center',
        paddingHorizontal: Spacing.SCALE_16,
        flexDirection: 'row',
        borderRadius: Size.SIZE_8,
        marginBottom: Spacing.SCALE_12,
        backgroundColor: colors.whiteColor,
        height: Size.SIZE_74,
        alignItems: 'center',
        // shadow
        shadowColor: colors.shadowColor1,
        shadowOffset: {width: 0, height: 3},
        shadowOpacity: 0.2,
        shadowRadius: 2,
        elevation: 5,
    },
    listClubMemberStyle: {
        justifyContent: 'center',
        flexDirection: 'row',
        backgroundColor: colors.whiteColor,
        alignItems: 'center',
    },
    font18: {
        fontSize: Typography.FONT_SIZE_18,
        fontFamily: 'Ubuntu-Medium',
    },
    font14: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        color: colors.rgbaBlack,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '400',
        textTransform: 'capitalize',
    },
    font16: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '500',
        lineHeight: Size.SIZE_14,
    },
    font12: {
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Medium',
        color: colors.darkGreyRgba,
    },
    font28: {
        fontSize: Typography.FONT_SIZE_28,
        fontFamily: 'Ubuntu-Medium',
    },
    spacing6: {
        marginBottom: Spacing.SCALE_6,
    },
    btnContainer: {
        alignItems: 'center',
        paddingBottom: 16,
    },
    detailsContainer: {
        flex: 1,
        paddingVertical: Spacing.SCALE_12,
    },
    clubDetailsValue: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.lightBlack,
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_15,
    },
    clubDetailsLabel: {
        fontSize: Typography.FONT_SIZE_12,
        color: '#666666',
        fontFamily: 'Ubuntu-Regular',
        marginRight: Spacing.SCALE_8,
    },
    detailsLabelView: {
        flexDirection: 'row',
        marginBottom: Spacing.SCALE_8,
        alignItems: 'center',
    },
    toolTipIcon: {
        height: Size.SIZE_14,
        width: Size.SIZE_14,
    },
    infoContainer: {
        flexDirection: 'row',
        paddingVertical: Spacing.SCALE_11,
        paddingHorizontal: Spacing.SCALE_18,
        backgroundColor: '#F6F6F6',
        alignItems: 'center',
    },
    infoLabel: {
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
        fontWeight: '500',
        marginRight: Spacing.SCALE_4,
    },
    inputBoxText: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_14,
        fontFamily: 'Ubuntu-Medium',
    },
    title: {textAlign: 'center', fontSize: 18, color: '#333', marginBottom: 15},
    buttonSave: {
        width: '40%',
        height: Size.SIZE_45,
        backgroundColor: colors.darkteal,
        justifyContent: 'center',
        alignItems: 'center',
        color: 'white',
        borderRadius: 8,
    },
    buttonCancel: {
        backgroundColor: colors.lightgray,
        marginRight: 10,
        marginLeft: 0,
    },
    infoLabelNew: {
        fontSize: Typography.FONT_SIZE_16,
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
        fontWeight: '500',
        marginRight: Spacing.SCALE_4,
    },
    badge: {
        maxWidth: Size.SIZE_120,
        height: Size.SIZE_22,
        backgroundColor: colors.badgeLabelBackgroundColor,
        borderRadius: Size.SIZE_7,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: Spacing.SCALE_9,
    },
    badgeText: {
        textAlign: 'center',
        color: colors.tealRgb,
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_10,
        lineHeight: Size.SIZE_10,
        fontWeight: '700',
        paddingHorizontal: Spacing.SCALE_1,
    },
    box2: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    golfIconWrapper: {
        flex: 0.1,
    },
    clubNameWrapper: {
        flex: 0.8,
    },
    arrowIconWrapper: {
        flex: 0.1,
        alignItems: 'flex-end',
        justifyContent: 'center',
    },
    contentContainerStyle: {
        alignContent: 'center',
        paddingTop: Spacing.SCALE_16,
    },
    tealBtnStyle: {
        width: '90%',
        height: Size.SIZE_45,
        paddingVertical: 0,
        borderRadius: Size.SIZE_8,
    },
    tealBtnLabelStyle: {
        fontSize: Typography.FONT_SIZE_16,
    },
    whiteBtnStyle: {
        width: '90%',
        backgroundColor: colors.white,
        borderWidth: 1,
        borderColor: colors.dark_charcoal,
        paddingVertical: 0,
        borderRadius: Size.SIZE_8,
        height: Size.SIZE_45,
    },
    whiteBtnLabelStyle: {
        fontSize: Typography.FONT_SIZE_16,
        color: colors.dark_charcoal,
    },
    toggleTestStyle: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_20,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.dark_charcoal,
    },
    toggleTextWrapperStyle: {
        width: Size.SIZE_260,
    },
    toggleBtnWrapper: {
        paddingHorizontal: Spacing.SCALE_16,
        paddingVertical: Spacing.SCALE_20,
        flexDirection: 'row',
        justifyContent: 'space-between',
        backgroundColor: colors.white,
    },
    membersCountText: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.rgbaBlack,
        fontFamily: 'Ubuntu-Medium',
        marginRight: Spacing.SCALE_8,
        fontWeight: '500',
        lineHeight: Size.SIZE_17,
    },
    toggleOffText: {
        color: colors.greyRgb,
        marginTop: Spacing.SCALE_10,
        paddingHorizontal: Spacing.SCALE_50,
        textAlign: 'center',
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_18,
    },
    otherMemberFoundStyle: {
        color: colors.rgbaBlack,
        marginTop: Spacing.SCALE_15,
        paddingHorizontal: Spacing.SCALE_50,
        textAlign: 'center',
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_17,
    },
    toggleOfMemberContainer: {
        backgroundColor: colors.greyRgba,
        justifyContent: 'center',
        height: Size.SIZE_60,
        alignItems: 'center',
        paddingBottom: 0,
    },
    box: {
        justifyContent: 'center',
        width: '100%',
        height: '100%',
        alignItems: 'center',
    },
    imageWrapper: {
        flex: 0.14,
        height: Size.SIZE_40,
        borderRadius: Size.SIZE_4,
        justifyContent: 'center',
        alignItems: 'center',
        width: Size.SIZE_40,
    },
    profileImageStyle: {
        height: Size.SIZE_40,
        width: Size.SIZE_40,
        borderRadius: Size.SIZE_4,
        backgroundColor: colors.lightgray,
        justifyContent: 'center',
        alignItems: 'center',
    },
    card: {
        flex: 0.8,
        marginLeft: Spacing.SCALE_10,
    },
    cardBody: {
        flexDirection: 'row',
        marginTop: Size.SIZE_5,
    },
    callIconWrapper: {
        flexDirection: 'row',
        flex: 0.4,
    },
    emailIconWrapper: {
        flexDirection: 'row',
        flex: 0.6,
    },
    emailIconStyle: {
        marginLeft: Size.SIZE_10,
    },
    separateLineContainer: {
        paddingHorizontal: Spacing.SCALE_16,
        paddingVertical: Spacing.SCALE_15,
    },
    separateLineStyle: {
        backgroundColor: colors.gray99,
        height: 1,
        width: '100%',
        opacity: 0.2,
    },
    dividerLine: {
        backgroundColor: colors.lineDividerColor,
        height: Size.SIZE_1,
    },
    titleStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_16,
        fontWeight: '500',
        color: colors.dark_charcoal,
    },
    lowerTierTextWrapper: {
        paddingHorizontal: Spacing.SCALE_10,
    },
    lowerTierText: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_18,
        fontWeight: '400',
        color: colors.darkGreyRgba,
    },
    toggleBtnStyle: {
        paddingHorizontal: Spacing.SCALE_10,
        paddingBottom: Spacing.SCALE_10,
    },
    checkBoxTitleStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_14,
        fontWeight: '400',
        color: colors.dark_charcoal,
    },
    headerTitleStyle: {
        marginLeft: Spacing.SCALE_10,
    },
    favWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: Spacing.SCALE_15,
        marginTop: Spacing.SCALE_10,
    },
    favTextWrapper: {
        width: '90%',
    },
    favTextStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_22,
        fontWeight: '500',
        color: colors.dark_charcoal,
    },
    footerTextWrapper: {
        paddingHorizontal: Spacing.SCALE_15,
        marginTop: Spacing.SCALE_5,
    },
    footerTextStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_18,
        fontWeight: '400',
        color: colors.darkGreyRgba,
    },
    genderPreferenceWrapper: {
        marginTop: Spacing.SCALE_20,
        paddingHorizontal: Spacing.SCALE_15,
    },
    genderPreferenceText: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_16,
        fontWeight: '500',
        color: colors.lightBlack,
    },
    radioBtnWrapper: {
        padding: Spacing.SCALE_15,
    },
    radioBtnContainer: {
        borderRadius: Size.SIZE_8,
        justifyContent: 'center',
        paddingVertical: Spacing.SCALE_5,
        margin: Spacing.SCALE_5,
    },
});
