import { useNavigation } from '@react-navigation/core';
import React, { useState, useEffect, useRef, useContext } from 'react';
import { View, Text, ScrollView, ActivityIndicator } from 'react-native';
import Moment from 'moment';
import { useIsFocused } from '@react-navigation/native';

import { colors } from '../../../theme/theme';
import { Spacing } from '../../../utils/responsiveUI';
import styles from '../style/ListStyle';
import ShowToolTip from '../ToolTip';
import { AuthContext } from '../../../context/AuthContext';
import ProfileHeader from '../../../components/layout/ProfileHeader';
import AccountToggleButton from '../../../components/buttons/AccountToggleButton';
import Spacer from '../../../components/layout/Spacer';
import CheckboxNew from '../../../forms/profile/views/CheckboxNew';
import { CLUB_LOWER_TIER_VISSIBILITY, FAVOURITE_RESTRICTED, favListURL } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';
import { updateUserDetails } from '../../../forms/profile/actions';
import { UPDATE_USER } from '../../../graphql/mutations/user';
import useClient from '../../../hooks/useClient';
import GET_USER_CLUBS from '../../../graphql/queries/getUserClubs';
import useQuery from '../../../hooks/useQuery';
import {
    GENDER_PREFERENCE_HEADER,
    GENDER_PREFERENCE_SUBHEADER,
    LOWER_TIER_TEXT,
    RECEIVED_REQUEST_ONLY_TEXT,
} from '../../../utils/constants/strings';
import MuteClubComponent from './MuteClubComponent';
import MuteModalNew from '../../../components/modals/MuteModalNew';
import ToggleButton from '../../../components/buttons/ToggleButton';
import { FETCH_SYSTEM_SETTING } from '../../../graphql/queries/requests';
import TGAlert from '../../../components/fields/TGAlert';
import config from '../../../config';
import { TouchableWithoutFeedback } from 'react-native';
import CustomRadioButton from '../../../components/buttons/CustomRadioButton';
import { updateClubPreferences } from '../action/updateClubPreferences';

export default (props) => {
    const { clubName, clubId } = props?.route?.params;
    let data = props?.route?.params?.clubDetails?.club?.lowest_visible_tier;
    var clubTier1 = data == 1 ? 'Fern' : data == 2 ? 'Sage' : data == 3 ? 'Moss' : 'Olive';
    const { user, refreshUser } = useContext(AuthContext);
    let getUserClubs = useQuery(GET_USER_CLUBS);
    let myClub = getUserClubs?.data?.user_club;
    const navigation = useNavigation();
    const [club, setClub] = useState({});
    const [timeDescriptions, setTimeDescriptions] = useState([]);
    const [sageCheckBox, setSageCheckBox] = useState(true);
    const [mossCheckBox, setMossCheckBox] = useState(false);
    const [oliveCheckBox, setOliveCheckBox] = useState(false);
    const [settings, setSettings] = useState();
    const [request, setRequest] = useState({});
    const [needUpdateUser, setNeedUpdateUser] = useState(false);
    const [clubTier, setClubTier] = useState(props?.route?.params?.clubDetails?.club?.lowest_visible_tier);
    const [visibleToTiers, setVisibleToTiers] = useState(props?.route?.params?.clubDetails?.visible_to_tiers);
    const [MOSS, setMOSS] = useState(3);
    const [OLIVE, setOLIVE] = useState(5);
    const [updatedClub, setUpdatedClub] = useState();
    const [loading, setLoading] = useState(false);
    const [muteModalOpen, setMuteModalOpen] = useState(false);
    const [minFavClub, setMinFavClub] = useState(null);
    const [totalFavClub, setTotalFavClub] = useState(0);
    const [filter, setFilter] = useState();
    const client = useClient();
    const isFocused = useIsFocused();

    const dayLabels = {
        mon: 'Monday',
        tues: 'Tuesday',
        wed: 'Wednesday',
        thurs: 'Thursday',
        fri: 'Friday',
        sat: 'Saturday',
        sun: 'Sunday',
    };
    const [toolTip, setToolTip] = useState({
        clubInfo: false,
        guestFee: false,
        guestPay: false,
        closurePeriods: false,
        memberInfo: false,
        otherInstruction: false,
        visibility: false,
    });
    useEffect(() => {
        user?.clubs?.map((club) => {
            if (club?.club_id == props?.route?.params?.clubDetails?.club_id) {
                setUpdatedClub(club);
                setClubTier(club?.club?.lowest_visible_tier);
                setVisibleToTiers(club?.visible_to_tiers);
                setLoading(false);
            }
        });
    }, [user]);

    useEffect(() => {
        const {
            id,
            muted,
            tier,
            visibleToLowerTiers,
            is_visibile_to_lower_tiers,
            tierVisibility,
            visibleToPublic,
            profilePublic,
            socialPublic,
            notificationSettings,
            opt_in_email,
            visible_to_favorite_clubs,
            mute_other_notifications,
            visibleInNetwork,
        } = user;
        setSettings({
            muted,
            tier: props?.route?.params?.clubDetails?.club?.lowest_visible_tier,
            visibleToLowerTiers: updatedClub
                ? updatedClub?.is_visibile_to_lower_tiers
                : props?.route?.params?.clubDetails?.is_visibile_to_lower_tiers,
            visibleToSage:
                (updatedClub ? updatedClub?.visible_to_tiers : props?.route?.params?.clubDetails?.visible_to_tiers) >=
                2,
            visibleToMoss:
                (updatedClub ? updatedClub?.visible_to_tiers : props?.route?.params?.clubDetails?.visible_to_tiers) >=
                3,
            visibleToOlive:
                (updatedClub ? updatedClub?.visible_to_tiers : props?.route?.params?.clubDetails?.visible_to_tiers) >=
                5,
            visibleToPublic,
            profilePublic,
            socialPublic,
            notificationSettings,
            opt_in_email,
            visible_to_favorite_clubs,
            mute_other_notifications,
            visibleInNetwork,
        });

        let obj = {
            user_id: user?.id,
        };

        setRequest(obj);
        // }
        setLoading(false);
    }, [props?.route?.params, club?.club, updatedClub]);

    useEffect(() => {
        if (props?.route?.params?.lowest_visible_tier === 5 || club?.club?.lowest_visible_tier === 5) {
            setSageCheckBox(true);
            setMossCheckBox(true);
            setOliveCheckBox(true);
        } else if (props?.route?.params?.lowest_visible_tier === 3 || club?.club?.lowest_visible_tier === 3) {
            setSageCheckBox(true);
            setMossCheckBox(true);
        } else if (props?.route?.params?.lowest_visible_tier === 2 || club?.club?.lowest_visible_tier === 2) {
            setSageCheckBox(true);
        }
    }, [props?.route?.params?.lowest_visible_tier, club?.club, updatedClub]);

    useEffect(() => {
        const club = user?.clubs.filter(({ club }) => clubId === club?.id);
        setClub(club[0]);

        // Set gender preferences value to the filter state
        setFilter(club[0]?.gender_preference);
    }, [user]);

    function capitalizeFirstLetter(str) {
        // converting first letter to uppercase
        const capitalized = str?.charAt(0)?.toUpperCase() + str?.slice(1);
        return capitalized;
    }

    useEffect(() => {
        const weekdays = Object.fromEntries(
            Object.entries(dayLabels).map(([shortDay, longDay]) => {
                if (club?.club?.guest_time_restrictions[longDay]) {
                    return [longDay, club.club?.guest_time_restrictions[longDay]];
                } else {
                    return [longDay, null];
                }
            }),
        );
        setTimeDescriptions(getDayDescriptions([], 'Monday', 0, weekdays));
    }, [club]);

    const handleCheckbox = async (club, user, active, visibleToTiers) => {
        setLoading(true);
        fetcher({
            endpoint: CLUB_LOWER_TIER_VISSIBILITY,
            method: 'POST',
            body: {
                userId: user?.id,
                clubId: club?.club?.id,
                visibleToTiers: !active ? (visibleToTiers == 5 ? 3 : visibleToTiers == 3 ? 2 : 1) : visibleToTiers,
                isVisibleToLowerTiers: true,
            },
        }).then(async (res) => {
            await refreshUser();
        });
        setTimeout(() => {
            setLoading(false);
        }, 3000);
    };

    const updateClubLtv = async (club, user, active) => {
        setLoading(true);
        try {
            fetcher({
                endpoint: CLUB_LOWER_TIER_VISSIBILITY,
                method: 'POST',
                body: {
                    userId: user?.id,
                    clubId: club?.club?.id,
                    visibleToTiers: !active
                        ? null
                        : !visibleToTiers && clubTier < MOSS
                        ? clubTier + 1
                        : !visibleToTiers && clubTier === MOSS
                        ? OLIVE
                        : visibleToTiers,
                    isVisibleToLowerTiers: active,
                },
            }).then(async (response) => {
                await refreshUser();
                setLoading(false);
                if (response.status === 200) {
                }
            });
        } catch (error) {
            console.log(error);
        }
    };

    function handleChecked(field, active) {
        let visibleToTiers = field === 'visibleToSage' ? 2 : field === 'visibleToMoss' ? 3 : 5;

        handleCheckbox(club, user, active, visibleToTiers);
        if (active) {
            if (field === 'visibleToOlive') {
                setSettings({
                    ...settings,
                    visibleToSage: true,
                    visibleToMoss: true,
                    visibleToOlive: true,
                });
            }
            if (field === 'visibleToMoss') {
                setSettings({
                    ...settings,
                    visibleToSage: true,
                    visibleToMoss: true,
                });
            }
        } else {
            if (field === 'visibleToMoss') {
                setSettings({
                    ...settings,
                    visibleToMoss: settings?.visibleToOlive,
                });
            } else
                setSettings({
                    ...settings,
                    [field]: active,
                });
        }
    }

    function handleTogglePress(field, active) {
        updateClubLtv(club, user, active);
        setSettings({ ...settings, [field]: active });
    }
    function getDayDescriptions(allTimes = [], dayLabel = 'Monday', i = 0, value) {
        const labels = Object.entries(dayLabels);
        if (i !== labels.length - 1) {
            const nextDayLabel = labels[i + 1][1];
            if (value[dayLabel]) {
                const startDay = dayLabel;
                const { nextEqualDay, nextEqualIndex, nextUnequalDayLabel } = getNextEqualDay(startDay, i, value);
                const description =
                    startDay === nextEqualDay || !nextEqualDay ? `${startDay}` : `${startDay} - ${nextEqualDay}`;
                if (nextEqualIndex !== labels.length - 1) {
                    return getDayDescriptions(
                        [...allTimes, description],
                        nextUnequalDayLabel,
                        nextEqualIndex + 1,
                        value,
                    );
                } else {
                    return [...allTimes, description];
                }
            } else {
                return getDayDescriptions(allTimes, nextDayLabel, i + 1, value);
            }
        } else {
            if (value[dayLabel]) {
                const description = `${dayLabel}`;
                return [...allTimes, description];
            } else {
                return allTimes;
            }
        }
    }

    function getNextEqualDay(dayLabel, i, value) {
        const labels = Object.entries(dayLabels);
        if (i !== 6) {
            const nextDayLabel = labels[i + 1][1];
            if (value[nextDayLabel] === value[dayLabel]) {
                return getNextEqualDay(nextDayLabel, i + 1, value);
            } else {
                return {
                    nextEqualDay: dayLabel,
                    nextUnequalDayLabel: nextDayLabel,
                    nextEqualIndex: i,
                };
            }
        } else {
            return {
                nextEqualDay: dayLabel,
                nextUnequalDayLabel: dayLabel,
                nextEqualIndex: i,
            };
        }
    }

    useEffect(() => {
        fetchFavouriteList(user?.id);
    }, [navigation, isFocused]);

    // Function to call total favourite club list
    function fetchFavouriteList(user_id) {
        fetcher({
            method: 'POST',
            endpoint: favListURL,
            body: {
                user_id,
                data: true,
            },
        }).then((res) => {
            setTotalFavClub(res?.count);
            setLoading(false);
        });
    }

    // get the no minimum club
    const { data: systemSettings } = useQuery(FETCH_SYSTEM_SETTING, {
        name: 'min_favorite_clubs_for_user_visibility',
    });

    // This useEffect is used to get the min favourite club value
    useEffect(() => {
        if (systemSettings) {
            const {
                system_setting_by_pk: {
                    value: { value = 10 },
                },
            } = systemSettings;
            setMinFavClub(value);
        }
    }, [systemSettings]);

    // This function is used to update fav restricted for that club
    const handleFavRestricted = (isRestricted = false) => {
        fetcher({
            method: 'POST',
            endpoint: FAVOURITE_RESTRICTED,
            body: {
                userId: user?.id,
                clubId: clubId,
                isRestricted,
            },
        }).then((res) => {
            refreshUser();
        });
    };
    // handle favourite toggle button.
    const handleOnPress = (field, active) => {
        if (field) {
            // if condition will call when min fav club is less than 4
            if (minFavClub > totalFavClub && !user?.visible_to_favorite_clubs) {
                navigation.navigate('DeleteChannelConfirmationPopup', {
                    handleYesButton: async () => {
                        navigation.navigate('Search Favourit Clubs', {
                            minFavClub,
                            totalFavClub,
                            club,
                        });
                    },
                    popupHeader: 'Mark clubs as favourite',
                    popupSubText: `You need to have at least ${minFavClub} clubs marked as favourite in order to use this functionality!`,
                    firstBtnLabel: 'Cancel',
                    secondBtnLabel: 'Mark favourite',
                });
            } else {
                // else condition will call when fav club is more then 4 and handleFavRestricted call
                handleFavRestricted(field);
            }
        } else {
            handleFavRestricted(field);
        }
    };

    /**
     *
     * @param {*} option
     * Handle club gender preferences
     */
    const handleClubPreferences = async (option) => {
        setFilter(option);
        const payload = {
            userId: user?.id,
            clubId: clubId,
            genderPrefrence: option.toLowerCase(),
        };
        let response = await updateClubPreferences(payload);
    };

    const clubInfo = () => {
        return (
            <>
                <View style={[styles.infoContainer, { borderTopRightRadius: 10, borderTopLeftRadius: 10 }]}>
                    <Text style={styles.infoLabelNew}>Club Specific Information</Text>
                    <ShowToolTip
                        show={toolTip?.clubInfo}
                        description={
                            'If you change any club-related information it would get reflected to all the members of the club'
                        }
                        iconColor={colors.darkteal}
                        posiition="bottom"
                        onPress={() =>
                            setToolTip({
                                ...toolTip,
                                clubInfo: true,
                            })
                        }
                        onClose={() =>
                            setToolTip({
                                ...toolTip,
                                clubInfo: false,
                            })
                        }
                        iconSize={14}
                        marginLeft={5}
                    />
                </View>

                <View
                    style={{
                        backgroundColor: 'white',
                        paddingTop: Spacing.SCALE_25,
                        paddingHorizontal: Spacing.SCALE_20,
                    }}>
                    <View style={styles.detailsLabelView}>
                        <Text style={styles.clubDetailsLabel}>Accompanied Guest Fee</Text>

                        <ShowToolTip
                            show={toolTip?.guestFee}
                            description={'Please add Guest Fee (including currency)'}
                            iconColor={toolTip?.guestFee ? colors.darkteal : colors.fadeBlack}
                            onPress={() =>
                                setToolTip({
                                    ...toolTip,
                                    guestFee: true,
                                })
                            }
                            onClose={() =>
                                setToolTip({
                                    ...toolTip,
                                    guestFee: false,
                                })
                            }
                            iconSize={14}
                            marginLeft={5}
                        />
                    </View>
                    <Text style={styles.clubDetailsValue}>{club?.club?.guestFee}</Text>

                    <View style={styles.detailsLabelView}>
                        <Text style={styles.clubDetailsLabel}>Club Availability for Guest Play</Text>
                        <ShowToolTip
                            show={toolTip?.guestPay}
                            description={
                                'Please indicate which days of the week are allowable for guest play at your club'
                            }
                            iconColor={toolTip?.guestPay ? colors.darkteal : colors.fadeBlack}
                            onPress={() =>
                                setToolTip({
                                    ...toolTip,
                                    guestPay: true,
                                })
                            }
                            onClose={() =>
                                setToolTip({
                                    ...toolTip,
                                    guestPay: false,
                                })
                            }
                            iconSize={14}
                            marginLeft={5}
                        />
                    </View>
                    <View>
                        {timeDescriptions?.length > 0 ? (
                            timeDescriptions.map((time, i) => (
                                <Text style={[styles.clubDetailsValue, { marginBottom: 0 }]}>{time}</Text>
                            ))
                        ) : (
                            <Text style={styles.clubDetailsValue} />
                        )}
                    </View>

                    {club?.club?.closurePeriods?.length > 0 && (
                        <>
                            <View style={[styles.detailsLabelView, { marginTop: Spacing.SCALE_25 }]}>
                                <Text style={styles.clubDetailsLabel}>Closure Periods</Text>
                                <ShowToolTip
                                    show={toolTip?.closurePeriods}
                                    description={
                                        'Please indicate when your club is closed for the season or for reasons such as renovation. This only applies to clubs that shut for a significant time period each year'
                                    }
                                    iconColor={toolTip?.closurePeriods ? colors.darkteal : colors.fadeBlack}
                                    onPress={() =>
                                        setToolTip({
                                            ...toolTip,
                                            closurePeriods: true,
                                        })
                                    }
                                    onClose={() =>
                                        setToolTip({
                                            ...toolTip,
                                            closurePeriods: false,
                                        })
                                    }
                                    iconSize={14}
                                    marginLeft={5}
                                />
                            </View>
                            <View>
                                {club?.club?.closurePeriods?.map(({ from, to }, i) => (
                                    <Text style={[styles.clubDetailsValue, { marginBottom: 0 }]}>
                                        {Moment(from, 'MM/DD/YYYY').format('MM/DD/YYYY')} -{' '}
                                        {Moment(to, 'MM/DD/YYYY').format('MM/DD/YYYY')}
                                    </Text>
                                ))}
                            </View>
                        </>
                    )}
                    <View style={[styles.detailsLabelView, { marginTop: Spacing.SCALE_25 }]}>
                        <Text style={styles.clubDetailsLabel}>Dress Code</Text>
                    </View>
                    <Text style={styles.clubDetailsValue}>{club?.club?.dressCode}</Text>

                    <View style={styles.detailsLabelView}>
                        <Text style={styles.clubDetailsLabel}>Caddie Required</Text>
                    </View>
                    <Text style={styles.clubDetailsValue}>{club?.club?.caddieRequired ? 'Yes' : 'No'}</Text>
                    {club?.club?.caddieRequired ? (
                        <>
                            <View style={styles.detailsLabelView}>
                                <Text style={styles.clubDetailsLabel}>Caddie Fees (Incl. tips)</Text>
                            </View>
                            <Text style={styles.clubDetailsValue}>
                                {club?.club?.caddieRequired ? club?.club?.caddieFee : null}
                            </Text>
                        </>
                    ) : null}
                    <View style={styles.detailsLabelView}>
                        <Text style={styles.clubDetailsLabel}> Club's Tier</Text>
                    </View>
                    <Text style={styles.clubDetailsValue}>{clubTier1}</Text>
                </View>
            </>
        );
    };

    const memberInfo = () => {
        return (
            <>
                {/* Member specific information section UI */}
                <View style={styles.infoContainer}>
                    <Text style={styles.infoLabelNew}>Member Specific Information</Text>
                    <ShowToolTip
                        show={toolTip?.memberInfo}
                        iconColor={colors.darkteal}
                        description={'This information would be specific to the user for the selected club'}
                        onPress={() =>
                            setToolTip({
                                ...toolTip,
                                memberInfo: true,
                            })
                        }
                        onClose={() =>
                            setToolTip({
                                ...toolTip,
                                memberInfo: false,
                            })
                        }
                        iconSize={14}
                        marginLeft={5}
                    />
                </View>

                {/* UI to show title of Preferred method to do payment */}
                <View
                    style={{
                        backgroundColor: 'white',
                        paddingVertical: Spacing.SCALE_15,
                        paddingHorizontal: Spacing.SCALE_10,
                    }}>
                    <View style={[styles.detailsLabelView, { paddingHorizontal: Spacing.SCALE_10 }]}>
                        <Text style={styles.clubDetailsLabel}>Preferred Method of Green Fees Settlement:</Text>
                    </View>

                    {/* UI to show type of Preferred method to do payment */}
                    <Text
                        style={[
                            styles.clubDetailsValue,
                            {
                                paddingHorizontal: Spacing.SCALE_10,
                                marginBottom: Spacing.SCALE_15,
                            },
                        ]}>
                        {club?.paymentMethod?.type !== 'digital'
                            ? capitalizeFirstLetter(club?.paymentMethod?.type)
                            : capitalizeFirstLetter(club?.paymentMethod?.digitalPaymentType)}
                    </Text>

                    {club?.paymentMethod?.otherText?.length > 0 &&
                        (club?.paymentMethod?.type?.toLowerCase() == 'pay by credit card at pro shop' ||
                            club?.paymentMethod?.type?.toLowerCase() == 'other') && (
                            <>
                                <View style={[styles.detailsLabelView, { paddingHorizontal: Spacing.SCALE_10 }]}>
                                    <Text style={[styles.clubDetailsLabel]}>
                                        {club?.paymentMethod?.type?.toLowerCase() == 'pay by credit card at pro shop'
                                            ? 'Any Other Information'
                                            : 'Other Payment Information'}
                                    </Text>
                                </View>
                                <Text style={[styles.clubDetailsValue, { paddingHorizontal: Spacing.SCALE_10 }]}>
                                    {club?.paymentMethod?.otherText}
                                </Text>
                            </>
                        )}

                    {/* Other instruction UI */}
                    {club?.otherInstructions?.length > 0 && (
                        <>
                            <View style={[styles.detailsLabelView, { paddingHorizontal: Spacing.SCALE_10 }]}>
                                <Text style={styles.clubDetailsLabel}>Other Instructions</Text>
                                <ShowToolTip
                                    show={toolTip?.otherInstruction}
                                    description={
                                        'Please share any other information that you would like your guest to know'
                                    }
                                    iconColor={toolTip?.otherInstruction ? colors.darkteal : colors.fadeBlack}
                                    onPress={() =>
                                        setToolTip({
                                            ...toolTip,
                                            otherInstruction: true,
                                        })
                                    }
                                    onClose={() =>
                                        setToolTip({
                                            ...toolTip,
                                            otherInstruction: false,
                                        })
                                    }
                                    iconSize={14}
                                    marginLeft={5}
                                />
                            </View>
                            <Text style={[styles.clubDetailsValue, { paddingHorizontal: Spacing.SCALE_10 }]}>
                                {club?.otherInstructions}
                            </Text>
                        </>
                    )}
                </View>
            </>
        );
    };

    const requestPreferences = () => {
        return (
            <>
                <View style={styles.infoContainer}>
                    <Text style={styles.infoLabelNew}>Request Preferences</Text>
                </View>
                {/* Receive request from lower tiers UI */}
                <View style={{ paddingHorizontal: Spacing.SCALE_10 }}>
                    {club?.club?.lowest_visible_tier < 5 && user?.visibleToPublic && (
                        <>
                            <AccountToggleButton
                                title="Receive requests from lower tiers"
                                tips={`Making yourself visible to lower tier members\n enables them to make a request to you`}
                                name="visibleToLowerTiers"
                                disabled={false}
                                isToggled={settings?.visibleToLowerTiers}
                                onPress={handleTogglePress}
                                titleStyle={styles.titleStyle}
                                style={styles.toggleBtnStyle}
                            />
                            <View style={styles.lowerTierTextWrapper}>
                                <Text style={styles.lowerTierText}>{LOWER_TIER_TEXT}</Text>
                            </View>

                            {settings?.visibleToLowerTiers && (
                                <>
                                    <Spacer />
                                    <View
                                        style={{
                                            paddingHorizontal: 16,
                                            marginTop: -14,
                                        }}>
                                        {settings?.tier <= 1 && (
                                            <CheckboxNew
                                                useTierVisibilityEffect
                                                name="visibleToSage"
                                                label="Visible to Sage ?"
                                                active={settings?.visibleToSage}
                                                disabled={true}
                                                width="50%"
                                                onCheck={handleChecked}
                                                titleStyle={styles.checkBoxTitleStyle}
                                            />
                                        )}
                                        {settings?.tier <= 2 && (
                                            <CheckboxNew
                                                useTierVisibilityEffect
                                                name="visibleToMoss"
                                                label="Visible to Moss ?"
                                                disabled={settings?.tier >= 2}
                                                active={settings?.visibleToMoss}
                                                width="50%"
                                                onCheck={handleChecked}
                                                titleStyle={styles.checkBoxTitleStyle}
                                            />
                                        )}
                                        {settings?.tier <= 3 && (
                                            <CheckboxNew
                                                useTierVisibilityEffect
                                                name="visibleToOlive"
                                                label="Visible to Olive ?"
                                                disabled={settings?.tier >= 3}
                                                active={settings?.visibleToOlive}
                                                width="50%"
                                                onCheck={handleChecked}
                                                titleStyle={styles.checkBoxTitleStyle}
                                            />
                                        )}
                                    </View>
                                </>
                            )}
                        </>
                    )}
                    {/* Filter favourite functionality */}
                    <View style={styles.favWrapper}>
                        <View style={styles.favTextWrapper}>
                            <Text style={styles.favTextStyle}>Receive Request only from My Favourite Club Members</Text>
                        </View>
                        <View>
                            <ToggleButton onPress={handleOnPress} isToggled={updatedClub?.favorite_restricted} />
                        </View>
                    </View>

                    {/* Gender preferences UI */}
                    <View style={styles.footerTextWrapper}>
                        <Text style={styles.footerTextStyle}>{RECEIVED_REQUEST_ONLY_TEXT}</Text>
                    </View>
                    <View style={styles.genderPreferenceWrapper}>
                        <Text style={styles.genderPreferenceText}>{GENDER_PREFERENCE_HEADER}</Text>
                        <Text style={[styles.footerTextStyle, { marginTop: Spacing.SCALE_4 }]}>
                            {GENDER_PREFERENCE_SUBHEADER}
                        </Text>
                        <View style={[styles.radioBtnContainer]}>
                            <CustomRadioButton
                                placeHolder="both"
                                onPress={() => handleClubPreferences('both')}
                                selectedBox={filter}
                            />
                        </View>
                        {user?.gender === 'female' ? (
                            <View style={[styles.radioBtnContainer]}>
                                <CustomRadioButton
                                    placeHolder="female"
                                    onPress={() => handleClubPreferences('female')}
                                    selectedBox={filter}
                                />
                            </View>
                        ) : (
                            <View style={[styles.radioBtnContainer]}>
                                <CustomRadioButton
                                    placeHolder="male"
                                    onPress={() => handleClubPreferences('male')}
                                    selectedBox={filter}
                                />
                            </View>
                        )}
                    </View>
                </View>
            </>
        );
    };

    return (
        <>
            <View style={styles.container}>
                <View
                    style={{
                        paddingVertical: Spacing.SCALE_10,
                        marginBottom: Spacing.SCALE_10,
                    }}>
                    <ProfileHeader
                        showEditIcon={true}
                        title={clubName}
                        iconShow={'Club Member'}
                        onClick={() =>
                            navigation.navigate('Club Member Details', {
                                clubId: club?.club?.id,
                            })
                        }
                        onClickEditIcon={() =>
                            navigation.navigate('EditMyClub', {
                                clubName,
                                club,
                            })
                        }
                        headerTitleStyle={styles.headerTitleStyle}
                    />
                </View>

                <View style={[styles.listContainer, { backgroundColor: 'white' }]}>
                    <ScrollView
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={{
                            paddingBottom: Spacing.SCALE_40,
                            borderRadius: 10,
                        }}>
                        {clubInfo()}
                        {memberInfo()}
                        <View style={{ paddingHorizontal: Spacing.SCALE_16 }}>
                            <View style={styles.dividerLine} />
                        </View>
                        <MuteClubComponent club={club} setMuteModalOpen={setMuteModalOpen} navigation={navigation} />
                        {requestPreferences()}
                    </ScrollView>
                </View>
            </View>
            {muteModalOpen && <MuteModalNew modalData={muteModalOpen} closeModal={() => setMuteModalOpen(false)} />}
            {loading && (
                <View
                    style={{
                        position: 'absolute',
                        top: 0,
                        bottom: 0,
                        right: 0,
                        left: 0,
                        backgroundColor: 'rgba(0,0,0,0.5)',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}>
                    <ActivityIndicator color={colors.darkteal} size="large" />
                </View>
            )}
        </>
    );
};
