import React, { useEffect, useState } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, Text, Linking } from 'react-native';
import HTMLView from 'react-native-htmlview';
import { colors } from '../../theme/theme';
import useQuery from '../../hooks/useQuery';
import { Collapse, Expand } from '../../assets/images/svg';
import ScreenHeader from '../../components/layout/ScreenHeader';
import NewScreenHeader from '../../components/layout/NewScreenHeader';
import TGText from '../../components/fields/TGText';
import { Spacing, Typography } from '../../utils/responsiveUI';
import EmailIcon from '../../assets/svg/mail.svg';

export default function FAQ({ navigation }) {
    // const [error, setError] = useState({});
    const [state, stateState] = useState({
        expanded: false,
        faqList: null,
    });

    const { data, error } = useQuery(`
    query fetchFAQs {
        faq(order_by: {order: asc}) {
            heading
            body
            id
        }
    }
    `);

    useEffect(() => {
        if (data) {
            var newList = data?.faq.map((el) => {
                var o = Object.assign({}, el);
                o.isCollapse = false;
                return o;
            });
            stateState({
                ...state,
                faqList: newList,
            });
        }
    }, [data]);
    console.log('data', data, error);
    const htmlContent =
        '<p>All members are placed in a tier based on their club membership (local or seasonal unrestricted members only, read on for how other members are tiered). Clubs are categorized in 4 tiers. Tiers are designed to strike the right balance between creating a broad network AND ensuring that the members of highly reputed clubs do not get overwhelmed by unmanageable demand. Published rankings lists are used from well known sources (recognizing that all lists are subjective) to make the tier determination. We do not make personal judgement regardless of the inherent subjectivity in the underlying lists. The 4 tiers are</p><p>1. Fern Tier (Best in the World) - Clubs listed in the World Top 100 in any one of Golf.com or www.top100golfcourses.com lists. We use a super set of both lists to be more inclusive</p><p>2. Sage Tier (Best in the Country) - Clubs listed in the US Top 100, Britain and Ireland Top 100, Continental Europe Top 100, Australia Top 50, Canada Top 30, or smaller countries, the country Top 10. For the US, we use three published lists (Golf.com, Golf Digest and www.top100golfcourses.com). We also consider any course featured in the Top 50 Modern courses in Golfweek. For all regions outside the US, we use www.top100golfcourses.com, it seems to be the only comprehensive global list available</p><p>3. Moss Tier (Best in the State) - Clubs listed on the www.top100golfcourses.com list. We use this list as only they seem to go to this level of detail consistently and globally</p><p>4. Olive Tier (Other private clubs) - Private clubs that do not feature on www.top100golfcourses.com are placed in the Olive Tier</p><p>We will on rare occasion tier a club up or down based on supply demand patterns. Over time, we hope to use the request and demand data more intelligently to inform our club tiering criteria.</p><p>Only clubs where a member lives within a 90 minute drive (permanently or seasonally) and has unrestricted guest privileges are used to determine a member\'s tier initially. Clubs which are further than a 90 minute drive or where a member has restrictions on guest privileges do not factor into member tier determination apriori. All such memberships start the member in the Olive tier.. In infrequent cases where a member does not hold any full certificated membership, they are placed in the Olive tier.</p><p>Members can earn a bump up to a higher tier under one scenario that reward members appropriately for their hosting efforts.</p><p>a) If a member hosts another member at one of their long distance clubs (which has a higher tier rating), their tier will bump up to their long distance club tier for a specified period (6 months at time of publication).  If a member hosts again during that window, the clock gets reset to the latest hosting date.</p><p>All members are able to request play at any club in their tier or lower tiers. Conversely, any offer made by you will go to members of your tier or higher tiers. For members who value their privacy, this will ensure that you are only interacting with other members of clubs of reputational equivalency. If you are, however, as golf-obsessed as I am, I sincerely hope that you will enable access to your beautiful club to others in this exclusive network. There are two ways to do so. First, in your "My Profile" page, you can make your club visible to lower tiers. You control who you want to make it visible to, and it is defaulted to your tier and higher tiers. Second, when you make an offer, you will have an option of making your offer visible to lower tiers. I sincerely hope you will take advantage of this flexibility.</p>';

    function RenderFAQItem({ item, index }) {
        const handleCollapse = (item) => {
            let arr = data?.faq.map((el) => {
                var o = Object.assign({}, el);
                if (o.heading == item?.heading && !item.isCollapse) {
                    o.isCollapse = true;
                } else {
                    o.isCollapse = false;
                }
                return o;
            });
            stateState({
                ...state,
                faqList: arr,
                expanded: !state.expanded,
            });
        };

        return (
            <View style={styles.itemContainer}>
                <TouchableOpacity style={{ flexDirection: 'row' }} onPress={() => handleCollapse(item)}>
                    <TGText style={styles.itemText}>{item?.heading}</TGText>
                    <View style={{ padding: 15 }}>{item.isCollapse ? <Collapse /> : <Expand />}</View>
                </TouchableOpacity>
                {item.isCollapse && (
                    <View style={{ padding: 15 }}>
                        <HTMLView value={item?.body} stylesheet={styles} />
                    </View>
                )}
            </View>
        );
    }

    return (
        <View style={{ flex: 1 }}>
            <View style={{ flex: 1, backgroundColor: 'white' }}>
                <NewScreenHeader title="FAQ" showNotification={false} />
                <View style={styles.container}>
                    <TGText
                        style={{
                            color: colors.dark_charcoal,
                            fontSize: Typography.FONT_SIZE_16,
                            fontFamily: 'Ubuntu-Medium',
                        }}>
                        Contact
                    </TGText>
                    <TouchableOpacity
                        style={{
                            padding: Spacing.SCALE_12,
                            backgroundColor: colors.whiteRGB,
                            borderRadius: 8,
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginTop: Spacing.SCALE_12,
                            marginBottom: Spacing.SCALE_24,
                            justifyContent: 'space-between',
                        }}
                        onPress={() => Linking.openURL('mailto:<EMAIL>')}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', columnGap: Spacing.SCALE_12 }}>
                            <EmailIcon />
                            <TGText
                                style={{
                                    color: colors.dark_charcoal,
                                    fontSize: Typography.FONT_SIZE_14,
                                    fontFamily: 'Ubuntu-Regular',
                                }}>
                                Email Us
                            </TGText>
                        </View>
                        <View style={{ transformOrigin: 'center', transform: [{ rotate: '90deg' }] }}>
                            <Collapse />
                        </View>
                    </TouchableOpacity>
                    <TGText
                        style={{
                            color: colors.dark_charcoal,
                            fontSize: Typography.FONT_SIZE_16,
                            fontFamily: 'Ubuntu-Medium',
                        }}>
                        FAQ
                    </TGText>
                    <FlatList
                        data={state.faqList}
                        renderItem={({ item, index }) => <RenderFAQItem item={item} index={index} />}
                        keyExtractor={(item) => item?.id}
                    />
                </View>
            </View>
        </View>
    );
}
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#F2F2F2',
        padding: 16,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
    },
    itemContainer: {
        borderRadius: 8,
        marginVertical: 10,
        alignItems: 'center',
        backgroundColor: 'white',
    },
    itemText: {
        flex: 1,
        color: '#000000',
        fontFamily: 'Ubuntu-Regular',
        fontSize: 16,
        padding: 15,
    },
});
