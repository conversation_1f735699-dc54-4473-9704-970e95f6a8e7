import { Linking, ScrollView, StatusBar, StyleSheet, Text, View } from 'react-native';
import React from 'react';

// Theme and assets, utils imports
import { colors } from '../../theme/theme';
import { RequestMaintenanceIcon } from '../../assets/svg';
import { Size, Spacing, Typography } from '../../utils/responsiveUI';
import HomeScreenCommonHeader from '../../components/homeScreenComponent/HomeScreenCommonHeader';
import ProfileHeader from '../../components/layout/ProfileHeader';

interface ModuleMaintenanceProps {
    title: string;
    description: string;
    isNestedScreen?: boolean;
    headerTitle: string;
}

const ModuleMaintenance: React.FC<ModuleMaintenanceProps> = ({
    title,
    description,
    isNestedScreen = false,
    headerTitle = 'Requests',
}) => {
    return (
        <>
            <StatusBar barStyle="dark-content" backgroundColor={colors.whiteRGB} />
            <View style={{ backgroundColor: colors.whiteRGB, flex: 1 }}>
                {isNestedScreen ? (
                    <ProfileHeader
                        title={headerTitle}
                        headerTitleStyle={styles.headerTitleStyle}
                        backButtonFillColor={colors.lightBlack}
                        containerStyle={{
                            backgroundColor: colors.whiteRGB,
                            paddingBottom: Spacing.SCALE_10,
                        }}
                    />
                ) : (
                    <HomeScreenCommonHeader title={headerTitle} />
                )}
                <ScrollView contentContainerStyle={styles.container}>
                    <RequestMaintenanceIcon height={Size.SIZE_250} width={Size.SIZE_290} />
                    <Text style={styles.title}>{title}</Text>
                    <Text style={styles.description}>{description}</Text>
                    <Text style={styles.contactUs}>
                        Need help? Contact us at{' '}
                        <Text
                            style={{ color: colors.darkteal }}
                            onPress={() => Linking.openURL('mailto:<EMAIL>')}>
                            <EMAIL>
                        </Text>
                    </Text>
                </ScrollView>
            </View>
        </>
    );
};

export default ModuleMaintenance;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.white,
        alignItems: 'center',
        padding: Spacing.SCALE_16,
        marginTop: Spacing.SCALE_50,
    },
    title: {
        fontSize: Typography.FONT_SIZE_20,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
        textAlign: 'center',
        marginTop: Spacing.SCALE_32,
    },
    description: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.systemMessageText,
        textAlign: 'center',
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_21,
        marginTop: Spacing.SCALE_16,
    },
    contactUs: {
        fontSize: Typography.FONT_SIZE_14,
        color: colors.blackVariant1,
        textAlign: 'center',
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_21,
        marginTop: Spacing.SCALE_16,
    },
    headerTitleStyle: {
        color: colors.lightBlack,
        textAlign: 'left',
        marginLeft: Spacing.SCALE_10,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_1,
    },
});
