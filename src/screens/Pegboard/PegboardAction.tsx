import { Modal, Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';

//utils, theme imports
import { Spacing, Typography } from '../../utils/responsiveUI';
import { Size } from '../../utils/responsiveUI';
import { colors } from '../../theme/theme';

const PegboardAction = ({
    popupState,
    onPressViewPegboard,
    onPressHidePegboard,
    selectedItem,
    userId,
}: {
    popupState: [boolean, (value: boolean) => void];
    onPressViewPegboard: () => void;
    onPressHidePegboard: () => void;
    selectedItem: any;
    userId: string;
}) => {

    const [trippleDot, setTrippleDot] = popupState;
    return (
        <Modal transparent={true} visible={trippleDot} style={styles.modalStyle}>
            <View style={styles.container}>
                <Pressable style={styles.blankScreenWrapper} />
                <View style={styles.bodyStyle}>
                    <View style={styles.popupWrapper}>
                        <TouchableOpacity style={styles.popupContent} onPress={onPressViewPegboard}>
                            <Text style={styles.popupOptionStyle}>View Pegboard</Text>
                        </TouchableOpacity>
                        {selectedItem?.creatorId !== userId && (
                            <>
                                <View style={styles.divider} />
                                <TouchableOpacity style={styles.popupContent} onPress={onPressHidePegboard}>
                                    <Text style={styles.popupOptionStyle}>
                                        {selectedItem?.isHidden ? 'Unhide Pegboard' : 'Hide Pegboard'}
                                    </Text>
                                </TouchableOpacity>
                            </>
                        )}
                    </View>
                    <Pressable style={styles.popupWrapper1} onPress={() => setTrippleDot(false)}>
                        <Text style={styles.cancelBtnStyle}>Cancel</Text>
                    </Pressable>
                </View>
            </View>
        </Modal>
    );
};

export default PegboardAction;

const styles = StyleSheet.create({
    modalStyle: {
        paddingHorizontal: 0,
        marginHorizontal: 0,
        paddingVertical: 0,
        marginVertical: 0,
    },
    container: {
        flex: 1,
    },
    popupWrapper: {
        width: Size.SIZE_340,
        position: 'absolute',
        bottom: Spacing.SCALE_85,
        backgroundColor: colors.white,
        borderRadius: Size.SIZE_18,
        marginHorizontal: Spacing.SCALE_6,
        alignItems: 'center',
    },
    blankScreenWrapper: {
        flex: 1,
        backgroundColor: colors.transparentRgba,
    },
    bodyStyle: {
        backgroundColor: colors.transparentRgba,
        width: '100%',
        minHeight: '100%',
        justifyContent: 'center',
        alignItems: 'center',
    },
    popupWrapper1: {
        width: Size.SIZE_340,
        minHeight: Size.SIZE_54,
        position: 'absolute',
        bottom: Spacing.SCALE_24,
        backgroundColor: '#FFFFFF',
        borderRadius: Size.SIZE_14,
        marginHorizontal: 7,
        justifyContent: 'center',
        alignItems: 'center',
    },
    cancelBtnStyle: {
        color: colors.Dark_Azure,
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '600',
        textAlign: 'center',
        fontFamily: 'Ubuntu-Medium',
    },
    popupContent: {
        height: Size.SIZE_40,
        justifyContent: 'center',
    },
    popupOptionStyle: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        color: colors.Dark_Azure,
        fontFamily: 'Ubuntu-Medium',
    },
    divider: {
        height: 0.3,
        backgroundColor: colors.shadowDarkColor,
        width: '95%',
        alignSelf: 'center',
    },
});
