import React, { useCallback, useContext, useEffect, useState } from 'react';
import { Keyboard, View } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';

import StreamChatHeader from '../../../../components/layout/StreamChatHeader';
import { ALL, GROUPS } from '../../../../utils/constants/strings';
import styles from '../style/GroupsStyle';
import { getMyTgGroups } from '../action/getMyTgGroup';
import { AuthContext } from '../../../../context/AuthContext';
import debounce, { handleGetAllFriendsId } from '../../../my-TG-Stream-Chat/action';
import config from '../../../../config';
import { GlobalContext } from '../../../../context/contextApi';
import GroupTopNav from './GroupTopNav';
import constants from '../../../../utils/constants/constants';
import Config from 'react-native-config';
const CleverTap = require('clevertap-react-native');

const GroupScreen = () => {
    const { user } = useContext(AuthContext);
    const { actions } = useContext(GlobalContext);
    const navigation = useNavigation();
    const isFocused = useIsFocused();

    const [groupsList, setGroupList] = useState([]);
    const [searchData, setSearchData] = useState('');
    const [loading, setLoading] = useState(false);
    const [triggerSearchString, setTriggerSearchString] = useState('');
    const [currentPage, setCurrentPage] = useState(0);
    const [totalPage, setTotalPage] = useState(0);
    const [allGroups, setAllgroups] = useState([]);
    const [openSearchBar, setOpenSearchBar] = useState(false);
    const [selectedSubTab, setSelectedSubTab] = useState('My Groups');
    const [showSearch, setShowSearch] = useState(true);

    useEffect(() => {
        handleGetMyTgGroup();
        handleGetAllFriendsId(user?.id, actions);
    }, []);

    useEffect(() => {
        const unsubscribe = navigation.addListener('focus', () => {
            if (searchData) {
                handleGetMyTgGroupOnSearch();
                handleGetAllFriendsId(user?.id, actions);
            } else {
                handleGetMyTgGroup();
                handleGetAllFriendsId(user?.id, actions);
            }
        });
        return unsubscribe;
    }, [navigation, isFocused]);

    useEffect(() => {
        if (searchData) {
            handleGetMyTgGroupOnSearch();
        } else {
            handleGetMyTgGroup();
        }
    }, [triggerSearchString]);

    useEffect(() => {
        setCurrentPage(0);
        updateTriggerSearchStringUpdate(searchData);
    }, [searchData]);

    const updateTriggerSearchStringUpdate = useCallback(
        debounce((value) => {
            setTriggerSearchString(value);
        }, 300),
        [],
    );

    const handleGetMyTgGroup = () => {
        if (loading) return;
        if (currentPage <= totalPage) {
            setLoading(true);
            getMyTgGroups(
                { userId: user?.id, limit: 6, type: 2, page: 0 },
                setLoading,
                setGroupList,
                setAllgroups,
                allGroups,
                setTotalPage,
                setCurrentPage,
            );
        }
    };
    const handleGetMyTgGroupOnSearch = () => {
        if (loading) return;
        if (currentPage <= totalPage) {
            setLoading(true);
            getMyTgGroups(
                { userId: user?.id, limit: 10, page: currentPage, type: 2, search: triggerSearchString },
                setLoading,
                setGroupList,
                setAllgroups,
                allGroups,
                setTotalPage,
                setCurrentPage,
            );
        }
    };

    const handleOnPress = () => {
        navigation.navigate(config.routes.VIEW_ALL_GROUPS);
    };

    const onMapIconPress = () => {
        CleverTap.recordEvent(constants.CLEVERTAP.EVENTS.TG_GROUPS_ON_MAP);
        actions.setMapFilterState({ myTGGroupMember: true, clubMemberCount: ALL, clubPercentage: ALL }); // set Map filter in map screen after redirection to update filter data
        navigation.navigate(config.routes.BOTTOM_TAB_NAVIGATION, {
            screen: config.routes.CLUBS,
            params: { category: 'My TG Group Member' },
        });
    };

    const onPlusIconPress = () => {
        navigation.navigate(config.routes.CREATE_GROUP_SCREEEN);
    };

    return (
        <>
            <View style={styles.mainWrapper}>
                <StreamChatHeader
                    title={GROUPS}
                    disableSearch={false}
                    headerName={''}
                    setSearchData={setSearchData}
                    setRecentChatPopupState={undefined}
                    recentChatPopupState={undefined}
                    showTripleDot={false}
                    screenName={GROUPS}
                    showSearchIcon={showSearch}
                    showPlusIcon={true}
                    showMapIcon={true}
                    onMapIconPress={onMapIconPress}
                    onPlusIconPress={onPlusIconPress}
                    setOpenSearchBar={setOpenSearchBar}
                />
                <View style={styles.container} onPress={() => Keyboard.dismiss()}>
                    <GroupTopNav
                        tabParams={0}
                        openSearchBar={openSearchBar}
                        onPressViewAll={handleOnPress}
                        setSelectedSubTab={setSelectedSubTab}
                        setShowSearch={setShowSearch}
                    />
                </View>
            </View>
        </>
    );
};

export default GroupScreen;
