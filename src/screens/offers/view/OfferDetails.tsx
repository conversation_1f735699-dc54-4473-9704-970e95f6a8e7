import { Platform, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { SvgProps } from 'react-native-svg';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

//components and contexts imports
import Header from '../../../components/layout/ProfileHeader';
import TGText from '../../../components/fields/TGText';
import { handleTimeFormat } from '../../../components/timeFormatComponent/handleTimeFormat';
import TealButtonNew from '../../../components/buttons/TealButtonNew';
import TGCustomIconButton from '../../../components/buttons/TGCustomIconButton';
import showToast from '../../../components/toast/CustomToast';
import { AuthContext } from '../../../context/AuthContext';

//theme, assets, utils, actions and interface imports
import { colors } from '../../../theme/theme';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { MapClub, OfferDetail } from '../../../interface';
import {
    CalenderNewIcon,
    ClubGolfIconTeal,
    DeleteGreyIcon,
    EditIcon,
    GiftBoxIconTeal,
    MapLocationIconNew,
    TGAmbassadorLogo,
} from '../../../assets/svg';
import { fetcher } from '../../../service/fetcher';
import { DELETE_OFFER } from '../../../graphql/mutations/offers';
import { RootStackParamList } from '../../../interface/type';
import FastImage from 'react-native-fast-image';
import { GlobalContext } from '../../../context/contextApi';
import RequestScreenSkelton from '../../requests/view/RequestScreenSkelton';
import redirectToUserProfile from '../../requests/action/openUserProfile';
import { apiServices } from '../../../service/apiServices';
import TierCard from '../../../components/map/TierCard';

interface OfferDetailsProps {
    route: {
        params: {
            offerID: string;
            prevScreenCallBack?: () => void;
        };
    };
    navigation: NativeStackNavigationProp<RootStackParamList>;
}

type IconType = React.ComponentType<SvgProps>;

const OfferDetails = ({ route, navigation }: OfferDetailsProps) => {
    const { offerID, prevScreenCallBack = () => {} } = route.params;
    const { user } = useContext(AuthContext);
    const { state, actions } = useContext(GlobalContext);
    const [offerDetails, setOfferDetails] = useState<OfferDetail | null>(null);

    useEffect(() => {
        handleGetOfferDetails();
    }, []);

    const handleGetOfferDetails = async () => {
        actions?.setAppSkeltonLoader(true);
        const offerDetailsRes = await apiServices.getOfferDetails({
            userId: user?.id,
            offerId: String(offerID),
        });
        if (offerDetailsRes?.status) {
            actions?.setAppSkeltonLoader(false);
            setOfferDetails(offerDetailsRes?.data);
        }
    };
    const isMyOffer = useMemo(() => {
        return user.clubs?.some((club: any) => {
            if (club?.club_id == offerDetails?.club_id) {
                return true;
            }
        });
    }, [offerDetails]);

    const handleDeleteOffer = () => {
        const deleteOffer = () => {
            actions?.setAppLoader(true);
            apiServices
                .deleteOffer({
                    userId: user?.id,
                    offerId: offerDetails?.id || '',
                })
                .then((res) => {
                    if (res?.status) {
                        navigation.goBack();
                        prevScreenCallBack();
                    } else {
                        showToast({});
                    }
                    actions?.setAppLoader(false);
                });
        };
        navigation.navigate('DeleteChannelConfirmationPopup', {
            handleYesButton: deleteOffer,
            popupHeader: 'Delete Offer',
            popupSubText: 'Are you sure you would like to delete your offer?',
            firstBtnLabel: 'Dismiss',
            secondBtnLabel: 'Delete',
        });
    };

    const handleEditOffer = () => {
        navigation.navigate('Edit Offer', {
            offer: offerDetails,
            refresh: handleGetOfferDetails,
        });
    };

    const handleProfilePress = () => {
        if (isMyOffer) return null;
        redirectToUserProfile({
            userId: offerDetails?.creator_id || '',
            navigation,
            user,
        });
    };

    const handleRequestAgainstOffer = async () => {
        const res = await apiServices.checkCanCreateRequest(user, !state.allFriendsId[offerDetails?.creator_id || '']);
        if (res?.canCreate) {
            navigation.navigate('Request Against Offer', {
                offer: offerDetails,
                callBack: () => {
                    prevScreenCallBack();
                    handleGetOfferDetails();
                },
            });
        } else {
            navigation.navigate('DeleteChannelConfirmationPopup', {
                popupSubText: res?.message,
                firstBtnLabel: 'Cancel',
                secondBtnLabel: 'Ok',
            });
        }
    };

    return (
        <>
            <StatusBar backgroundColor={colors.whiteRGB} barStyle="dark-content" />
            <View style={styles.container}>
                <Header
                    title={'Offer Details'}
                    headerTitleStyle={styles.headerTitleStyle}
                    backButtonFillColor={colors.lightBlack}
                />
                {!state.appSkeltonLoader ? (
                    <>
                        <View style={styles.offerDetailsContainer}>
                            <View style={styles.card}>
                                {/* Offer Details Header */}
                                <View style={styles.offerDetailsHeader}>
                                    <View style={styles.offerIconContainer}>
                                        <GiftBoxIconTeal />
                                    </View>
                                    <View style={isMyOffer ? styles.offerDetailsHeaderTextContainer : {}}>
                                        <TGText style={styles.offerIdText}>
                                            Offer ID{' '}
                                            <Text style={{ color: colors.tealRgb }}>#{offerDetails?.offer_id}</Text>
                                        </TGText>
                                        {isMyOffer && (
                                            <View style={styles.offerStatusContainer}>
                                                <TGText style={styles.offerStatusText}>Your Own Offer</TGText>
                                            </View>
                                        )}
                                    </View>
                                </View>
                                {/* Offer Details Body */}
                                <TierCard
                                    clubType={offerDetails?.club_type || 0}
                                    tier={offerDetails?.lowest_visible_tier || 0}
                                    wrapperStyle={styles.tierCard}
                                />
                                <View style={[styles.offerDetailsHeader, styles.subHeaderContainer]}>
                                    <ClubGolfIconTeal />
                                    <TGText style={styles.offerDetailsBodyText}>{offerDetails?.club_name}</TGText>
                                </View>
                                <View style={[styles.subHeaderContainer, styles.addressWrapper]}>
                                    <MapLocationIconNew />
                                    <TGText style={styles.offerAddressText}>{offerDetails?.club_address}</TGText>
                                </View>
                                <View
                                    style={[styles.offerDetailsHeader, styles.subHeaderContainer, { marginBottom: 0 }]}>
                                    <CalenderNewIcon />
                                    <TGText style={styles.offerDateText}>
                                        {offerDetails?.start_date === offerDetails?.end_date
                                            ? handleTimeFormat(offerDetails?.start_date)
                                            : `${handleTimeFormat(offerDetails?.start_date)} - ${handleTimeFormat(
                                                  offerDetails?.end_date,
                                              )}`}
                                    </TGText>
                                </View>
                                <View>
                                    <TGText style={styles.offerDetailsText}>{offerDetails?.details}</TGText>
                                </View>

                                {/* Offer Details Footer */}
                                <View style={styles.offerDetailsFooter}>
                                    <TGText style={styles.createdByText}>Created by</TGText>
                                    <View
                                        style={[
                                            styles.profileContainer,
                                            {
                                                columnGap: offerDetails?.creator_is_tg_ambassador
                                                    ? Spacing.SCALE_17
                                                    : Spacing.SCALE_10,
                                            },
                                        ]}>
                                        <TouchableOpacity
                                            style={styles.profileImageContainer}
                                            onPress={handleProfilePress}>
                                            {offerDetails?.creator_profile_photo ? (
                                                <FastImage
                                                    source={{
                                                        uri: offerDetails?.creator_profile_photo,
                                                        priority: FastImage.priority.high,
                                                        cache: FastImage.cacheControl.immutable,
                                                    }}
                                                    resizeMode={FastImage.resizeMode.cover}
                                                    style={styles.profileImage}
                                                />
                                            ) : (
                                                <Text style={styles.profileImageText}>
                                                    {offerDetails?.creator_name?.charAt(0).toUpperCase()}
                                                </Text>
                                            )}
                                            {offerDetails?.creator_is_tg_ambassador && (
                                                <View style={styles.ambassadorIconContainer}>
                                                    <TGAmbassadorLogo />
                                                </View>
                                            )}
                                        </TouchableOpacity>
                                        <View style={styles.profileTextContainer}>
                                            <TGText style={styles.offerDetailsFooterText}>
                                                {offerDetails?.creator_name}{' '}
                                            </TGText>
                                            {(offerDetails?.creator_is_tg_founder ||
                                                offerDetails?.creator_is_super_host) && (
                                                <View style={styles.badgeContainer}>
                                                    {offerDetails?.creator_is_tg_founder && (
                                                        <View style={[styles.badge, styles.founderBadge]}>
                                                            <Text style={styles.badgeText}>Founder Club Member</Text>
                                                        </View>
                                                    )}
                                                    {offerDetails?.creator_is_super_host && (
                                                        <View style={[styles.badge, styles.superHostBadge]}>
                                                            <Text style={styles.badgeText}>Super Host</Text>
                                                        </View>
                                                    )}
                                                </View>
                                            )}
                                        </View>
                                    </View>
                                </View>
                            </View>
                        </View>
                        {/* Buttons */}
                        <View
                            style={[
                                styles.buttonContainer,
                                {
                                    height: !isMyOffer
                                        ? Size.SIZE_64
                                        : isMyOffer && offerDetails?.creator_id === user?.id
                                        ? Size.SIZE_64
                                        : 0,
                                },
                            ]}>
                            {isMyOffer ? (
                                user.id === offerDetails?.creator_id ? (
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            justifyContent: 'space-between',
                                            alignItems: 'center',
                                        }}>
                                        <TouchableOpacity
                                            style={{
                                                height: Size.SIZE_40,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                marginTop: Spacing.SCALE_12,
                                            }}
                                            onPress={handleDeleteOffer}>
                                            <DeleteGreyIcon />
                                        </TouchableOpacity>
                                        <TGCustomIconButton
                                            text="Edit"
                                            onPress={handleEditOffer}
                                            btnStyle={[styles.buttonStyle, { width: '88%' }]}
                                            loading={false}
                                            Icon={() => <EditIcon />}
                                        />
                                    </View>
                                ) : null
                            ) : offerDetails?.requested ? (
                                <TealButtonNew
                                    text="Requested"
                                    onPress={() => {}}
                                    btnStyle={[styles.buttonStyle, { backgroundColor: colors.requestedBtnColor }]}
                                    textStyle={[styles.btnTextStyle, { color: colors.tealRgb }]}
                                    disabled
                                />
                            ) : (
                                <TealButtonNew
                                    text="Request Against Offer"
                                    onPress={handleRequestAgainstOffer}
                                    btnStyle={styles.buttonStyle}
                                    textStyle={styles.btnTextStyle}
                                />
                            )}
                        </View>
                    </>
                ) : (
                    <RequestScreenSkelton screen="OfferDetail" />
                )}
            </View>
        </>
    );
};

export default OfferDetails;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.whiteRGB,
    },
    headerTitleStyle: {
        color: colors.lightBlack,
        textAlign: 'left',
        marginLeft: Spacing.SCALE_10,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_1,
    },
    offerDetailsContainer: {
        flex: 1,
        paddingHorizontal: Spacing.SCALE_16,
        backgroundColor: colors.screenBG,
        paddingVertical: Spacing.SCALE_12,
    },
    card: {
        backgroundColor: colors.whiteRGB,
        borderRadius: Spacing.SCALE_10,
        padding: Spacing.SCALE_12,
    },
    offerIconContainer: {
        height: Size.SIZE_40,
        width: Size.SIZE_40,
        backgroundColor: colors.tealRGBAColor,
        borderRadius: 40,
        alignItems: 'center',
        justifyContent: 'center',
    },
    offerDetailsHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_12,
        marginBottom: Spacing.SCALE_20,
    },
    offerIdText: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.dark_charcoal,
    },
    offerDetailsBodyText: {
        fontSize: Typography.FONT_SIZE_18,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.dark_charcoal,
    },
    subHeaderContainer: {
        columnGap: Spacing.SCALE_10,
        marginBottom: Spacing.SCALE_8,
    },
    offerDateText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_18,
        color: colors.greyRgb,
    },
    offerDetailsText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_20,
        color: colors.dark_charcoal,
        marginVertical: Spacing.SCALE_19,
    },
    offerDetailsFooter: {
        paddingHorizontal: Spacing.SCALE_12,
        paddingVertical: Spacing.SCALE_10,
        borderRadius: Size.SIZE_12,
        backgroundColor: colors.greyRgba,
        rowGap: Spacing.SCALE_4,
    },
    offerDetailsFooterText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
    },
    profileImageContainer: {
        height: Size.SIZE_40,
        width: Size.SIZE_40,
        borderRadius: Size.SIZE_40,
        backgroundColor: colors.tealRgb,
        alignItems: 'center',
        justifyContent: 'center',
    },
    profileContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    badgeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: Spacing.SCALE_8,
        marginBottom: Spacing.SCALE_10,
    },
    badge: {
        paddingVertical: Spacing.SCALE_5,
        paddingHorizontal: Spacing.SCALE_8,
        borderRadius: Size.SIZE_6,
    },
    founderBadge: {
        backgroundColor: colors.opacityTeal,
    },
    superHostBadge: {
        backgroundColor: colors.badgeLabelBackgroundColor,
        borderColor: colors.darkteal,
    },
    badgeText: {
        fontSize: Typography.FONT_SIZE_10,
        color: colors.darkteal,
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Typography.FONT_SIZE_10,
    },
    profileTextContainer: {
        rowGap: Spacing.SCALE_4,
    },
    offerStatusContainer: {
        backgroundColor: colors.greyRgba,
        paddingVertical: Spacing.SCALE_5,
        paddingHorizontal: Spacing.SCALE_8,
        borderRadius: Size.SIZE_6,
        width: Size.SIZE_90,
    },
    offerStatusText: {
        fontSize: Typography.FONT_SIZE_10,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.dark_charcoal,
        lineHeight: Size.SIZE_10,
    },
    offerDetailsHeaderTextContainer: {
        rowGap: Spacing.SCALE_4,
    },
    buttonContainer: {
        position: 'absolute',
        bottom: Spacing.SCALE_1,
        height: Size.SIZE_64,
        backgroundColor: colors.whiteRGB,
        width: '100%',
        paddingHorizontal: Spacing.SCALE_16,
        ...Platform.select({
            ios: {
                shadowColor: colors.shadowColor,
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 4,
            },
            android: {
                elevation: 10,
                shadowOffset: {
                    width: 0,
                    height: 5,
                },
                shadowColor: colors.shadowColor,
                shadowRadius: 4,
                shadowOpacity: 0.5,
            },
        }),
    },
    buttonStyle: {
        width: '100%',
        height: Size.SIZE_40,
        borderRadius: Size.SIZE_8,
        backgroundColor: colors.tealRgb,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 0,
        marginTop: Spacing.SCALE_12,
    },
    btnTextStyle: {
        fontSize: Size.SIZE_14,
        lineHeight: Size.SIZE_18,
        color: colors.white,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
    offerAddressText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        lineHeight: Size.SIZE_18,
        color: colors.fadeBlack,
        width: '90%',
    },
    addressWrapper: {
        flexDirection: 'row',
        columnGap: Spacing.SCALE_10,
        marginBottom: Spacing.SCALE_8,
    },
    profileImageText: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        lineHeight: Size.SIZE_18,
        color: colors.whiteRGB,
    },
    profileImage: {
        height: Size.SIZE_40,
        width: Size.SIZE_40,
        borderRadius: Size.SIZE_40,
    },
    ambassadorIconContainer: {
        position: 'absolute',
        top: -8,
        right: -8,
        borderRadius: 50,
        padding: 2,
        backgroundColor: colors.whiteRGB,
        borderWidth: 1,
        borderColor: colors.lightgray,
        alignItems: 'center',
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    createdByText: {
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.lightBlack,
    },
    tierCard: {
        borderRadius: Size.SIZE_6,
        marginBottom: Spacing.SCALE_8,
    },
});
