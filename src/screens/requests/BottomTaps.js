import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import React, { useContext, useState } from 'react';
import requested from '../../../assets/images/svg/requested.svg';
import received from '../../../assets/images/svg/received.svg';
import create from '../../../assets/images/svg/create.svg';
import history from '../../../assets/images/svg/history.svg';
import requestedActive from '../../../assets/images/svg/requested-active.svg';
import receivedActive from '../../../assets/images/svg/received-active.svg';
import historyActive from '../../../assets/images/svg/history-active.svg';
import createActive from '../../../assets/images/svg/create-active.svg';


import TabIcon from './TabIcon';

import CreateRequest from './CreateRequest';
import HistoryRequest from './HistoryRequest';
import ReceivedRequest from './ReceivedRequest';
import RequestedRequest from './RequestedRequest';
import { fetcher } from '../../../service/fetcher';
import { tokenCheckURL } from '../../../service/EndPoint';
import { AuthContext } from '../../../context/AuthContext';
import { useNavigation } from '@react-navigation/core';

const Tab = createBottomTabNavigator();

const BottomTaps = ({ unreadChannels }) => {
    const { user } = useContext(AuthContext);
    const navigation = useNavigation()
    const [tokenAccess, setTokenAccess] = useState(false)
    return (
        <Tab.Navigator
            screenOptions={{
                headerShown: false,
                tabBarStyle: { backgroundColor: '#E5E5E5' },
                tabBarItemStyle: { marginBottom: 5, borderTopWidth: 1 },
                tabBarLabelStyle: { fontSize: 9 },
                tabBarActiveTintColor: '#098089',
                tabBarInactiveTintColor: '#4A4A4A',

            }} >

            <Tab.Screen
                name='RECEIVED'
                component={ReceivedRequest}
                options={{
                    tabBarIcon: ({ focused }) => {
                        return <TabIcon Icon={focused ? receivedActive : received} />;
                    },
                }}
                initialParams={unreadChannels}
            />
            <Tab.Screen
                name='REQUESTED'
                component={RequestedRequest}
                options={{
                    tabBarIcon: ({ focused }) => {
                        return <TabIcon Icon={focused ? requestedActive : requested} />;
                    },
                }}
                initialParams={unreadChannels}
            />

            <Tab.Screen
                name='HISTORY'
                component={HistoryRequest}
                options={{
                    tabBarIcon: ({ focused }) => {
                        return <TabIcon Icon={focused ? historyActive : history} />;
                    },
                }}
                initialParams={unreadChannels}
            />
            <Tab.Screen
                name='CREATE REQUEST'
                component={CreateRequest}
                options={{
                    tabBarIcon: ({ focused }) => {
                        return <TabIcon Icon={focused ? createActive : create} />;
                    },

                }}
                listeners={{
                    tabPress: e => {
                        // Prevent default action
                        e.preventDefault();
                        fetcher({
                            endpoint: tokenCheckURL,
                            method: 'POST',
                            body:
                            {
                                user_id: user?.id
                            }
                        }).then(res => {
                            if (res?.canCreate) {
                                navigation.navigate('CREATE REQUEST')
                            } else alert(res?.message)

                        })

                    },
                }}
            />
        </Tab.Navigator>
    );
};

export default BottomTaps;
