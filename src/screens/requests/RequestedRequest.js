import React, { useContext, useEffect, useLayoutEffect, useState } from 'react';
import {
    FlatList,
    RefreshControl,
    StyleSheet,
    View,
    ActivityIndicator,
} from 'react-native';

import RequestCardNew from '../../components/layout/requests/RequestCardnew';
import { AuthContext } from '../../context/AuthContext';
import {
    requestedOpenURLv4,
    requestedAcceptedURLv4,
    requestedHistoryURLv4,
} from '../../service/EndPoint';
import { getRequests } from './Utils';
import { colors } from '../../theme/theme';
import useClient from '../../hooks/useClient';
import ReviewModalNew from '../../components/modals/ReviewModalNew';
import RequestDeleteModal from '../../components/modals/RequestDeleteModal';
import TGNoResult from '../../components/layout/TGNoResult';
import { GlobalContext } from '../../context/contextApi';
import NewOptionTab from './NewOptionTab';
import TGLoader from '../../components/layout/TGLoader';
import { PAGINATION_LIMIT, REQUEST_CHAT_GROUP } from '../my-TG-Stream-Chat/client';
import NewScreenHeader from '../../components/layout/NewScreenHeader';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import RequestHistoryFilter from './RequestHistoryFilter';
import EmptyHostIcon from '../../assets/images/EmptyHostIcon.svg';
import { StreamChatContext } from '../../context/StreamChatContext';


export default function RequestedRequest({ route }) {
    const { user } = useContext(AuthContext);
    const { state, actions } = useContext(GlobalContext);
    const { client } = useContext(StreamChatContext);
    const navigation = useNavigation();
    const isFocused = useIsFocused()
    const { unreadChannels } = state;

    const [refreshing, setRefreshing] = useState(false);
    const [activeTab, setActiveTab] = useState('open');

    const [requestedOpen, setRequestedOpen] = useState();
    const [requestedAccepted, setRequestedAccepted] = useState();
    const [requestedHistory, setRequestedHistory] = useState();
    const [reviewModal, setReviewModal] = useState();

    const [showDeleteModal, setshowDeleteModal] = useState(false);
    const [deleteRequest, setDeleteRequest] = useState();

    const [acceptRequestModal, setAcceptRequestModal] = useState();
    const [dateRangeModal, setDateRangeModal] = useState();
    const [screenLoader, setScreenLoader] = useState(false);
    const [currentPage, setCurrentPage] = useState(0);
    const [totalHistoryPage, setTotalHistoryPage] = useState(0);
    const [showFilterOption, setShowFilterOption] = useState(false);
    const [filter, setFilter] = useState('all');
    const [loading, setLoading] = useState(false);
    const [streamUnreadChannel, setStreamUnreadChannel] = useState([])

    useLayoutEffect(()=>{
        setStreamUnreadChannel([])
    }, [])

    useEffect(() => {
            // getStreamRequestChannel();
    }, [navigation, state?.currentTab, isFocused]);

    useEffect(() => {
        const messageNewEventListener = client.on('message.new', async (event) => {
            // getStreamRequestChannel();
        });

        return () => {
            messageNewEventListener?.unsubscribe();
        };
    }, [client]);

    //Get stream unread messages
    const getStreamRequestChannel = async () => {
        let page = 0;
        while (1) {
            const channels = await client?.queryChannels(
                {
                    type: REQUEST_CHAT_GROUP,
                    members: { $in: [user?.id] },
                    has_unread: true,
                },
                {},
                { limit: 30, offset: 30 * page },
            );
            page = page + 1;
            if (!channels?.length) {
                break;
            } else {
                let requestIds = channels.map((data) => data?.data?.request_id);
                const updatedUnreadChannels = Array.from(new Set([...requestIds]));
                setStreamUnreadChannel(updatedUnreadChannels)
            }
        }
    };

    useEffect(() => {
        // actions.setUnreadChannels([...streamUnreadChannel]);
    }, [streamUnreadChannel]);

    useEffect(() => {
        setActiveTab(route.params?.activeTab);
    }, [route.params]);

    useEffect(() => {
        const unsubscribe = navigation.addListener('focus', autoFocus);
        // Return the function to unsubscribe from the event so it gets removed on unmount
        return unsubscribe;
    }, [navigation]);

    const autoFocus = () => {
        _onRefresh()
    }

    useEffect(() => {
        if (activeTab === 'open' && !requestedOpen) {
            let data = {
                userId: user?.id,
            };
            getRequests(requestedOpenURLv4, data).then((requests) => {
                setRequestedOpen(requests?.data);
            });
        } else if (activeTab === 'accepted' && !requestedAccepted) {
            let data = {
                userId: user?.id,
            };
            getRequests(requestedAcceptedURLv4, data).then((requests) => {
                setRequestedAccepted(requests?.data);
            });
        } else if (activeTab === 'history' && !requestedHistory) {
            let data = {
                userId: user?.id,
                limit: PAGINATION_LIMIT,
                page: 1,
                filter
            };
            getRequests(requestedHistoryURLv4, data).then((requests) => {
                setCurrentPage(requests?.currentPage);
                setTotalHistoryPage(requests?.totalPages);
                setRequestedHistory(requests?.data);
            });
        }
    }, [activeTab]);

    const _onRefresh = () => {
        if (activeTab === 'open') {
            let data = {
                userId: user?.id,
            };
            getRequests(requestedOpenURLv4, data).then((requests) => {
                setRequestedOpen(requests?.data);
            });
        } else if (activeTab === 'accepted') {
            let data = {
                userId: user?.id,
            };
            getRequests(requestedAcceptedURLv4, data).then((requests) => {
                setRequestedAccepted(requests?.data);
            });
        } else if (activeTab === 'history') {
            let data = {
                userId: user?.id,
                limit: PAGINATION_LIMIT,
                page: 1,
                filter
            };
            getRequests(requestedHistoryURLv4, data).then((requests) => {
                setCurrentPage(requests?.currentPage);
                setTotalHistoryPage(requests?.totalPages);
                setRequestedHistory(requests?.data);
            });
        }
    };

    const handleOnReachEnd = () => {
        if (activeTab === 'history') {
            let data = {
                userId: user?.id,
                limit: PAGINATION_LIMIT,
                page: currentPage + 1,
                filter
            };
            getRequests(requestedHistoryURLv4, data).then((requests) => {
                setScreenLoader(false);
                setCurrentPage(requests?.currentPage);
                setTotalHistoryPage(requests?.totalPages);
                setRequestedHistory([...requestedHistory, ...requests?.data]);
            });
        }
    };

    const requests =
        activeTab === 'open'
            ? requestedOpen
            : activeTab === 'accepted'
                ? requestedAccepted
                : requestedHistory;

    if (!requests)
        return (
            <View
                style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                }}>
                <ActivityIndicator color={colors.darkteal} size="large" />
            </View>
        );

    return (
        <>
            <View style={{ flex: 1 }}>
                <NewScreenHeader
                    title="Requested"
                    disableSearch={true}
                    showBackBtn={true}
                    onPressGoBack={() => {
                        actions.setCurrentTab(1); // Set current tab in context
                        navigation.goBack();
                    }}
                    showNotification={false}
                    showFilter={
                        route.params?.activeTab === 'history' ? true : false
                    }
                    openFilterModal={() => setShowFilterOption((prev) => !prev)}
                />
                {/* back button header component */}
                <NewOptionTab
                    tabOption={route.params?.activeTab}
                    tabName="Requested"
                />
                {requests && requests?.length <= 0 ? (
                    <View style={{
                        justifyContent: 'center',
                        alignItems: 'center', flex: 1
                    }}>
                        <EmptyHostIcon />
                        <TGNoResult
                            message={`There are no requests listed.\nPlease check back later.`}
                            _onRefresh={_onRefresh}
                            isRefresh
                        />
                    </View>
                ) : (
                    <FlatList
                        data={requests}
                        style={{ marginTop: 8 }}
                        refreshControl={
                            <RefreshControl
                                refreshing={refreshing}
                                onRefresh={_onRefresh}
                            />
                        }
                        onEndReachedThreshold={0.5}
                        onEndReached={() => {
                            if (
                                requestedHistory?.length ===
                                currentPage * PAGINATION_LIMIT &&
                                currentPage < totalHistoryPage
                            ) {
                                if (activeTab === 'history') {
                                    setScreenLoader(true);
                                    handleOnReachEnd();
                                }
                            }
                        }}
                        renderItem={({ item, index }) => {
                            return (
                                <RequestCardNew
                                    request={item}
                                    type={`requested-${activeTab}`}
                                    hasUnreadMessages={unreadChannels?.filter(
                                        (data) => data === item?.request_id,
                                    )}
                                    onDeclineRequest={() => {
                                        setDeleteRequest(item);
                                        setshowDeleteModal(true);
                                    }}
                                    markGameCompleted={() => {
                                        setDeleteRequest(item);
                                        setReviewModal(true);
                                    }}
                                    acceptRequestModalState={[
                                        acceptRequestModal,
                                        setAcceptRequestModal,
                                    ]}
                                    onRefresh={_onRefresh}
                                    rangeModal={[
                                        dateRangeModal,
                                        setDateRangeModal,
                                    ]}
                                    setScreenLoader={setScreenLoader}
                                />
                            );
                        }}
                        keyExtractor={(item) => item?.request_id}
                    />
                )}

                <RequestDeleteModal
                    type={`requested-${activeTab}`}
                    modalState={[showDeleteModal, setshowDeleteModal]}
                    request={deleteRequest}
                    goBack={_onRefresh}
                />

                {reviewModal && (
                    <ReviewModalNew
                        request={deleteRequest}
                        isMyRequest={true}
                        closeModal={() => {
                            setReviewModal();
                            _onRefresh();
                        }}
                    />
                )}
            </View>
            {screenLoader && (
                <TGLoader loading={screenLoader} loaderColor={colors.tealRgb} />
            )}
            {showFilterOption && (
                <RequestHistoryFilter
                    popupState={[showFilterOption, setShowFilterOption]}
                    filterState={[filter, setFilter]}
                    getHistory={_onRefresh}
                    loadingState={[loading, setLoading]}
                />
            )}
        </>
    );
}
const styles = StyleSheet.create({
    button: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#E05E52',
        borderRadius: 8,
        height: 35,
    },
    input: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 12,
        borderBottomColor: '#808080',
        borderBottomWidth: 0.5,
        paddingVertical: 10,
        paddingHorizontal: 5,
    },
});
