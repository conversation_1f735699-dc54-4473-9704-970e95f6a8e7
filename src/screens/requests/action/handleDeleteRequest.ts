import showToast from '../../../components/toast/CustomToast';
import { DELETE_REQUESTED_REQUEST } from '../../../service/EndPoint';
import { fetcher } from '../../../service/fetcher';
import {
    REQUEST_CANCELLED_AND_MOVED_TO_HISTORY,
    REQUEST_MOVED_TO_HISTORY,
    SUCCESS,
} from '../../../utils/constants/strings';

export const handleDeleteRequestedRequest = (body: any, navigation: any, callBack: () => void, type: string) => {
    try {
        return fetcher({
            endpoint: DELETE_REQUESTED_REQUEST,
            method: 'POST',
            body,
        }).then((res) => {
            if (res?.status) {
                navigation.goBack();
                callBack();
                showToast({
                    type: SUCCESS,
                    header: '',
                    message:
                        type === 'requested-accepted'
                            ? REQUEST_CANCELLED_AND_MOVED_TO_HISTORY
                            : REQUEST_MOVED_TO_HISTORY,
                });
            } else {
                showToast({});
            }
        });
    } catch (error) {
        showToast({});
    }
};
