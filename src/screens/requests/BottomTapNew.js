import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import React, { useContext, useEffect, useState } from 'react';
import requested from '../../assets/images/svg/requested.svg';
import received from '../../assets/images/svg/received.svg';
import create from '../../assets/images/svg/create.svg';
import history from '../../assets/images/svg/history.svg';
import requestedActive from '../../assets/images/svg/requested-active.svg';
import receivedActive from '../../assets/images/svg/received-active.svg';
import historyActive from '../../assets/images/svg/history-active.svg';
import createActive from '../../assets/images/svg/create-active.svg';


import TabIcon from './TabIcon';

import CreateRequest from './CreateRequest';
import HistoryRequest from './HistoryRequest';
import ReceivedRequest from './ReceivedRequest';
import RequestedRequest from './RequestedRequest';
import { fetcher } from '../../service/fetcher';
import { tokenCheckURL } from '../../service/EndPoint';
import { AuthContext } from '../../context/AuthContext';
import { useNavigation } from '@react-navigation/core';
import { View } from 'react-native';
import TabItem from './TabItem';

const Tab = createBottomTabNavigator();

const BottomTapNew = ({ unreadChannels }) => {
    const { user } = useContext(AuthContext);
    const screens = ['RECEIVED', 'REQUESTED', 'CREATE REQUEST',]
    const [selectedPosition, setPosition] = useState(0)

    const matchTokenActive = () => {
        fetcher({
            endpoint: tokenCheckURL,
            method: 'POST',
            body:
            {
                user_id: user?.id
            }
        }).then(res => {
            if (res?.canCreate)
                setPosition(2)
            else {
                alert(res?.message)
            }
        })
    }

    const getIcon = (index) => {
        switch (index) {
            case 0:
                return selectedPosition == 0 ? receivedActive : received
            case 1:
                return selectedPosition == 1 ? requestedActive : requested
            case 2:
                // return selectedPosition == 2 ? historyActive : history

                // case 3:
                return selectedPosition == 2 ? createActive : create

            default:
                break;
        }
    }

    const getRequestScreen = (index) => {
        switch (index) {
            case 0:
                return <ReceivedRequest unreadChannels={unreadChannels} />
            case 1:
                return <RequestedRequest unreadChannels={unreadChannels} />
            case 2:
                // return <HistoryRequest  unreadChannels={unreadChannels} />

                // case 3:
                return <CreateRequest setPosition={setPosition} />

            default:
                break;
        }
    }

    return (
        <View style={{ flex: 1 }}>

            <View style={{ flex: 1 }}>
                {getRequestScreen(selectedPosition)}
            </View>
            <View
                style={{
                    flexDirection: 'row',


                }}>
                {screens.map((name, index) =>
                    <TabItem name={name} Icon={getIcon(index)} isSelected={selectedPosition === index} action={() => {

                        if (index === 2)

                            matchTokenActive()
                        else setPosition(index)

                    }} />
                )}

            </View>


        </View>
    );

};

export default BottomTapNew;
