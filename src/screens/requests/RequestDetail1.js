import React, { useContext, useEffect } from 'react'
import { View, Text, TouchableOpacity, SafeAreaView, ScrollView, FlatList, StyleSheet, Alert, RefreshControl, ActivityIndicator } from 'react-native'
import { useState } from 'react'
import { Back, Check, Cross, Delete, DocEdit, Edit, Golf, Group, Message, MessageDark } from '../../assets/images/svg'
import TGText from '../../components/fields/TGText'
import { fetcher } from '../../service/fetcher'
import { gameUserInformation, requestDetailsURL } from '../../service/EndPoint'
import ReviewModalNew from '../../components/modals/ReviewModalNew'
import AcceptRequestModalNew from '../../components/modals/AcceptRequestModalNew'
import { AuthContext } from '../../context/AuthContext'
import useClient from '../../hooks/useClient'
import moment from 'moment';
import CustomRM from '../../components/layout/requests/customRM';
import { UPDATE_USER } from '../../graphql/mutations/user';
import RequestRequesterCard from '../../components/layout/requests/RequestRequestorCard';
import RequestHostCard from '../../components/layout/requests/RequestHostCard';
import RequestDeleteModal from '../../components/modals/RequestDeleteModal';
import { Spacing, Typography } from '../../utils/responsiveUI';
import useQuery from '../../hooks/useQuery';
import { GAME_INFO } from '../../graphql/queries/requestDetailsQuerry';
import { Collapse, Expand } from '../../assets/images/svg'
import { handleTimeFormat } from '../../components/timeFormatComponent/handleTimeFormat';
import dateFormatter from '../../utils/helpers/dateFormatter';


export default function RequestDetail1({ navigation, route }) {
    console.log("**********")
    const { user } = useContext(AuthContext);
    const client = useClient();

    const [channels, setChannels] = useState([]);
    const [request, setRequest] = useState(route?.params?.request);
    const [refreshing, setRefreshing] = useState(false)

    const [unreadChannels, setUnreadChannels] = useState([]);
    const [unreadMessages, setUnreadMessages] = useState([]);

    const { requestId, actionType, _onRefresh, params } = route?.params
    console.log("route?.params", route?.params)
    console.log("4")
    const [requester, setRequester] = useState()
    const [type, setType] = useState(actionType)

    const [gameHost, setGameHost] = useState()


    const [reviewModal, setReviewModal] = useState();
    const [acceptRequestModal, setAcceptRequestModal] = useState();

    const [isLoading, setLoading] = useState(false)

    const [showModal, setShowModal] = useState(false)
    const [reason, setReason] = useState('')
    const [reasonError, setReasonError] = useState('')

    const [isVisible, setVisible] = useState(false);
    const [isAccepted, setIsAccepted] = useState(false)
    const [has_messages, setHas_messages] = useState()
    const [request1, setRequest1] = useState()
    const [actionType1, setActionType1] = useState()
    const [isloading1, setIsloading1] = useState(false)
    const [isDetailVisible, setDetailVisible] = useState(false)


    const { data } = useQuery(GAME_INFO, {
        request_id: request?.request_id,
    })
    console.log("request details =>>1", data?.request_by_pk?.criteria);


    useEffect(() => {
        navigateToRequest(navigation, route?.params?.params, _onRefresh)
    }, [])

    React.useEffect(() => {
        const unsubscribe = navigation.addListener('focus', refresh);
        // Return the function to unsubscribe from the event so it gets removed on unmount
        return unsubscribe;
    }, [navigation]);
    const refresh = () => {
        navigateToRequest(navigation, route?.params?.params, _onRefresh)
    }

    function navigateToRequest(navigation, params, _onRefresh) {
        setRequest1()
        setActionType1()
        console.log("params-=-=", params)
        setIsloading1(true)

        const { user_id, request_id, channel_url, data = true } = params

        console.log('requestDetails', {
            user_id,
            request_id,
            channel_url,
            data
        });
        console.log("jsjsjs", params)

        if (request_id)
            fetcher({
                method: 'POST',
                endpoint: requestDetailsURL,
                body: {
                    user_id,
                    request_id,
                    data
                },

            }).then(res => {
                console.log('respose==<><>', res);
                if (res?.code === 200) {
                    setIsloading1(false)
                    const request = res?.data[0]
                    setRequest1(request)
                    const actionType = res?.tab.replace('/', '-').toLowerCase()
                    setActionType1(actionType)
                    console.log("==>>", request)
                } else {
                    alert(res.error)
                    //   navigation.navigate('Requests')
                }
            })
        else navigation.navigate('Requests')
    }

    useEffect(() => {
        if (type.includes('received')) {
            getGameUserInformation({
                request_id: request?.request_id,
                host_id: request?.requestor_user_id
            }).then(res => {
                setLoading(false)
                setRequester(res?.data)
            })

        } else if (request?.game_host_user_id) {
            getGameUserInformation({
                request_id: request?.request_id,
                host_id: request?.game_host_user_id
            }).then(res => {
                console.log('GameHost', res?.data);
                setLoading(false)
                setGameHost(res?.data)
            })
        }
    }, []);

    useEffect(() => {

    }, [requestId]);

    React.useEffect(() => {
        const unsubscribe = navigation.addListener('focus', fetchMarketPlaceData);
        // Return the function to unsubscribe from the event so it gets removed on unmount
        return unsubscribe;
    }, [navigation]);
    const fetchMarketPlaceData = () => {
        console.log("back")
        // onRefresh()
    }


    useEffect(() => {
        // if (channels.length > 0) {
        //     setUnreadChannels(
        //         channels
        //             .filter(({ unreadMessageCount }) => unreadMessageCount > 0)
        //             .map(({ data, memberMap }) => { return { data, memberMap } }),
        //     );
        // }
    }, [channels]);


    useEffect(() => {
        if (unreadChannels.length > 0) {
            setUnreadMessages(unreadChannels.filter(({ data }) => data === request?.request_id))
        }
    }, [unreadChannels]);

    const handleModalClose = () => {
        setVisible(false);
    };

    const handleContinue = () => {
        setVisible(false);
        if (isAccepted) {
            client.request(UPDATE_USER, {
                user_id: user?.id,
                user: {
                    additional_settings: {
                        showAcceptRequestPopup: false,
                        showCreateRequestPopup: user?.additional_settings?.showCreateRequestPopup
                    }
                },
            });
        }
        setTimeout(() => {
            acceptRequest()
        }, 100);
    };

    const showRequestPopup = () => {
        console.log('additional_settings', user?.additional_settings);
        if (user?.additional_settings?.showAcceptRequestPopup) {
            setVisible(true)
        } else acceptRequest();

    };

    const getGameUserInformation = async (body) => {
        return await fetcher({
            endpoint: gameUserInformation,
            method: 'POST',
            body
        })
    }

    function goBack() {
        navigation.goBack()
        if (_onRefresh)
            _onRefresh()
    }

    async function acceptRequest() {
        Alert.alert(
            'Accept Request',
            `Have you confirmed the logistics for #${request.game_id} with ${request.requestor_full_name}?`,
            [
                {
                    text: 'Cancel',
                    onPress: () => {
                        Alert.alert(
                            'You must finalize the logistics before accepting the request',
                            '',
                            [
                                {
                                    text: 'Cancel',
                                    onPress: () => console.log('Cancel Pressed'),
                                    style: 'cancel',
                                },
                                {
                                    text: ` Chat with ${request1 ? request1.requestor_full_name : request.requestor_full_name} `,
                                    onPress: async () => goToChatScreen(),
                                },
                            ],
                            { cancelable: false },
                        );
                    },
                    style: 'cancel',
                },
                {
                    text: 'Yes',
                    onPress: async () => {
                        setAcceptRequestModal({ request1, request, onRefresh: () => setType('received-accepted') });
                    },
                },
            ],
            { cancelable: false },
        );
    }

    async function showChatWarning() {
        Alert.alert(
            'Accepting Requests',
            'You need to initiate the chat session with the Requester first to Confirm Logistics before accepting it',
            [
                {
                    text: 'Cancel',
                    onPress:
                        () =>
                            console.log(
                                'Cancel Pressed',
                            ),
                    style: 'cancel',
                },
                {
                    text: 'Start Chat',
                    onPress: () => goToChatScreen(),
                },
            ],
        );
    }

    function goToChatScreen(host) {
        const { sendbird_channel_id, request_id, game_id, requestor_user_id, requestor_full_name, host_user_id } = request1

        const params = {
            type,
            request_id,
            game_id,
            channel_url: host?.sendbird_channel_id ? host?.sendbird_channel_id : sendbird_channel_id,
            requestor_user_id,
            requestor_full_name,
            host_user_id: host?.host_user_id ? host?.host_user_id : host_user_id,
        }
        navigation.navigate('ChatDetails', params);
    }

    const sortedArray = (hosts, host_user_id) => {
        return hosts.sort(function (a, b) {
            if (host_user_id === a.host_user_id) return -1;
            else return 1;
        });
    }
    const getData = (data) => {
        
        let englishFluency = '';
        data?.map((data) => {
            data = data.substring(0,1).toUpperCase()+data.substring(1, data.length)
            englishFluency = englishFluency + data + ','
        })
        englishFluency = englishFluency.substring(0, englishFluency.length - 1)
        return englishFluency
    }

    const checkKeyAvailable = (data) => {
        let res = data.hasOwnProperty('accompanied_only') && data.hasOwnProperty('all_ages') && data.hasOwnProperty('englishFluency') && data.hasOwnProperty('pace') && data.hasOwnProperty('sendToPrivateNetwork') && data.hasOwnProperty('playAsCouple')
        return res
    }

    const requestDate = (
        (request1
            ? request1?.status
            : request?.status === 'completed' ||
              (actionType1
                  ? actionType1.includes('accepted')
                  : type.includes('accepted'))) && request1
            ? request1?.game_date
            : request?.game_date
    )
        ? handleTimeFormat(request1 ? request1?.game_date : request?.game_date)
        : dateFormatter(request?.start_date, request?.end_date);

    // console.log("GameHost",gameHost)
    return (
        <>
            {/* {
                isloading1 &&  */}
            <SafeAreaView style={{flex: 1, backgroundColor: 'white'}}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        height: 44,
                    }}>
                    <Text
                        style={{
                            fontSize: 16,
                            flex: 1,
                            textAlign: 'center',
                            color: '#000000',
                        }}>
                        Game ID #
                        {request1 ? request1?.game_id : request?.game_id}
                    </Text>
                    <TouchableOpacity
                        onPress={() => navigation.goBack()}
                        style={{position: 'absolute', padding: 12}}>
                        <Back />
                    </TouchableOpacity>
                </View>
                <ScrollView
                    style={{flex: 1, padding: 12}}
                    refreshControl={
                        <RefreshControl
                            refreshing={refreshing}
                            onRefresh={_onRefresh}
                        />
                    }>
                    <View
                        style={{
                            borderBottomWidth: 0.5,
                            height: 50,
                        }}>
                        <View
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                            }}>
                            <Golf />
                            <TGText
                                style={{
                                    flex: 1,
                                    marginLeft: 12,
                                    fontSize: 16,
                                    color: '#000000',
                                }}>
                                {request?.club_name}
                            </TGText>
                        </View>
                        <TGText
                            style={{
                                fontSize: 12,
                                fontFamily: 'Ubuntu-Light',
                                marginTop: 5,
                            }}>
                            {requestDate}
                        </TGText>
                    </View>
                    <View style={{flexDirection: 'row', marginTop: 14}}>
                        <DocEdit />
                        <View style={{marginHorizontal: 8}}>
                            <TGText style={{fontSize: 12.5, color: '#333'}}>
                                This request is{' '}
                                {request?.accompanied_only ? '' : 'not'} for
                                accompanied play only.
                            </TGText>
                            <TGText
                                style={{
                                    fontSize: 12.5,
                                    fontFamily: 'Ubuntu-Light',
                                    color: '#333',
                                }}>
                                {request?.message}
                            </TGText>
                        </View>
                    </View>
                    <View style={{flexDirection: 'row', marginTop: 12}}>
                        <Group />
                        <TGText style={{fontSize: 12.5, marginLeft: 12}}>
                            Number of people
                        </TGText>
                        <View
                            style={{
                                width: 20,
                                height: 20,
                                borderRadius: 10,
                                backgroundColor: '#098089',
                                alignItems: 'center',
                                justifyContent: 'center',
                                marginLeft: 6,
                            }}>
                            <TGText style={{fontSize: 12, color: 'white'}}>
                                {request?.number_of_players}
                            </TGText>
                        </View>
                    </View>

                    {type.includes('requested') &&
                        data &&
                        Object.keys(data).length > 0 &&
                        Object.keys(data?.request_by_pk?.criteria).length > 0 &&
                        checkKeyAvailable(data?.request_by_pk?.criteria) && (
                            <View
                                style={{
                                    backgroundColor: 'rgba(242, 242, 242, 1)',
                                    paddingHorizontal: 10,
                                    justifyContent: 'space-between',
                                    flexDirection: 'row',
                                    marginTop: 10,
                                    borderTopLeftRadius: 10,
                                    borderTopRightRadius: 10,
                                    borderBottomLeftRadius: isDetailVisible
                                        ? 0
                                        : 10,
                                    borderBottomRightRadius: isDetailVisible
                                        ? 0
                                        : 10,
                                }}>
                                <TGText
                                    style={{
                                        marginVertical: 16,
                                        color: '#098089',
                                    }}>
                                    View Game Information
                                </TGText>
                                <TouchableOpacity
                                    style={styles.buttonExpand}
                                    onPress={() =>
                                        setDetailVisible(!isDetailVisible)
                                    }>
                                    {isDetailVisible ? (
                                        <Collapse />
                                    ) : (
                                        <Expand />
                                    )}
                                </TouchableOpacity>
                            </View>
                        )}
                    {isDetailVisible && (
                        <View
                            style={{
                                backgroundColor: 'rgba(242, 242, 242, 1)',
                                borderBottomLeftRadius: 10,
                                borderBottomRightRadius: 10,
                            }}>
                            <View style={{paddingHorizontal: 10}}>
                                <View
                                    style={{
                                        borderWidth: 0.4,
                                        borderColor: 'rgba(128, 128, 128, 1)',
                                        opacity: 0.8,
                                        paddingHorizontal: 20,
                                    }}
                                />
                            </View>
                            <Text
                                style={{
                                    color: 'rgba(51, 51, 51, 1)',
                                    fontSize: Typography.FONT_SIZE_12,
                                    marginLeft: 10,
                                    fontWeight: '500',
                                    marginTop: 20,
                                }}>
                                Request sent to members meeting the following
                                criteria:
                            </Text>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    paddingHorizontal: 10,
                                }}>
                                <Text
                                    style={{
                                        color: 'rgba(51, 51, 51, 1)',
                                        fontSize: Typography.FONT_SIZE_12,
                                        fontWeight: '500',
                                        marginTop: 30,
                                    }}>
                                    Pace
                                </Text>
                                <Text
                                    style={{
                                        color: 'rgba(51, 51, 51, 1)',
                                        fontSize: Typography.FONT_SIZE_12,
                                        marginLeft: 10,
                                        fontWeight: '500',
                                        marginTop: 30,
                                    }}>
                                    {data?.request_by_pk?.criteria?.pace
                                        ?.length === 3
                                        ? 'All'
                                        : getData(
                                              data?.request_by_pk?.criteria
                                                  ?.pace,
                                          )}
                                </Text>
                            </View>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    paddingHorizontal: 10,
                                }}>
                                <Text
                                    style={{
                                        color: 'rgba(51, 51, 51, 1)',
                                        fontSize: Typography.FONT_SIZE_12,
                                        fontWeight: '500',
                                        marginTop: 30,
                                    }}>
                                    Golf Index
                                </Text>
                                <Text
                                    style={{
                                        color: 'rgba(51, 51, 51, 1)',
                                        fontSize: Typography.FONT_SIZE_12,
                                        fontWeight: '500',
                                        marginTop: 30,
                                    }}>
                                    {data?.request_by_pk?.criteria?.handicap
                                        .length === 3
                                        ? 'All'
                                        : getData(
                                              data?.request_by_pk?.criteria
                                                  ?.handicap,
                                          )}
                                </Text>
                            </View>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    paddingHorizontal: 10,
                                }}>
                                <Text
                                    style={{
                                        color: 'rgba(51, 51, 51, 1)',
                                        fontSize: Typography.FONT_SIZE_12,
                                        fontWeight: '500',
                                        marginTop: 30,
                                    }}>
                                    English Fluency
                                </Text>
                                <Text
                                    style={{
                                        color: 'rgba(51, 51, 51, 1)',
                                        fontSize: Typography.FONT_SIZE_12,
                                        fontWeight: '500',
                                        marginTop: 30,
                                    }}>
                                    {data?.request_by_pk?.criteria
                                        ?.englishFluency.length === 4
                                        ? 'All'
                                        : getData(
                                              data?.request_by_pk?.criteria
                                                  ?.englishFluency,
                                          )}
                                </Text>
                            </View>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    paddingHorizontal: 10,
                                }}>
                                <Text
                                    style={{
                                        color: 'rgba(51, 51, 51, 1)',
                                        fontSize: Typography.FONT_SIZE_12,
                                        fontWeight: '500',
                                        marginTop: 30,
                                    }}>
                                    Age
                                </Text>
                                <Text
                                    style={{
                                        color: 'rgba(51, 51, 51, 1)',
                                        fontSize: Typography.FONT_SIZE_12,
                                        fontWeight: '500',
                                        marginTop: 30,
                                    }}>
                                    {data?.request_by_pk?.criteria?.all_ages
                                        ? 'All'
                                        : data?.request_by_pk?.criteria
                                              ?.max_age +
                                          ' Years' +
                                          ' - ' +
                                          data?.request_by_pk?.criteria
                                              ?.min_age +
                                          ' Years'}
                                </Text>
                            </View>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    paddingHorizontal: 10,
                                }}>
                                <Text
                                    style={{
                                        color: 'rgba(51, 51, 51, 1)',
                                        fontSize: Typography.FONT_SIZE_12,
                                        fontWeight: '500',
                                        marginTop: 30,
                                    }}>
                                    Private Network Only
                                </Text>
                                <Text
                                    style={{
                                        color: 'rgba(51, 51, 51, 1)',
                                        fontSize: Typography.FONT_SIZE_12,
                                        fontWeight: '500',
                                        marginTop: 30,
                                    }}>
                                    {data?.request_by_pk?.criteria
                                        ?.sendToPrivateNetwork
                                        ? 'Yes'
                                        : 'No'}
                                </Text>
                            </View>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    paddingHorizontal: 10,
                                }}>
                                <Text
                                    style={{
                                        color: 'rgba(51, 51, 51, 1)',
                                        fontSize: Typography.FONT_SIZE_12,
                                        fontWeight: '500',
                                        marginVertical: 30,
                                    }}>
                                    Play As A Couple
                                </Text>
                                <Text
                                    style={{
                                        color: 'rgba(51, 51, 51, 1)',
                                        fontSize: Typography.FONT_SIZE_12,
                                        fontWeight: '500',
                                        marginTop: 30,
                                    }}>
                                    {data?.request_by_pk?.criteria?.playAsCouple
                                        ? 'Yes'
                                        : 'No'}
                                </Text>
                            </View>
                        </View>
                    )}

                    <TGText
                        style={{
                            marginTop: 16,
                            marginBottom: 8,
                            color: '#098089',
                        }}>
                        {type.includes('received') ? 'Requester' : 'Host'}'s
                        Information
                    </TGText>
                    {type.includes('received') ? (
                        <RequestRequesterCard
                            isLoading={RequestRequesterCard}
                            requester={requester}
                        />
                    ) : request?.hosts?.length > 0 ? (
                        <FlatList
                            data={sortedArray(
                                request?.hosts,
                                request?.game_host_user_id,
                            )}
                            renderItem={({item}) => (
                                <RequestHostCard
                                    request={request}
                                    host={item}
                                    unreadMessages={unreadMessages}
                                    isLoading={isLoading}
                                    goToChatScreen={goToChatScreen}
                                    gameHost={gameHost}
                                />
                            )}
                            keyExtractor={(item, index) => index.toString()}
                        />
                    ) : (
                        <TGText
                            style={{
                                color: '#000',
                                textAlign: 'center',
                                marginTop: 20,
                            }}>
                            No host data for this request.
                        </TGText>
                    )}
                </ScrollView>

                {!requester?.userInfo?.deleted_at && (
                    <View style={{flexDirection: 'row', padding: 12}}>
                        <TouchableOpacity
                            style={[
                                styles.button,
                                {
                                    marginRight: 5,
                                    opacity:
                                        !request?.host_completed &&
                                        !request?.requestor_completed
                                            ? 1
                                            : 0.3,
                                },
                            ]}
                            onPress={() => {
                                if (
                                    !request?.host_completed &&
                                    !request?.requestor_completed
                                )
                                    setShowModal(true);
                                else {
                                    const message = request?.host_completed
                                        ? "This request can't be deleted as the host has already marked the game as complete"
                                        : "This Request can't be Declined as the Requestor has already marked the game as Completed.";
                                    alert(message);
                                }
                            }}>
                            {type.includes('received') &&
                            !type.includes('history-received') &&
                            !type.includes('received-history') ? (
                                <Cross />
                            ) : (
                                <Delete />
                            )}
                        </TouchableOpacity>
                        {(!actionType1?.includes('history') ||
                            !type.includes('history')) && (
                            <TouchableOpacity
                                style={[
                                    styles.button,
                                    {
                                        flex:
                                            actionType1?.includes('accepted') ||
                                            type.includes('accepted')
                                                ? 5
                                                : 1,
                                        flexDirection: 'row',
                                        backgroundColor:
                                            actionType1?.includes(
                                                'requested-open',
                                            ) || type.includes('requested-open')
                                                ? '#EBEBEB'
                                                : '#2E9360',
                                        marginHorizontal: 5,
                                    },
                                ]}
                                onPress={() => {
                                    // console.log("request1?.has_messages", request1?.has_messages);
                                    // console.log("actionType1?.includes('accepted')", actionType1?.includes('accepted'));
                                    // console.log("actionType1=>", actionType1);
                                    if (
                                        actionType1?.includes(
                                            'requested-open',
                                        ) ||
                                        type.includes('requested-open')
                                    ) {
                                        console.log(
                                            '1==>>',
                                            actionType1?.includes(
                                                'requested-open',
                                            ),
                                        );
                                        navigation.navigate('Create Request', {
                                            request,
                                        });
                                    } else if (
                                        actionType1?.includes(
                                            'received-open',
                                        ) ||
                                        type.includes('received-open')
                                    ) {
                                        console.log(
                                            '2==>>',
                                            actionType1?.includes(
                                                'received-open',
                                            ),
                                        );
                                        if (
                                            request1?.has_messages ||
                                            request?.has_messages
                                        ) {
                                            console.log('if');
                                            showRequestPopup();
                                            navigateToRequest(
                                                navigation,
                                                route?.params?.params,
                                                _onRefresh,
                                            );
                                        } else {
                                            console.log('else');
                                            showChatWarning();
                                        }
                                    } else if (
                                        actionType1?.includes(
                                            'received-accepted',
                                        ) ||
                                        type.includes('accepted')
                                    ) {
                                        // console.log("3==>>", actionType1?.includes('accepted'))
                                        setReviewModal(true);
                                    }
                                }}>
                                {actionType1?.includes('requested-open') ||
                                type.includes('requested-open') ? (
                                    <Edit />
                                ) : (
                                    <Check />
                                )}
                                {(actionType1?.includes('accepted') ||
                                    type.includes('accepted')) && (
                                    <TGText
                                        style={{
                                            color: '#FFFFFF',
                                            fontSize: 14,
                                            fontFamily: 'Ubuntu-Regular',
                                        }}>
                                        {' '}
                                        Mark Completed
                                    </TGText>
                                )}
                            </TouchableOpacity>
                        )}

                        {((!actionType1?.includes('history') &&
                            !actionType1?.includes('requested')) ||
                            (!type.includes('history') &&
                                !type.includes('requested'))) && (
                            <TouchableOpacity
                                style={[
                                    styles.button,
                                    {
                                        marginLeft: 5,
                                        backgroundColor:
                                            request1?.hosts ||
                                            request1?.has_messages ||
                                            request?.hosts ||
                                            request?.has_messages
                                                ? '#098089'
                                                : '#EBEBEB',
                                    },
                                ]}
                                onPress={() => goToChatScreen()}>
                                {request1?.hosts ||
                                request1?.has_messages ||
                                request?.hosts ||
                                request?.has_messages ? (
                                    <Message />
                                ) : (
                                    <MessageDark />
                                )}
                                {unreadMessages.length > 0 && (
                                    <View
                                        style={{
                                            position: 'absolute',
                                            zIndex: 20,
                                            backgroundColor: 'red',
                                            height: 10,
                                            width: 10,
                                            borderRadius: 5,
                                            top: -2.5,
                                            right: -2.5,
                                        }}
                                    />
                                )}
                            </TouchableOpacity>
                        )}
                    </View>
                )}

                <RequestDeleteModal
                    type={type}
                    modalState={[showModal, setShowModal]}
                    request={request}
                    goBack={goBack}
                />

                {reviewModal && (
                    <ReviewModalNew
                        request={request}
                        isMyRequest={type.includes('requested')}
                        closeModal={(success) => {
                            console.log(success);
                            if (success) navigation.goBack();
                            setReviewModal(false);
                        }}
                    />
                )}
                {acceptRequestModal && (
                    <AcceptRequestModalNew
                        modal={acceptRequestModal}
                        setModal={setAcceptRequestModal}
                    />
                )}
                {/* Request modal  */}
                {user?.additional_settings?.showAcceptRequestPopup && (
                    <CustomRM
                        isVisible={isVisible}
                        label={'Request'}
                        heading="Are you sure you want accept the request?"
                        data={[
                            `This is a no-obligation system.  You don't have to accept anything that doesn't work for you`,
                            `Clarify that logistics work before accepting`,
                            `Learn enough about the requester so that you are sure you want to spend a few hours with them`,
                            `We are not big fans of "unaccompanied" play.  Accept if you can host.  If setting up unaccompanied play, remember that you are responsible for guest conduct`,
                        ]}
                        isChecked={isAccepted}
                        handleChecked={() => setIsAccepted(!isAccepted)}
                        handleContinue={handleContinue}
                        handleCancel={handleModalClose}
                        handleBack={handleModalClose}
                    />
                )}
            </SafeAreaView>
            {/* } */}
            {isloading1 && (
                <View
                    style={{
                        position: 'absolute',
                        left: 0,
                        right: 0,
                        top: Spacing.SCALE_100,
                    }}>
                    <ActivityIndicator size="small" color="white" />
                </View>
            )}
        </>
    );
}
const styles = StyleSheet.create({

    labelGameId: {
        fontSize: 14,
        fontFamily: 'Ubuntu-Regular',
        color: '#808080',
        marginTop: 5,
        borderColor: '#808080',
        borderBottomWidth: 0.5,
        lineHeight: 22,
    },
    button: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#E05E52',
        borderRadius: 8,
        height: 35,
    },
    input: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 12,
        borderBottomColor: '#808080',
        borderBottomWidth: 0.5,
        paddingVertical: 10,
        paddingHorizontal: 5
    },
    buttonExpand: {
        height: 30, width: 30, backgroundColor: '#EBEBEB',
        borderRadius: 6, alignItems: 'center',
        justifyContent: 'center', marginLeft: 16,
        marginVertical: 8,
    },

})