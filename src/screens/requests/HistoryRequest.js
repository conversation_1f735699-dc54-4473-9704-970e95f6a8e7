import React, {useContext, useEffect, useState} from 'react';
import {
    <PERSON><PERSON>,
    FlatList,
    RefreshControl,
    View,
    TouchableOpacity,
    StyleSheet,
    Text,
    ActivityIndicator,
} from 'react-native';
import {colors} from '../../theme/theme';
import {Delete} from '../../assets/images/svg';
import RequestCardNew from '../../components/layout/requests/RequestCardnew';
import TGCustomModalView from '../../components/modals/TGCustomModalView';
import {AuthContext} from '../../context/AuthContext';
import {UPDATE_REQUEST} from '../../graphql/mutations/requests';
import useClient from '../../hooks/useClient';
import {historyRequestedURL, historyReceivedURL} from '../../service/EndPoint';
import OptionTab from './OptionTab';
import {getRequests} from './Utils';
import RequestDeleteModal from '../../components/modals/RequestDeleteModal';
import TGNoResult from '../../components/layout/TGNoResult';
import EmptyHostIcon from '../../assets/images/EmptyHostIcon.svg';

export default function HistoryRequest({unreadChannels}) {
    const {user} = useContext(AuthContext);
    const client = useClient();

    const [refreshing, setRefreshing] = useState(false);
    const [activeTab, setActiveTab] = useState('received');

    const [historyReceived, setHistoryReceived] = useState();
    const [historyRequested, setHistoryRequested] = useState();

    const [showDeleteModal, setshowDeleteModal] = useState(false);
    const [deleteRequest, setDeleteRequest] = useState();

    const [acceptRequestModal, setAcceptRequestModal] = useState();
    const [screenLoader, setScreenLoader] = useState(false);

    useEffect(() => {
        if (activeTab === 'received' && !historyReceived) {
            let data = {
                user_id: user?.id,
            };
            getRequests(historyReceivedURL, data).then((requests) => {
                setHistoryReceived(requests?.data);
            });
        } else if (activeTab === 'requested' && !historyRequested) {
            let data = {
                user_id: user?.id,
            };
            getRequests(historyRequestedURL, data).then((requests) => {
                setHistoryRequested(requests?.data);
            });
        }
    }, [activeTab]);

    const _onRefresh = () => {
        if (activeTab === 'received') {
            let data = {
                user_id: user?.id,
            };
            getRequests(historyReceivedURL, data).then((requests) => {
                setHistoryReceived(requests?.data);
            });
        } else if (activeTab === 'requested') {
            let data = {
                user_id: user?.id,
            };
            getRequests(historyRequestedURL, data).then((requests) => {
                setHistoryRequested(requests?.data);
            });
        }
    };

    const requests =
        activeTab === 'received' ? historyReceived : historyRequested;

    const renderListItem = ({item}) => {
        return (
            <RequestCardNew
                request={item}
                type={`history-${activeTab}`}
                acceptRequestModalState={[
                    acceptRequestModal,
                    setAcceptRequestModal,
                ]}
                onDeclineRequest={() => {
                    setDeleteRequest(item);
                    setshowDeleteModal(true);
                }}
                onRefresh={_onRefresh}
                hasUnreadMessages={unreadChannels?.filter(
                    (data) => data === item?.request_id,
                )}
                setScreenLoader={setScreenLoader}
            />
        );
    };

    if (!requests)
        return (
            <View
                style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                }}>
                <ActivityIndicator color={colors.darkteal} size="large" />
            </View>
        );

    return (
        <View style={{flex: 1}}>
            <OptionTab
                options={['received', 'requested']}
                activeTab={activeTab}
                setActiveTab={setActiveTab}
            />
            {requests && requests?.length <= 0 ? (
                <View style={{
                    justifyContent: 'center',
                    alignItems: 'center', flex: 1
                }}>
                    <EmptyHostIcon />
                    <TGNoResult
                        message={`There are no requests listed.\nPlease check back later.`}
                        _onRefresh={_onRefresh}
                        isRefresh
                    />
                </View>
            ) : (
                <FlatList
                    data={requests}
                    style={{marginTop: 8}}
                    refreshControl={
                        <RefreshControl
                            refreshing={refreshing}
                            onRefresh={_onRefresh}
                        />
                    }
                    renderItem={renderListItem}
                    keyExtractor={(item, index) => index.toString()}
                />
            )}

            <RequestDeleteModal
                type="history"
                modalState={[showDeleteModal, setshowDeleteModal]}
                request={deleteRequest}
                goBack={_onRefresh}
            />
        </View>
    );
}
const styles = StyleSheet.create({
    button: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#E05E52',
        borderRadius: 8,
        height: 35,
    },
    input: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 12,
        borderBottomColor: '#808080',
        borderBottomWidth: 0.5,
        paddingVertical: 10,
        paddingHorizontal: 5,
    },
});
