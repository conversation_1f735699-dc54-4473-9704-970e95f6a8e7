import { Image, Pressable, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { useContext, useState } from 'react';
import showToast from '../../../components/toast/CustomToast';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { HostDataInterface } from '../../../interface';
import { Size, Spacing } from '../../../utils/responsiveUI';
import { colors } from '../../../theme/theme';
import { ChatWhiteIcon } from '../../../assets/svg';
import { RootStackParamList } from '../../../interface/type';
import redirectToUserProfile from '../action/openUserProfile';
import { AuthContext } from '../../../context/AuthContext';
import { GlobalContext } from '../../../context/contextApi';
import { StreamChatContext } from '../../../context/StreamChatContext';

const HostPreview = ({
    hostDataItem,
    index,
    request_id,
    requestor_user_id,
    game_id,
    requestor_full_name,
    navigation,
    lastIndex,
    isAcceptingHost,
    dividerStyle={},
    name,
}: {
    hostDataItem: HostDataInterface;
    index: number;
    request_id: string;
    requestor_user_id: string;
    game_id: number;
    requestor_full_name: string;
    navigation: NativeStackNavigationProp<RootStackParamList>;
    lastIndex?: number;
    isAcceptingHost?: boolean;
    dividerStyle?: any;
    name?: string;
}) => {
    const { user } = useContext(AuthContext);
    const { state } = useContext(GlobalContext);
    const { unreadChannelsObject } = state;
    const { setChannel } = useContext(StreamChatContext);
    const [isDeletedUser, setIsDeletedUser] = useState(hostDataItem?.deleted_at);
    const goToChatScreen = () => {
        setChannel({});
        if (
            hostDataItem?.id &&
            (requestor_user_id || user?.id) &&
            hostDataItem?.id !== (requestor_user_id || user?.id)
        ) {
            navigation.navigate('ChatDetails', {
                request_id: request_id,
                game_id: game_id,
                streamChannelId: hostDataItem?.stream_channel_id,
                requestor_user_id: requestor_user_id || user?.id,
                requestor_full_name: requestor_full_name,
                host_user_id: hostDataItem?.id,
                has_messages: hostDataItem?.has_messages,
                name: name,
            });
        } else {
            showToast({});
        }
    };

    return (
        <>
            <Pressable
                onPress={() => {
                    isDeletedUser ? null : redirectToUserProfile({ userId: hostDataItem.id, navigation, user });
                }}
                style={[
                    styles.container,
                    index === 0 && { marginTop: 0 },
                    isAcceptingHost && styles.acceptingHostStyle,
                ]}>
                <View style={{ flexDirection: 'row', columnGap: Spacing.SCALE_6, alignItems: 'center' }}>
                    {hostDataItem?.profilePhoto ? (
                        isDeletedUser ? (
                            <View
                                style={[
                                    styles.profileIconWrapper,
                                    { backgroundColor: isDeletedUser ? colors.lightShadeGray : colors.tealRgb },
                                ]}>
                                <Text style={styles.requesterProfileInitials}>{'?'}</Text>
                            </View>
                        ) : (
                            <Image source={{ uri: hostDataItem?.profilePhoto }} style={styles.profileIconWrapper} />
                        )
                    ) : (
                        <View
                            style={[
                                styles.profileIconWrapper,
                                { backgroundColor: isDeletedUser ? colors.lightShadeGray : colors.tealRgb },
                            ]}>
                            <Text style={styles.requesterProfileInitials}>
                                {isDeletedUser
                                    ? '?'
                                    : state?.allFriendsId[hostDataItem?.id]
                                    ? hostDataItem?.name?.[0]
                                    : hostDataItem?.username?.[0] || ''}
                            </Text>
                        </View>
                    )}
                    <View>
                        {isAcceptingHost && !isDeletedUser ? (
                            <View style={styles.acceptingHostTextWrapper}>
                                <Text style={styles.acceptingHostText}>Host</Text>
                            </View>
                        ) : null}
                        <Text
                            style={[
                                styles.requesterProfileText,
                                { color: isDeletedUser ? colors.lightShadeGray : colors.lightBlack },
                            ]}>
                            {isDeletedUser
                                ? 'Deleted User'
                                : state?.allFriendsId[hostDataItem?.id]
                                ? hostDataItem?.name
                                : hostDataItem?.username}
                        </Text>
                    </View>
                </View>
                <TouchableOpacity style={styles.chatIconWrapper} onPress={goToChatScreen}>
                    <ChatWhiteIcon width={Size.SIZE_14} height={Size.SIZE_13} />
                    {Array.isArray(unreadChannelsObject) &&
                    unreadChannelsObject?.filter((data: any) => {
                        return data?.id === hostDataItem?.stream_channel_id;
                    })?.length ? (
                        <View style={styles.tealDotStyle} />
                    ) : null}
                </TouchableOpacity>
            </Pressable>
            {index !== lastIndex && <View style={[styles.divider, dividerStyle]} />}
        </>
    );
};

export default HostPreview;

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        height: Size.SIZE_40,
    },
    acceptingHostStyle: {
        padding: Spacing.SCALE_10,
        borderRadius: Size.SIZE_12,
        backgroundColor: colors.lightgray,
    },
    requesterProfileText: {
        fontSize: Size.SIZE_14,
        fontFamily: 'Ubuntu-Regular',
        fontWeight: '400',
    },
    profileIconWrapper: {
        width: Size.SIZE_24,
        height: Size.SIZE_24,
        borderRadius: Size.SIZE_50,
        justifyContent: 'center',
        alignItems: 'center',
    },
    requesterProfileInitials: {
        fontSize: Size.SIZE_12,
        color: colors.whiteRGB,
        fontFamily: 'Ubuntu-Regular',
        textTransform: 'uppercase',
    },
    chatIconWrapper: {
        width: Size.SIZE_30,
        height: Size.SIZE_30,
        backgroundColor: colors.tealRgb,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: Size.SIZE_8,
    },
    tealDotStyle: {
        width: 10,
        height: 10,
        borderRadius: 50,
        backgroundColor: colors.tealRgb,
        position: 'absolute',
        right: -2,
        borderWidth: 1,
        borderColor: colors.whiteRGB,
        top: -2,
    },
    divider: {
        height: 1,
        backgroundColor: colors.lightGrey,
    },
    acceptingHostTextWrapper: {
        paddingHorizontal: Spacing.SCALE_4,
        paddingVertical: Spacing.SCALE_2,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.opacityTeal,
        borderRadius: Size.SIZE_16,
        width: Size.SIZE_36,
    },
    acceptingHostText: {
        fontSize: Size.SIZE_9,
        lineHeight: Size.SIZE_10,
        color: colors.tealRgb,
        fontFamily: 'Ubuntu-Medium',
        textTransform: 'uppercase',
    },
});
