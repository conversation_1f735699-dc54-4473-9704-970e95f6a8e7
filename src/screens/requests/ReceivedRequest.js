import React, { useContext, useEffect, useLayoutEffect, useState } from 'react';
import { FlatList, RefreshControl, StyleSheet, View, ActivityIndicator } from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';

import RequestCardNew from '../../components/layout/requests/RequestCardnew';
import { AuthContext } from '../../context/AuthContext';
import { receivedAcceptedURL, receivedOpenURL, historyReceivedUrlV4 } from '../../service/EndPoint';
import { colors } from '../../theme/theme';
import { getRequests } from './Utils';
import AcceptRequestModalNew from '../../components/modals/AcceptRequestModalNew';
import ReviewModalNew from '../../components/modals/ReviewModalNew';
import RequestDeleteModal from '../../components/modals/RequestDeleteModal';
import TGNoResult from '../../components/layout/TGNoResult';
import DateRangeModal1 from '../../components/modals/DateRangeModal1';
import { GlobalContext } from '../../context/contextApi';
import NewOptionTab from './NewOptionTab';
import { PAGINATION_LIMIT, REQUEST_CHAT_GROUP } from '../my-TG-Stream-Chat/client';
import NewScreenHeader from '../../components/layout/NewScreenHeader';
import RequestHistoryFilter from './RequestHistoryFilter';
import EmptyHostIcon from '../../assets/images/EmptyHostIcon.svg';
import { StreamChatContext } from '../../context/StreamChatContext';

export default function ReceivedRequest({ route }) {
    const { user } = useContext(AuthContext);
    const { state, actions } = useContext(GlobalContext);
    const navigation = useNavigation();
    const isFocused = useIsFocused();
    // const client = useClient();
    const { client } = useContext(StreamChatContext);
    const { unreadChannels } = state;

    const [refreshing, setRefreshing] = useState(false);
    const [activeTab, setActiveTab] = useState('open');

    const [showDeclineModal, setshowDeclineModal] = useState(false);
    const [declineReason, setDeclineReason] = useState('');
    const [declineRequest, setDeclineRequest] = useState();
    const [declineReasonError, setDeclineReasonError] = useState('');

    const [acceptRequestModal, setAcceptRequestModal] = useState();
    const [reviewModal, setReviewModal] = useState();

    const [receivedOpen, setReceivedOpen] = useState();
    const [receivedAccepted, setReceivedAccepted] = useState();
    const [receivedHistory, setReceivedHistory] = useState();
    const [dateRangeModal, setDateRangeModal] = useState();
    const [screenLoader, setScreenLoader] = useState(false);
    const [currentPage, setCurrentPage] = useState(0);
    const [totalHistoryPage, setTotalHistoryPage] = useState(0);
    const [showFilterOption, setShowFilterOption] = useState(false);
    const [filter, setFilter] = useState('all');
    const [loading, setLoading] = useState(false);
    const [streamUnreadChannel, setStreamUnreadChannel] = useState([]);

    useLayoutEffect(() => {
        setStreamUnreadChannel([]);
    }, []);

    useEffect(() => {
        // getStreamRequestChannel();
    }, [navigation, state?.currentTab, isFocused]);

    useEffect(() => {
        const messageNewEventListener = client.on('message.new', async (event) => {
            // getStreamRequestChannel();
        });

        return () => {
            messageNewEventListener?.unsubscribe();
        };
    }, [client]);

    //Get stream unread messages
    const getStreamRequestChannel = async () => {
        let page = 0;
        while (1) {
            const channels = await client?.queryChannels(
                {
                    type: REQUEST_CHAT_GROUP,
                    members: { $in: [user?.id] },
                    has_unread: true,
                },
                {},
                { limit: 30, offset: 30 * page },
            );
            page = page + 1;
            if (!channels?.length) {
                break;
            } else {
                let requestIds = channels.map((data) => data?.data?.request_id);
                const updatedUnreadChannels = Array.from(new Set([...requestIds]));
                setStreamUnreadChannel(updatedUnreadChannels);
            }
        }
    };

    useEffect(() => {
        // actions.setUnreadChannels([...streamUnreadChannel]);
    }, [streamUnreadChannel]);

    useEffect(() => {
        setActiveTab(route.params?.activeTab);
    }, [route.params]);

    useEffect(() => {
        if (activeTab === 'open' && !receivedOpen) {
            let data = {
                userId: user?.id,
            };
            getRequests(receivedOpenURL, data).then((requests) => {
                setReceivedOpen(requests?.data);
            });
        } else if (activeTab === 'accepted' && !receivedAccepted) {
            let data = {
                userId: user?.id,
            };
            getRequests(receivedAcceptedURL, data).then((requests) => {
                setReceivedAccepted(requests?.data);
            });
        } else if (activeTab === 'history' && !receivedHistory) {
            let data = {
                userId: user?.id,
                limit: PAGINATION_LIMIT,
                page: 1,
                filter,
            };
            getRequests(historyReceivedUrlV4, data).then((requests) => {
                setCurrentPage(requests?.currentPage);
                setTotalHistoryPage(requests?.totalPages);
                setReceivedHistory(requests?.data);
            });
        }
    }, [activeTab]);

    useEffect(() => {
        setDeclineReasonError('');
    }, [declineReason]);

    const _onRefresh = () => {
        if (activeTab === 'open') {
            let data = {
                userId: user?.id,
            };
            getRequests(receivedOpenURL, data).then((requests) => {
                setReceivedOpen(requests?.data);
            });
        } else if (activeTab === 'accepted') {
            let data = {
                userId: user?.id,
            };
            getRequests(receivedAcceptedURL, data).then((requests) => {
                setReceivedAccepted(requests?.data);
            });
        } else if (activeTab === 'history') {
            let data = {
                userId: user?.id,
                limit: PAGINATION_LIMIT,
                page: 1,
                filter,
            };
            getRequests(historyReceivedUrlV4, data).then((requests) => {
                setCurrentPage(requests?.currentPage);
                setTotalHistoryPage(requests?.totalPages);
                setReceivedHistory(requests?.data);
            });
        }
    };

    const _openRequestRefresh = () => {
        let data = {
            userId: user?.id,
        };
        getRequests(receivedOpenURL, data).then((requests) => {
            setReceivedOpen(requests?.data);
        });
    };

    const handleOnReachEnd = () => {
        if (activeTab === 'history') {
            let data = {
                userId: user?.id,
                limit: PAGINATION_LIMIT,
                page: currentPage + 1,
                filter,
            };
            getRequests(historyReceivedUrlV4, data).then((requests) => {
                setScreenLoader(false);
                setCurrentPage(requests?.currentPage);
                setTotalHistoryPage(requests?.totalPages);
                setReceivedHistory([...receivedHistory, ...requests?.data]);
            });
        }
    };

    const requests =
        activeTab === 'open' ? receivedOpen : activeTab === 'accepted' ? receivedAccepted : receivedHistory;
    if (!requests)
        return (
            <View
                style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                }}>
                <ActivityIndicator color={colors.darkteal} size="large" />
            </View>
        );

    return (
        <>
            <View style={{ flex: 1 }}>
                <NewScreenHeader
                    title="Received"
                    disableSearch={true}
                    showBackBtn={true}
                    onPressGoBack={() => {
                        if (route?.params?.backHandling) {
                            navigation.goBack();
                        } else {
                            actions.setCurrentTab(1); // Set current tab in context
                            navigation.goBack();
                        }
                    }}
                    showNotification={false}
                    showFilter={route.params?.activeTab === 'history' ? true : false}
                    openFilterModal={() => setShowFilterOption((prev) => !prev)}
                />
                {/* back button header component */}
                <NewOptionTab tabOption={route.params?.activeTab} tabName="Received" />
                {requests && requests?.length <= 0 ? (
                    <View
                        style={{
                            justifyContent: 'center',
                            alignItems: 'center',
                            flex: 1,
                        }}>
                        <EmptyHostIcon />
                        <TGNoResult
                            message={`There are no requests listed.\nPlease check back later.`}
                            _onRefresh={_onRefresh}
                            isRefresh
                        />
                    </View>
                ) : (
                    <FlatList
                        data={
                            activeTab === 'open'
                                ? receivedOpen
                                : activeTab === 'accepted'
                                ? receivedAccepted
                                : receivedHistory
                        }
                        style={{ marginTop: 8 }}
                        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={_onRefresh} />}
                        onEndReachedThreshold={0.5}
                        onEndReached={() => {
                            if (
                                receivedHistory?.length === currentPage * PAGINATION_LIMIT &&
                                currentPage < totalHistoryPage
                            ) {
                                if (activeTab === 'history') {
                                    setScreenLoader(true);
                                    handleOnReachEnd();
                                }
                            }
                        }}
                        renderItem={({ item, index }) => {
                            return (
                                <RequestCardNew
                                    key={index}
                                    request={item}
                                    type={`received-${activeTab}`}
                                    onRefresh={_onRefresh}
                                    markGameCompleted={() => {
                                        setDeclineRequest(item);
                                        setReviewModal(true);
                                    }}
                                    onDeclineRequest={() => {
                                        setDeclineRequest(item);
                                        setshowDeclineModal(true);
                                    }}
                                    acceptRequestModalState={[acceptRequestModal, setAcceptRequestModal]}
                                    hasUnreadMessages={unreadChannels?.filter((data) => data === item?.request_id)}
                                    rangeModal={[dateRangeModal, setDateRangeModal]}
                                    _openRequestRefresh={_openRequestRefresh}
                                    setScreenLoader={setScreenLoader}
                                />
                            );
                        }}
                        keyExtractor={(item) => item?.request_id}
                    />
                )}

                <RequestDeleteModal
                    type={`received-${activeTab}`}
                    modalState={[showDeclineModal, setshowDeclineModal]}
                    request={declineRequest}
                    goBack={_onRefresh}
                />

                {acceptRequestModal && (
                    <AcceptRequestModalNew modal={acceptRequestModal} setModal={setAcceptRequestModal} />
                )}

                {reviewModal && (
                    <ReviewModalNew
                        game={declineRequest}
                        request={declineRequest}
                        isMyRequest={false}
                        closeModal={() => {
                            setReviewModal();
                            _onRefresh();
                        }}
                    />
                )}
                {dateRangeModal && (
                    <View
                        style={{
                            position: 'absolute',
                            // flex: 1,
                            left: 0,
                            right: 0,
                            top: 0,
                            bottom: 0,
                            paddingHorizontal: 30,
                            justifyContent: 'center',
                            alignItems: 'center',
                            zIndex: 100,
                        }}>
                        <DateRangeModal1
                            modal={{
                                ...dateRangeModal,
                            }}
                            setModal={setDateRangeModal}
                            getRequests={getRequests}
                            receivedAcceptedURL={receivedAcceptedURL}
                            user_id={user.id}
                            setReceivedAccepted={setReceivedAccepted}
                        />
                    </View>
                )}
            </View>
            {screenLoader == true && (
                <View
                    style={{
                        position: 'absolute',
                        top: 0,
                        bottom: 0,
                        right: 0,
                        left: 0,
                        justifyContent: 'center',
                        alignItems: 'center',
                        flex: 1,
                    }}>
                    <ActivityIndicator color={colors.darkteal} size="large" />
                </View>
            )}
            {showFilterOption && (
                <RequestHistoryFilter
                    popupState={[showFilterOption, setShowFilterOption]}
                    filterState={[filter, setFilter]}
                    getHistory={_onRefresh}
                    loadingState={[loading, setLoading]}
                    tab={'Received'}
                />
            )}
        </>
    );
}

const styles = StyleSheet.create({
    button: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#E05E52',
        borderRadius: 8,
        height: 35,
    },
    input: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 12,
        borderBottomColor: '#808080',
        borderBottomWidth: 0.5,
        paddingVertical: 10,
        paddingHorizontal: 5,
    },
});
