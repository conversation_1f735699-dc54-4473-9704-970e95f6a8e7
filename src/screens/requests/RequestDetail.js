import React, { useContext, useEffect, useLayoutEffect } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    SafeAreaView,
    ScrollView,
    FlatList,
    StyleSheet,
    Alert,
    RefreshControl,
} from 'react-native';
import { useState } from 'react';
import { Back, Check, Cross, Delete, DocEdit, Edit, Golf, Group, Message, MessageDark } from '../../assets/images/svg';
import TGText from '../../components/fields/TGText';
import { fetcher } from '../../service/fetcher';
import {
    GET_RECIEVED_REQUEST_INFO,
    GET_REQUESTED_HOSTS,
    GET_REQUESTED_REQUEST_INFO,
    GET_REQUEST_INFO,
    gameUserInformation,
    receivedOpenURL,
    requestDetailsURL,
} from '../../service/EndPoint';
import ReviewModalNew from '../../components/modals/ReviewModalNew';
import AcceptRequestModalNew from '../../components/modals/AcceptRequestModalNew';
import { AuthContext } from '../../context/AuthContext';
import useClient from '../../hooks/useClient';
import CustomRM from '../../components/layout/requests/customRM';
import { UPDATE_USER } from '../../graphql/mutations/user';
import RequestDeleteModal from '../../components/modals/RequestDeleteModal';
import { getRequests } from './Utils';
import { Spacing, Typography } from '../../utils/responsiveUI';
import useQuery from '../../hooks/useQuery';
import { GAME_INFO } from '../../graphql/queries/requestDetailsQuerry';
import { handleTimeFormat } from '../../components/timeFormatComponent/handleTimeFormat';
import { colors } from '../../theme/theme';
import ReceivedRequestDetails from './ReceivedRequestDetails';
import RequestedRequestDetails from './RequestedRequestDetails';
import showToast from '../../components/toast/CustomToast';
import dateFormatter from '../../utils/helpers/dateFormatter';
import { StreamChatContext } from '../../context/StreamChatContext';
import { REQUEST_CHAT_GROUP } from '../my-TG-Stream-Chat/client';
import { useIsFocused } from '@react-navigation/native';

export default function RequestDetail({ navigation, route }) {
    const { user } = useContext(AuthContext);
    const client = useClient();
    const { client: streamClient } = useContext(StreamChatContext);

    const [request, setRequest] = useState(route?.params?.request);
    const [refreshing, setRefreshing] = useState(false);

    const [unreadChannels, setUnreadChannels] = useState([]);
    const [unreadMessages, setUnreadMessages] = useState([]);

    const { requestId, actionType, _onRefresh = () => {}, params, _openRequestRefresh } = route?.params;
    const [requester, setRequester] = useState();
    const [type, setType] = useState(actionType);

    const [gameHost, setGameHost] = useState();

    const [reviewModal, setReviewModal] = useState();
    const [acceptRequestModal, setAcceptRequestModal] = useState();

    const [isLoading, setLoading] = useState(false);
    const [loadingRequest, setLoadingRequest] = useState(false);

    const [showModal, setShowModal] = useState(false);
    const [reason, setReason] = useState('');
    const [reasonError, setReasonError] = useState('');

    const [isVisible, setVisible] = useState(false);
    const [isAccepted, setIsAccepted] = useState(false);
    const [has_messages, setHas_messages] = useState();
    const [receivedOpen, setReceivedOpen] = useState();
    const [updatedIsMessage, seUpdatedIsMessage] = useState();
    const [isDetailVisible, setDetailVisible] = useState(false);
    const [verify, setVerify] = useState({});
    const [requestInfo, setRequestInfo] = useState({});
    const [hosts, setHosts] = useState([]);
    const [streamUnreadChannel, setStreamUnreadChannel] = useState([]);
    const isFocused = useIsFocused();

    const { data } = useQuery(GAME_INFO, {
        request_id: request?.request_id,
    });

    // Code to get unread channel of stream
    useLayoutEffect(() => {
        setStreamUnreadChannel([]);
    }, []);

    useEffect(() => {
        // getStreamRequestChannel();
    }, [navigation, isFocused]);

    useEffect(() => {
        const messageNewEventListener = streamClient.on('message.new', async (event) => {
            // getStreamRequestChannel();
        });

        return () => {
            messageNewEventListener?.unsubscribe();
        };
    }, [streamClient]);

    //Get stream unread messages
    const getStreamRequestChannel = async () => {
        let page = 0;
        while (1) {
            const channels = await streamClient?.queryChannels(
                {
                    type: REQUEST_CHAT_GROUP,
                    members: { $in: [user?.id] },
                    has_unread: true,
                },
                {},
                { limit: 30, offset: 30 * page },
            );
            page = page + 1;
            if (!channels?.length) {
                break;
            } else {
                let requestIds = channels.map((data) => data?.data?.request_id);
                const updatedUnreadChannels = Array.from(new Set([...requestIds]));
                setStreamUnreadChannel(channels);
            }
        }
    };

    useEffect(() => {
        _openRequestRefresh1();
    }, []);
    useEffect(() => {
        receivedOpen?.filter((data) => {
            if (data.request_id === route?.params?.request?.request_id) seUpdatedIsMessage(data);
        });
    }, [receivedOpen]);

    const _openRequestRefresh1 = () => {
        let data = {
            userId: user?.id,
        };
        getRequests(receivedOpenURL, data).then((requests) => {
            setReceivedOpen(requests?.data);
        });
    };

    useEffect(() => {
        if (type.includes('received')) {
            getGameUserInformation({
                request_id: request?.request_id,
                host_id: request?.requestor_user_id,
            }).then((res) => {
                setLoading(false);
                setRequester(res?.data);
            });
        } else if (request?.game_host_user_id) {
            getGameUserInformation({
                request_id: request?.request_id,
                host_id: request?.game_host_user_id,
            }).then((res) => {
                setLoading(false);
                setGameHost(res?.data);
            });
        }
    }, []);

    useEffect(() => {
        if (unreadChannels.length > 0) {
            setUnreadMessages(unreadChannels.filter(({ data }) => data === request?.request_id));
        }
    }, [unreadChannels]);

    useEffect(() => {
        getRequestDetails();

        if (!type.includes('received')) {
            getRequestedHostData();
        }
    }, []);

    const getRequestDetails = () => {
        try {
            setLoadingRequest(true);
            fetcher({
                endpoint: type.includes('received') ? GET_RECIEVED_REQUEST_INFO : GET_REQUESTED_REQUEST_INFO,
                method: 'POST',
                body: {
                    userId: user?.id,
                    requestId: route?.params?.request?.request_id,
                },
            }).then((res) => {
                setLoadingRequest(false);
                if (res.status) {
                    setRequestInfo(res?.data);
                } else {
                    showToast({});
                }
            });
        } catch (error) {
            setLoadingRequest(false);
            console.log('error: getRequestDetails -->', error);
        }
    };

    const getRequestedHostData = () => {
        try {
            setLoadingRequest(true);
            fetcher({
                endpoint: GET_REQUESTED_HOSTS,
                method: 'POST',
                body: {
                    userId: user?.id,
                    requestId: route?.params?.request?.request_id,
                },
            }).then((res) => {
                setLoadingRequest(false);
                if (res.status) {
                    setHosts(res?.data);
                } else {
                    showToast({});
                }
            });
        } catch (error) {
            setLoadingRequest(false);
            console.log('error: getRequestedHostData -->', error);
        }
    };

    const handleModalClose = () => {
        setVisible(false);
    };

    const handleContinue = () => {
        setVisible(false);
        if (isAccepted) {
            client.request(UPDATE_USER, {
                user_id: user?.id,
                user: {
                    additional_settings: {
                        showAcceptRequestPopup: false,
                        showCreateRequestPopup: user?.additional_settings?.showCreateRequestPopup,
                    },
                },
            });
        }
        setTimeout(() => {
            acceptRequest();
        }, 100);
    };

    const showRequestPopup = () => {
        if (user?.additional_settings?.showAcceptRequestPopup) {
            setVisible(true);
        } else acceptRequest();
    };

    const getGameUserInformation = async (body) => {
        return await fetcher({
            endpoint: gameUserInformation,
            method: 'POST',
            body,
        });
    };

    function goBack() {
        navigation.goBack();
        if (_onRefresh) _onRefresh();
    }

    React.useEffect(() => {
        const unsubscribe = navigation.addListener('focus', focusScreen);
        // Return the function to unsubscribe from the event so it gets removed on unmount
        return unsubscribe;
    }, [navigation]);
    const focusScreen = () => {
        _onRefresh();
        _openRequestRefresh1();
    };

    async function acceptRequest() {
        Alert.alert(
            'Accept Request',
            `Have you confirmed the logistics for #${request.game_id} with ${request.requestor_full_name}?`,
            [
                {
                    text: 'Cancel',
                    onPress: () => {
                        Alert.alert(
                            'You must finalize the logistics before accepting the request',
                            '',
                            [
                                {
                                    text: 'Cancel',
                                    onPress: () => console.log('Cancel Pressed'),
                                    style: 'cancel',
                                },
                                {
                                    text: ` Chat with ${request.requestor_full_name} `,
                                    onPress: async () => goToChatScreen(),
                                },
                            ],
                            { cancelable: false },
                        );
                    },
                    style: 'cancel',
                },
                {
                    text: 'Yes',
                    onPress: async () => {
                        setAcceptRequestModal({ request, onRefresh: () => setType('received-accepted') });
                    },
                },
            ],
            { cancelable: false },
        );
    }

    async function showChatWarning(request) {
        Alert.alert(
            'Accepting Requests',
            `${
                !request?.has_messages && !request?.requester_has_message
                    ? 'Both host and requester must exchange at least one message to confirm logistics before accepting'
                    : 'You need to initiate the chat session with the Requester first to Confirm Logistics before accepting it'
            }`,
            [
                {
                    text: 'Cancel',
                    onPress: () => console.log('Cancel Pressed'),
                    style: 'cancel',
                },
                {
                    text: 'Start Chat',
                    onPress: () => goToChatScreen(),
                },
            ],
        );
    }

    function goToChatScreen(host) {
        const {
            sendbird_channel_id,
            request_id,
            game_id,
            requestor_user_id,
            requestor_full_name,
            host_user_id,
            stream_channel_id,
        } = request;

        const params = {
            type,
            request_id,
            game_id,
            channel_url: host?.sendbird_channel_id ? host?.sendbird_channel_id : sendbird_channel_id,
            requestor_user_id,
            requestor_full_name,
            host_user_id: host?.id ? host?.id : host_user_id,
            streamChannelId: host?.stream_channel_id,
        };
        navigation.navigate('ChatDetails', params);
    }

    const sortedArray = (hosts, host_user_id) => {
        return hosts.sort(function (a, b) {
            if (host_user_id === a.host_user_id) return -1;
            else return 1;
        });
    };
    const requestDate =
        (request?.status === 'completed' || type.includes('accepted')) && request?.game_date
            ? handleTimeFormat(request?.game_date)
            : dateFormatter(request?.start_date, request?.end_date);

    const getData = (data) => {
        let englishFluency = '';
        data?.map((data) => {
            data = data.substring(0, 1).toUpperCase() + data.substring(1, data.length);
            englishFluency = englishFluency + data + ',';
        });
        englishFluency = englishFluency.substring(0, englishFluency.length - 1);
        return englishFluency;
    };

    const checkKeyAvailable = (data) => {
        let res =
            data.hasOwnProperty('accompanied_only') &&
            data.hasOwnProperty('all_ages') &&
            data.hasOwnProperty('englishFluency') &&
            data.hasOwnProperty('playAsCouple');
        return res;
    };

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    height: 44,
                }}>
                <Text
                    style={{
                        fontSize: Typography.FONT_SIZE_18,
                        flex: 1,
                        textAlign: 'left',
                        color: colors.lightBlack,
                        paddingLeft: Spacing.SCALE_40,
                        fontWeight: '500',
                        fontFamily: 'Ubuntu-Medium',
                    }}>
                    Request #{request?.game_id}
                </Text>
                <TouchableOpacity onPress={() => navigation.goBack()} style={{ position: 'absolute', padding: 12 }}>
                    <Back />
                </TouchableOpacity>
            </View>
            <ScrollView
                style={{ flex: 1, paddingVertical: 12 }}
                refreshControl={<RefreshControl refreshing={refreshing} onRefresh={_onRefresh} />}>
                {type.includes('received') && (
                    <ReceivedRequestDetails
                        requestInfo={requestInfo}
                        loadingRequest={loadingRequest}
                        request={request}
                    />
                )}
                {type.includes('requested') && (
                    <RequestedRequestDetails
                        requestInfo={requestInfo}
                        loadingRequest={loadingRequest}
                        request={request}
                        unreadMessages={unreadMessages}
                        goToChatScreen={goToChatScreen}
                        type={type}
                        hosts={hosts}
                        streamUnreadChannel={streamUnreadChannel}
                    />
                )}
            </ScrollView>

            {!requester?.userInfo?.deleted_at && (
                <View style={{ flexDirection: 'row', padding: 12 }}>
                    <TouchableOpacity
                        style={[
                            styles.button,
                            {
                                marginRight: 5,
                                opacity: !request?.host_completed && !request?.requestor_completed ? 1 : 0.3,
                            },
                        ]}
                        onPress={() => {
                            if (!request?.host_completed && !request?.requestor_completed) setShowModal(true);
                            else {
                                const message = request?.host_completed
                                    ? "This request can't be deleted as the host has already marked the game as complete"
                                    : "This Request can't be Declined as the Requestor has already marked the game as Completed.";
                                alert(message);
                            }
                        }}>
                        {type.includes('received') &&
                        !type.includes('history-received') &&
                        !type.includes('received-history') ? (
                            <Cross />
                        ) : (
                            <Delete />
                        )}
                    </TouchableOpacity>

                    {!type.includes('history') && (
                        <TouchableOpacity
                            style={[
                                styles.button,
                                {
                                    flex: type.includes('accepted') ? 5 : 1,
                                    flexDirection: 'row',
                                    backgroundColor: type.includes('requested-open') ? '#EBEBEB' : '#2E9360',
                                    marginHorizontal: 5,
                                },
                            ]}
                            onPress={() => {
                                if (type.includes('requested-open')) {
                                    navigation.navigate('Create Request', {
                                        request,
                                    });
                                } else if (type.includes('received-open')) {
                                    if (updatedIsMessage?.has_messages && request?.requester_has_message) {
                                        showRequestPopup();
                                    } else {
                                        showChatWarning(request);
                                    }
                                } else if (type.includes('accepted')) {
                                    setReviewModal(true);
                                }
                            }}>
                            {type.includes('requested-open') ? <Edit /> : <Check />}
                            {type.includes('accepted') && (
                                <TGText
                                    style={{
                                        color: '#FFFFFF',
                                        fontSize: 14,
                                        fontFamily: 'Ubuntu-Regular',
                                    }}>
                                    {' '}
                                    Mark Completed
                                </TGText>
                            )}
                        </TouchableOpacity>
                    )}

                    {!type.includes('history') && !type.includes('requested') && (
                        <TouchableOpacity
                            style={[
                                styles.button,
                                {
                                    marginLeft: 5,
                                    backgroundColor:
                                        updatedIsMessage?.has_messages || request?.has_messages || request?.hosts
                                            ? '#098089'
                                            : '#EBEBEB',
                                },
                            ]}
                            onPress={() => goToChatScreen()}>
                            {/* {request?.hosts || request?.has_messages ? <Message /> : <MessageDark />} */}
                            {request?.hosts ||
                            updatedIsMessage?.has_messages ||
                            request?.has_messages ||
                            has_messages ? (
                                <Message />
                            ) : (
                                <MessageDark />
                            )}
                            {unreadMessages.length > 0 && (
                                <View
                                    style={{
                                        position: 'absolute',
                                        zIndex: 20,
                                        backgroundColor: 'red',
                                        height: 10,
                                        width: 10,
                                        borderRadius: 5,
                                        top: -2.5,
                                        right: -2.5,
                                    }}
                                />
                            )}
                        </TouchableOpacity>
                    )}
                </View>
            )}

            <RequestDeleteModal type={type} modalState={[showModal, setShowModal]} request={request} goBack={goBack} />

            {reviewModal && (
                <ReviewModalNew
                    request={request}
                    isMyRequest={type.includes('requested')}
                    closeModal={(success) => {
                        console.log(success);
                        if (success) navigation.goBack();
                        setReviewModal(false);
                    }}
                />
            )}
            {acceptRequestModal && (
                <AcceptRequestModalNew modal={acceptRequestModal} setModal={setAcceptRequestModal} />
            )}
            {/* Request modal  */}
            {user?.additional_settings?.showAcceptRequestPopup && (
                <CustomRM
                    isVisible={isVisible}
                    label={'Request'}
                    heading="Are you sure you want accept the request?"
                    data={[
                        `This is a no-obligation system.  You don't have to accept anything that doesn't work for you`,
                        `Clarify that logistics work before accepting`,
                        `Learn enough about the requester so that you are sure you want to spend a few hours with them`,
                        `We are not big fans of "unaccompanied" play.  Accept if you can host.  If setting up unaccompanied play, remember that you are responsible for guest conduct`,
                    ]}
                    isChecked={isAccepted}
                    handleChecked={() => setIsAccepted(!isAccepted)}
                    handleContinue={handleContinue}
                    handleCancel={handleModalClose}
                    handleBack={handleModalClose}
                />
            )}
        </SafeAreaView>
    );
}
const styles = StyleSheet.create({
    labelGameId: {
        fontSize: 14,
        fontFamily: 'Ubuntu-Regular',
        color: '#808080',
        marginTop: 5,
        borderColor: '#808080',
        borderBottomWidth: 0.5,
        lineHeight: 22,
    },
    button: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#E05E52',
        borderRadius: 8,
        height: 35,
    },
    input: {
        fontFamily: 'Ubuntu-Regular',
        fontSize: 12,
        borderBottomColor: '#808080',
        borderBottomWidth: 0.5,
        paddingVertical: 10,
        paddingHorizontal: 5,
    },
    buttonExpand: {
        height: 30,
        width: 30,
        backgroundColor: '#EBEBEB',
        borderRadius: 6,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: 16,
        marginVertical: 8,
    },
});
