import { UPDATE_REQUEST_CHAT, UPDATE_REQUESTER_CHAT } from '../../../../graphql/mutations/chat';
import { User } from '../../../../interface';

interface UpdateMessageKeyParams {
    graphQlClient: any;
    hasMessages: boolean;
    createdById: string;
    user: User;
    requestId: string;
    rollbar: any;
}

export const updateMessageKey = async ({
    graphQlClient,
    hasMessages,
    createdById,
    user,
    requestId,
    rollbar,
}: UpdateMessageKeyParams) => {
    try {
        if (!createdById) {
            return;
        }
        if (!hasMessages && createdById === user?.id) {
            try {
                await graphQlClient.request(UPDATE_REQUEST_CHAT, {
                    club_member_id: user.id,
                    request_id: requestId,
                });
            } catch (error) {
                rollbar.error('updateMessageKey >> Error updating UPDATE_REQUEST_CHAT chat:', requestId, error);
                setTimeout(async () => {
                    await graphQlClient.request(UPDATE_REQUEST_CHAT, {
                        club_member_id: user.id,
                        request_id: requestId,
                    });
                }, 30000);
            }
        } else {
            if (!(createdById === user?.id)) {
                try {
                    await graphQlClient.request(UPDATE_REQUESTER_CHAT, {
                        club_member_id: createdById,
                        request_id: requestId,
                    });
                } catch (error) {
                    rollbar.error('updateMessageKey >> Error updating UPDATE_REQUESTER_CHAT chat:', requestId, error);
                    setTimeout(async () => {
                        await graphQlClient.request(UPDATE_REQUESTER_CHAT, {
                            club_member_id: createdById,
                            request_id: requestId,
                        });
                    }, 30000);
                }
            }
        }
    } catch (error) {
        rollbar.error('updateMessageKey >> Error updating message key:', error, createdById, user?.id, requestId);
    }
};
