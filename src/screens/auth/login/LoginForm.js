import React, { useState, useContext, useEffect } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    Alert
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import auth from '@react-native-firebase/auth';

import Input from '../../../components/fields/InputNew';
import { AuthContext } from '../../../context/AuthContext';
import { getEmailByUsernameUrl } from '../../../service/EndPoint';
import { isEmpty } from '../actions';
import { Spacing } from '../../../utils/responsiveUI';
import Button from '../../../components/buttons/CustomButton';
import { colors } from '../../../theme/theme';
import styles from '../style';

export default function LoginForm() {
    const { user } = useContext(AuthContext);

    const [errors, setErrors] = useState({});
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [loading, setLoading] = useState(false);


    const navigation = useNavigation();

    useEffect(() => {
        if (user && !user?.deleted_at) {
            if (user?.registration_complete) {
                callScreen('Registration Complete');
            }
            else if (user?.verified) {
                if (user?.signup_current_step === 2) {
                    callScreen('Personal Profile Form');
                } else if (user?.signup_current_step === 3) {
                    callScreen('Golfer Profile Form');
                } else if (user?.signup_current_step === 4) {
                    callScreen('Club Information Form');
                } else if (user?.signup_current_step === 5) {
                    callScreen('Confirm Profile');
                } else if (user?.signup_current_step === 6) {
                    callScreen('Registration Complete');
                }
                else {
                    callScreen('SignUpBlocker');
                }
            } else {
                callScreen('SignUpBlocker');
            }

        }
    }, [user]);

    const callScreen = (screenName) => {
        navigation.reset({
            index: 0,
            routes: [{ name: screenName }],
        });
    }

    const resetForm = () => {
        setLoading(false)
        setErrors({});
        setPassword('');
        setEmail('');
    }

    const login = async () => {
        const checkEmpty = isEmpty({ email, password })
        if (Object.keys(checkEmpty).length === 0) {
            setLoading(true)
            console.log("Url4", getEmailByUsernameUrl);
            const response = await fetch(`${getEmailByUsernameUrl}`, {
                method: 'POST',
                credentials: 'same-origin',
                body: JSON.stringify({
                    username: email.trim().toLowerCase(),
                }),
            })
                .then((data) => data.json())
                .catch(error => {
                    setLoading(false)
                    console.log("Error123", error)
                    console.log('error', error.message.includes('Network request failed'));
                    if (error.message.includes('Network request failed'))
                        Alert.alert('No Internet!', 'Please check your internet connection and try again')
                });
            console.log("response1234",response)
            if (!response?.deactivated && !response.email) {
                setErrors({ password: 'Account with entered Credentials doesn’t exist' });
                setLoading(false)

            } else if (!response?.email || email === '<EMAIL>') {
                setErrors({ password: 'Your Email/Username and Password combination is incorrect' });
                setLoading(false)

            } else if (response?.deactivated) {
                setErrors({ password: 'Your account has been deactivated' });

                setLoading(false)
            } else {
                console.log("response123212321232132")
                auth().signInWithEmailAndPassword(
                    response.email
                        ? response.email.trim().toLowerCase()
                        : '<EMAIL>',
                    password,
                ).then(async (response) => { console.log("res=>", response) })
                    .catch((e) => {
                        console.log('error1212', e.message);
                        setErrors({
                            password: e.message.substr(e.message.indexOf(']') + 1)
                        });
                        setLoading(false)
                    });
            }


        }
        else setErrors(checkEmpty)
    };

    return (
        <View style={styles.inputContainer}>
            <View
                style={{
                    justifyContent: 'space-between',
                }}>
                <Input
                    name="email"
                    placeholder="Email/Username"
                    valueState={[email, setEmail]}
                    errors={errors}
                    setErrors={setErrors}
                    style={{color:colors.black}}
                />
                <Input
                    name="password"
                    placeholder="Password"
                    valueState={[password, setPassword]}
                    errors={errors}
                    setErrors={setErrors}
                    style={{color:colors.black}}
                />
            </View>

            <View style={styles.bottomContainer}>
                <Button
                    onPress={() => login()}
                    label="LOG IN"
                    btnStyle={{ width: 200 }}
                    loading={loading}
                />
                <TouchableOpacity
                    style={{ marginVertical: Spacing.SCALE_15 }}
                    onPress={() => {
                        navigation.navigate('Reset Password');
                    }}>
                    <Text
                        style={{
                            color: 'rgba(0,0,0,0.5)',
                            fontFamily: 'Ubuntu-Medium',
                        }}>
                        FORGOT PASSWORD
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={{ marginVertical: Spacing.SCALE_15 }}
                    onPress={() => {
                         //! temporary comment this code
                        // navigation.replace('SignUpBlocker', { text: 'Please go to www.thousandgreens.com on your browser to Create an Account.' });
                        // alert('clicked')
                        navigation.replace('Create Account Form');
                    }}>
                    <Text
                        style={{
                            color: colors.textProfile,
                            fontFamily: 'Ubuntu-Medium',
                        }}>
                        CREATE ACCOUNT
                    </Text>
                </TouchableOpacity>
            </View>
        </View>
    );
}
