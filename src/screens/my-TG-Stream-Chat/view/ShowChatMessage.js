import React, { useContext, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { TouchableOpacity, Text, View, Image, StyleSheet, Pressable, FlatList } from 'react-native';
import moment from 'moment';

import ShowChatImages from './showMessageImages';
import { handleMessageScreenParticipantsName } from './handleMessageScreenParticipantsName';
import { AuthContext } from '../../../context/AuthContext';
import { GlobalContext } from '../../../context/contextApi';
import { StreamChatContext } from '../../../context/StreamChatContext';
import { colors } from '../../../theme/theme';
import { DELETED_USER_ON_TAP_TEXT, REQUEST_CHAT_GROUP } from '../client';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import {
    Like<PERSON><PERSON>,
    Like,
    <PERSON>l<PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    Sad,
    Wow<PERSON><PERSON>,
    Wow,
} from '../../../assets/svg/chatReaction';
import FastImage from 'react-native-fast-image';

const ShowChatMessage = ({ message, alignment, onLongPress, onPressClick, reactions }) => {
    const { user } = useContext(AuthContext);
    const { state, actions } = useContext(GlobalContext);
    const { channel, setChannel, previousChannel, setPreviousChannel } = useContext(StreamChatContext);
    const navigation = useNavigation();
    const styles = makeStyles(alignment);
    const messageTime = moment.utc(message?.created_at).local().format('h:mm A');

    const onClickUserDp = () => {
        setPreviousChannel(channel);
        const selectedUser = message?.user;
        navigation.navigate('UserProfileScreen', {
            selectedUser: { id: selectedUser?.id },
            isComeFromChat: true,
        });
    };

    const deletedMessages = () => {
        return (
            <>
                <Text style={styles.message}>This message was deleted.</Text>
                <Text style={styles.time}>{messageTime}</Text>
            </>
        );
    };

    const handleMessageThreadUserProfile = () => {
        if (alignment === 'left') {
            if (message?.user?.deleted_at) {
                return (
                    <Pressable style={styles.deletedUserIconWrapper} onPress={() => alert(DELETED_USER_ON_TAP_TEXT)}>
                        <Text style={styles.deletedImageTextStyle}>?</Text>
                    </Pressable>
                );
            } else {
                return message?.user?.image ? (
                    <TouchableOpacity style={styles.imageContainer} onPress={() => onClickUserDp()}>
                        <Image
                            source={{
                                uri: message?.user?.image,
                            }}
                            style={styles.image}
                        />
                    </TouchableOpacity>
                ) : (
                    <Pressable style={styles.imageWrapper1} onPress={() => onClickUserDp()}>
                        {Object.keys(state?.allFriendsId)?.includes(message?.user?.id) ? (
                            <Text style={styles.imageTextStyle}>{message?.user?.first_name.trim('')[0]}</Text>
                        ) : (
                            <Text style={styles.imageTextStyle}>{message?.user?.username.trim('')[0]}</Text>
                        )}
                    </Pressable>
                );
            }
        } else return null;
    };

    const renderGameReview = () => {
        if (!message?.gameReview) return null;

        const { clubName, gameDate, review, photo } = message.gameReview;

        return (
            <TouchableOpacity style={styles.gameReviewContainer} onLongPress={onLongPress}>
                <Text style={styles.gameReviewSubHeader}>{clubName}</Text>
                <Text style={styles.gameReviewDate}>Game Date: {gameDate}</Text>
                {photo && (
                    <TouchableOpacity
                        onPress={() => navigation.navigate('Image Show', { review: message.gameReview })}
                        style={styles.imageWrapper}>
                        <FastImage
                            source={{ uri: photo, priority: FastImage.priority.normal }}
                            style={styles.reviewImage}
                            resizeMode={FastImage.resizeMode.cover}
                        />
                    </TouchableOpacity>
                )}

                <Text style={styles.gameReviewText}>{review}</Text>
            </TouchableOpacity>
        );
    };

    return (
        <>
            <View key={message.id} style={alignment === 'left' ? styles.leftContainer : styles.rightContainer}>
                {handleMessageThreadUserProfile()}

                <View>
                    {alignment === 'left' &&
                        (message?.user?.deleted_at ? (
                            <Text style={styles.deletedUserText}>Deleted User</Text>
                        ) : (
                            <Text style={styles.userName}>
                                {handleMessageScreenParticipantsName(state?.allFriendsId, message?.user, user)}
                            </Text>
                        ))}

                    <View>
                        {message?.type === 'deleted' ? (
                            <View style={styles.chatContainer}>{deletedMessages()}</View>
                        ) : message.text === 'Game Review' && message?.gameReview ? (
                            <View style={styles.chatContainer}>{renderGameReview()}</View>
                        ) : message?.attachments?.length && message?.attachments?.length <= 3 ? (
                            message?.attachments?.map((item, index) => {
                                return (
                                    <View style={styles.chatContainer}>
                                        <ShowChatImages
                                            messageData={message}
                                            alignment={alignment}
                                            onLongPress={onLongPress}
                                            onPressClick={onPressClick}
                                            media={item}
                                            isLast={index === message?.attachments?.length - 1}
                                        />
                                    </View>
                                );
                            })
                        ) : (
                            <View style={styles.chatContainer}>
                                <ShowChatImages
                                    messageData={message}
                                    alignment={alignment}
                                    onLongPress={onLongPress}
                                    onPressClick={onPressClick}
                                />
                            </View>
                        )}
                        {reactions.length > 0 && <View style={{ marginTop: 15 }} />}

                        {reactions.length > 0 && (
                            <View style={styles.reactionContainer}>
                                <View style={styles.reaction}>
                                    <FlatList
                                        horizontal
                                        data={reactions}
                                        renderItem={({ item }) => (
                                            <>
                                                {item.type === 'love' && (item.own ? <LoveBlue /> : <Love />)}
                                                {item.type === 'like' && (item.own ? <LikeBlue /> : <Like />)}
                                                {item.type === 'sad' && (item.own ? <SadBlue /> : <Sad />)}
                                                {item.type === 'haha' && (item.own ? <LolBlue /> : <Lol />)}
                                                {item.type === 'wow' && (item.own ? <WowBlue /> : <Wow />)}
                                            </>
                                        )}
                                        keyExtractor={(item, index) => index.toString()}
                                    />
                                </View>
                            </View>
                        )}
                    </View>
                </View>
            </View>
        </>
    );
};
const makeStyles = (alignment) =>
    StyleSheet.create({
        leftContainer: {
            flexDirection: 'row',
            marginHorizontal: Spacing.SCALE_5,
            marginVertical: Spacing.SCALE_3,
        },

        rightContainer: {},

        imageContainer: {
            alignSelf: 'flex-start',
            marginRight: Spacing.SCALE_6,
            width: Size.SIZE_32,
            height: Size.SIZE_32,
            borderRadius: 75,
            marginTop: Spacing.SCALE_17,
        },
        image: {
            width: Size.SIZE_32,
            height: Size.SIZE_32,
            borderRadius: 75,
        },
        chatContainer: {
            backgroundColor: alignment === 'left' ? '#FFFFFF' : '#098089',
            maxWidth: alignment === 'left' ? Size.SIZE_250 : Size.SIZE_290,
            alignSelf: alignment === 'left' ? 'flex-start' : 'flex-end',
            borderTopLeftRadius: alignment === 'right' ? Spacing.SCALE_8 : null,
            borderTopRightRadius: Spacing.SCALE_8,
            borderBottomLeftRadius: Spacing.SCALE_8,
            borderBottomRightRadius: alignment === 'left' ? Spacing.SCALE_8 : null,
            borderColor: '#ECEBEB',
            borderWidth: alignment === 'left' ? 1 : null,
            marginHorizontal: alignment === 'left' ? null : Spacing.SCALE_16,
            marginVertical: alignment === 'left' ? null : Spacing.SCALE_3,
            minWidth: Spacing.SCALE_120,
        },
        message: {
            color: alignment === 'left' ? '#333333' : '#FFFFFF',
            paddingTop: Spacing.SCALE_10,
            paddingHorizontal: Spacing.SCALE_8,
            fontSize: Typography.FONT_SIZE_14,
            fontFamily: 'Ubuntu-Medium',
            lineHeight: Size.SIZE_20,
        },
        time: {
            color: alignment === 'left' ? '#333333' : '#FFFFFF',
            alignSelf: 'flex-end',
            marginRight: alignment === 'left' ? Spacing.SCALE_12 : Spacing.SCALE_6,
            fontSize: Typography.FONT_SIZE_10,
            marginVertical: Spacing.SCALE_8,
            fontFamily: 'Ubuntu-Medium',
        },
        imageWrapper1: {
            width: Size.SIZE_32,
            height: Size.SIZE_32,
            borderRadius: Size.SIZE_100,
            justifyContent: 'center',
            alignItems: 'center',
            alignSelf: 'flex-start',
            backgroundColor: 'rgba(9, 128, 137, 1)',
            marginRight: Spacing.SCALE_6,
            marginTop: Spacing.SCALE_17,
        },
        deletedUserIconWrapper: {
            width: Size.SIZE_32,
            height: Size.SIZE_32,
            borderRadius: Size.SIZE_100,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'white',
            marginRight: Spacing.SCALE_6,
            marginTop: Spacing.SCALE_10,
            alignSelf: 'flex-start',
        },
        imageTextStyle: {
            textAlign: 'center',
            fontSize: Typography.FONT_SIZE_20,
            color: 'rgba(255, 255, 255, 1)',
            fontFamily: 'Ubuntu-Medium',
            fontWeight: '500',
            textTransform: 'capitalize',
        },
        deletedImageTextStyle: {
            textAlign: 'center',
            fontSize: 25,
            color: colors.lightShadeGray,
            fontFamily: 'Ubuntu-Medium',
            fontWeight: '500',
            textTransform: 'capitalize',
        },
        userName: {
            fontSize: Typography.FONT_SIZE_12,
            color: 'rgba(51,51,51,1)',
            fontFamily: 'Ubuntu-Medium',
            fontWeight: '500',
            marginBottom: Spacing.SCALE_1,
        },
        deletedUserText: {
            fontSize: Typography.FONT_SIZE_12,
            color: 'rgba(153,153,153,1)',
            fontFamily: 'Ubuntu-Medium',
            fontWeight: '500',
            marginBottom: Spacing.SCALE_1,
        },
        reactionContainer: {
            position: 'absolute',
            bottom: alignment === 'left' ? -8 : -2,
            marginTop: 50,
            marginLeft: alignment === 'left' ? 10 : 100,
            alignSelf: alignment === 'left' ? 'flex-start' : 'flex-end',
            paddingRight: alignment === 'left' ? 0 : 20,
        },
        reaction: {
            flexDirection: 'row',
            backgroundColor: colors.backGray,
            padding: 5,
            borderRadius: 20,
        },
        gameReviewContainer: {
            padding: Spacing.SCALE_12,
        },
        gameReviewHeader: {
            fontSize: Typography.FONT_SIZE_16,
            fontFamily: 'Ubuntu-Medium',
            color: alignment === 'left' ? colors.lightBlack : colors.white,
            marginBottom: Spacing.SCALE_8,
            fontWeight: 'bold',
        },
        gameReviewSubHeader: {
            fontSize: Typography.FONT_SIZE_14,
            fontFamily: 'Ubuntu-Medium',
            color: alignment === 'left' ? colors.lightBlack : colors.white,
            marginBottom: Spacing.SCALE_4,
            fontWeight: '500',
        },
        gameReviewDate: {
            fontSize: Typography.FONT_SIZE_12,
            fontFamily: 'Ubuntu-Regular',
            color: alignment === 'left' ? colors.darkgray : colors.white,
            marginBottom: Spacing.SCALE_8,
            fontWeight: '400',
        },
        gameReviewText: {
            fontSize: Typography.FONT_SIZE_14,
            fontFamily: 'Ubuntu-Regular',
            fontWeight: '400',
            color: alignment === 'left' ? colors.lightBlack : colors.white,
            lineHeight: Size.SIZE_20,
        },
        imageWrapper: {
            marginTop: Spacing.SCALE_8,
            width: '100%',
            marginBottom: Spacing.SCALE_12,
        },
        reviewImage: {
            width: '100%',
            height: 200,
            borderRadius: Spacing.SCALE_8,
        },
    });
export default ShowChatMessage;
