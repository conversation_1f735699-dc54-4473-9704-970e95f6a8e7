import { Platform, StyleSheet } from 'react-native';

import { Size, Spacing, Typography } from '../../../../utils/responsiveUI';
import { colors } from '../../../../theme/theme';

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.whiteRGB,
    },
    headerTitleStyle: {
        color: colors.lightBlack,
        textAlign: 'left',
        marginLeft: Spacing.SCALE_10,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_1,
    },
    bodyWrapper: {
        flex: 1,
        backgroundColor: colors.screenBG,
    },
    body: {
        flex: 1,
    },
    topSpace: {
        marginTop: 10,
        backgroundColor: colors.lightgray,
        height: Size.SIZE_12,
    },
    box: {
        flex: 1,
        backgroundColor: 'rgba(242, 242, 242, 1)',
        borderTopRightRadius: Size.SIZE_10,
        borderTopLeftRadius: Size.SIZE_10,
    },
    aboutWrapper: {
        backgroundColor: 'rgba(255, 255, 255, 1)',
        padding: Spacing.SCALE_20,
        marginTop: Spacing.SCALE_12,
        borderRadius: Size.SIZE_4,
    },
    aboutHeader: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_22,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Medium',
        color: 'rgba(128, 128, 128, 1)',
    },
    aboutText: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_22,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.lightBlack,
        marginTop: Spacing.SCALE_12,
    },
    blockIconWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.white,
    },
    blockTextStyle: {
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_18,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.orange,
        marginLeft: Spacing.SCALE_10,
    },
    unBlockTextStyle: {
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_18,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: 'rgba(9, 128, 137, 1)',
        marginLeft: Spacing.SCALE_10,
    },
    loaderStyle: {
        position: 'absolute',
        top: 0,
        bottom: 0,
        right: 0,
        left: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    blockText: {
        fontSize: Typography.FONT_SIZE_14,
        lineHeight: Size.SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: 'rgba(0, 0, 0, 1)',
        marginTop: Spacing.SCALE_28,
    },
    GroupDetailBlockIconStyle: {
        marginTop: Spacing.SCALE_12,
        backgroundColor: colors.whiteRGB,
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: Spacing.SCALE_20,
    },
    imageStyle: {
        width: '100%',
        height: Size.SIZE_96,
        resizeMode: 'cover',
    },
    profileIconWrapper: {
        position: 'absolute',
        marginTop: 0,
        top: -Spacing.SCALE_64,
        height: Size.SIZE_92,
        width: Size.SIZE_92,
        borderRadius: Size.SIZE_100,
        left: Spacing.SCALE_16,
    },
    memberShip: {
        fontSize: Size.SIZE_13,
        fontWeight: '400',
        lineHeight: Size.SIZE_15,
        fontFamily: 'Ubuntu-Regular',
        color: 'rgba(128, 128, 128, 1)',
        alignSelf: 'center',
        textTransform: 'uppercase',
        marginRight: Spacing.SCALE_8,
    },
    memberShipWrapper: {
        flexDirection: 'row',
        alignSelf: 'flex-end',
        alignItems: 'center',
    },
    tierWrapper: {
        backgroundColor: colors.opacityTeal,
        borderRadius: Size.SIZE_6,
        alignItems: 'center',
        justifyContent: 'center',
    },
    tierTextStyle: {
        color: colors.tealRgb,
        fontFamily: 'Ubuntu-Medium',
        fontWeight: '700',
        fontSize: Typography.FONT_SIZE_10,
        lineHeight: Size.SIZE_10,
        paddingHorizontal: Spacing.SCALE_8,
        paddingVertical: Spacing.SCALE_6,
        textTransform: 'uppercase',
        alignSelf: 'center',
        marginTop: Platform.OS === 'android' ? Spacing.SCALE_1 : 0,
        letterSpacing: 1, // 10% of 10 = 1
    },
    name: {
        fontSize: Typography.FONT_SIZE_20,
        fontWeight: '500',
        lineHeight: Size.SIZE_24,
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
        marginTop: Spacing.SCALE_8,
    },
    badgeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        height: Size.SIZE_20,
        marginTop: Spacing.SCALE_8,
    },
    fcmUserContainer: {
        backgroundColor: colors.opacityTeal,
        paddingHorizontal: Spacing.SCALE_8,
        borderRadius: Size.SIZE_6,
        height: Size.SIZE_22,
        justifyContent: 'center',
        alignItems: 'center',
    },
    fcmTextStyle: {
        fontFamily: 'Ubuntu-Medium',
        fontSize: Typography.FONT_SIZE_10,
        fontWeight: '500',
        color: colors.darkteal,
        lineHeight: Typography.FONT_SIZE_10,
    },
    squareSection: {
        paddingHorizontal: Spacing.SCALE_10,
        paddingVertical: Spacing.SCALE_4,
        borderRadius: Size.SIZE_6,
        borderWidth: 1,
        borderColor: colors.divider,
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_2,
    },
    squareText: {
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.fadeBlack,
    },
    squareValue: {
        fontSize: Typography.FONT_SIZE_12,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.lightBlack,
    },
    ageAndGolfIndexContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_5,
        marginTop: Spacing.SCALE_12,
    },
    buttonContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        columnGap: Spacing.SCALE_10,
        marginTop: Spacing.SCALE_20,
    },
    requestButton: {
        backgroundColor: colors.tealRgb,
        borderRadius: Size.SIZE_8,
        paddingVertical: Spacing.SCALE_11,
        paddingHorizontal: Spacing.SCALE_16,
        borderWidth: 1,
        borderColor: colors.darkteal,
    },
    sendMessageBtn: {
        backgroundColor: colors.opacityTeal,
        borderRadius: Size.SIZE_8,
        paddingVertical: Spacing.SCALE_11,
        paddingHorizontal: Spacing.SCALE_16,
        borderWidth: 1,
        borderColor: colors.darkteal,
    },
    requestTextStyle: {
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_18,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.whiteColor,
    },
    messageTextStyle: {
        fontSize: Typography.FONT_SIZE_12,
        lineHeight: Size.SIZE_18,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.darkteal,
    },
    userProfileInfo: {
        backgroundColor: colors.white,
        paddingHorizontal: Spacing.SCALE_16,
        paddingTop: Spacing.SCALE_10,
        paddingBottom: Spacing.SCALE_16,
    },
    socialLinkTextStyle: {
        color: colors.lightBlack,
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_20,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
    },
    cardWrapper: {
        backgroundColor: colors.white,
        padding: Spacing.SCALE_16,
        marginTop: Spacing.SCALE_12,
    },
    cardHeader: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        color: colors.lightBlack,
    },
    viewAllText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.darkteal,
    },
    cardHeaderContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: Spacing.SCALE_12,
        alignItems: 'center',
    },
    contactContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_7,
    },
    contactText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.lightBlack,
    },
    gamePlayedText: {
        fontSize: Typography.FONT_SIZE_14,
        fontWeight: '400',
        fontFamily: 'Ubuntu-Regular',
        color: colors.systemMessageText,
    },
    gamePlayedContainer: {
        marginTop: Spacing.SCALE_20,
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_6,
    },
});

export default styles;
