import { FlatList, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import FastImage from 'react-native-fast-image';

//Assets, theme, utils imports
import { colors } from '../../../../theme/theme';
import { Spacing } from '../../../../utils/responsiveUI';
import { ClubIcon } from '../../../../assets/svg';

interface UserClubs {
    name: string;
    muted: boolean;
    lowest_visible_tier: string;
    id: number;
    image?: string;
}

const CardListComponent = ({
    userClubs,
    isMutualFriends = false,
}: {
    userClubs: UserClubs[];
    isMutualFriends?: boolean;
}) => {
    return (
        <View style={styles.container}>
            <FlatList
                data={userClubs.slice(0, 3)}
                renderItem={({ item }) => (
                    <View style={styles.itemContainer} key={item?.id}>
                        <View style={styles.iconContainer}>
                            {isMutualFriends ? (
                                item?.image ? <FastImage
                                    source={{ uri: item?.image }}
                                    style={[
                                        { width: Spacing.SCALE_16, height: Spacing.SCALE_16 },
                                        styles.iconContainer,
                                    ]}
                                    resizeMode={FastImage.resizeMode.contain}
                                /> : 
                                <Text style={styles.iconText}>{item?.name?.charAt(0)}</Text>
                            ) : (
                                <ClubIcon width={Spacing.SCALE_16} height={Spacing.SCALE_16} />
                            )}
                        </View>
                        <Text>{item.name}</Text>
                    </View>
                )}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
                keyExtractor={(item) => item.id.toString()}
            />
        </View>
    );
};

export default CardListComponent;

const styles = StyleSheet.create({
    container: {
        borderWidth: 1.5,
        borderRadius: 14,
        borderColor: colors.lightGrey,
        paddingVertical: Spacing.SCALE_10,
    },
    itemContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: Spacing.SCALE_12,
        paddingHorizontal: Spacing.SCALE_12,
    },
    iconContainer: {
        height: Spacing.SCALE_24,
        width: Spacing.SCALE_24,
        backgroundColor: colors.lightGrey,
        borderRadius: Spacing.SCALE_100,
        alignItems: 'center',
        justifyContent: 'center',
    },
    separator: {
        height: 1.5,
        backgroundColor: colors.lightGrey,
        marginVertical: Spacing.SCALE_10,
    },
    iconText: {
        fontSize: Spacing.SCALE_14,
        fontWeight: '500',
        color: colors.tealRgb,
        fontFamily: 'Ubuntu-Medium',
    },
});
