import { <PERSON><PERSON>ist, <PERSON><PERSON>reshControl, StatusBar, StyleSheet, View } from 'react-native';
import React, { useContext, useEffect, useState } from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

//components and context imports
import ProfileHeader from '../../../../components/layout/ProfileHeader';
import GameReviewListingCard from '../../../map/view/gameReview/GameReviewListingCard';
import RequestScreenSkelton from '../../../requests/view/RequestScreenSkelton';
import showToast from '../../../../components/toast/CustomToast';
import { GlobalContext } from '../../../../context/contextApi';

//theme, apiServices, interfaces and utils imports
import { colors } from '../../../../theme/theme';
import { Spacing, Typography } from '../../../../utils/responsiveUI';
import { apiServices } from '../../../../service/apiServices';
import { RootStackParamList } from '../../../../interface/type';
import { GameReview } from '../../../../interface';

interface UsersAllGameReviewScreenProps {
    route: {
        params: {
            golferId: string;
            userId: string;
        };
    };
    navigation: NativeStackNavigationProp<RootStackParamList, 'UsersAllGameReviewScreen'>;
}
const UsersAllGameReviewScreen = ({ route, navigation }: UsersAllGameReviewScreenProps) => {
    const [gameReviews, setGameReviews] = useState<GameReview[]>([]);
    const { golferId, userId } = route.params;
    const { actions, state } = useContext(GlobalContext);
    const [page, setPage] = useState(0);
    const [limit] = useState(10);
    const [hasNextPage, setHasNextPage] = useState(false);

    useEffect(() => {
        actions.setAppSkeltonLoader(true);
        getGameReviews();
    }, []);

    const getGameReviews = (isRefresh: boolean = false) => {
        apiServices
            .getGolfersGameReview({ userId: userId, golferId: golferId, page: isRefresh ? 0 : page, limit: limit })
            .then((res) => {
                if (res.status) {
                    setHasNextPage(res?.data?.pagination?.hasNextPage);
                    if (res?.data?.pagination?.hasNextPage) {
                        setPage(res?.data?.pagination?.currentPage + 1);
                    }
                    if (isRefresh) {
                        setGameReviews(res?.data?.gameReviews);
                    } else {
                        setGameReviews([...gameReviews, ...res?.data?.gameReviews]);
                    }
                    actions.setAppLoader(false);
                    actions.setAppSkeltonLoader(false);
                } else {
                    showToast({
                        message: res?.message,
                        type: 'error',
                    });
                    actions.setAppSkeltonLoader(false);
                    actions.setAppLoader(false);
                }
            });
    };

    const handleRefresh = () => {
        actions.setAppLoader(true);
        setPage(0);
        setGameReviews([]);
        getGameReviews(true);
    };

    return (
        <>
            <StatusBar barStyle="light-content" backgroundColor={colors.whiteRGB} />
            <View style={styles.container}>
                <ProfileHeader
                    title={'Game Reviews'}
                    headerTitleStyle={styles.headerTitleStyle}
                    backButtonFillColor={colors.lightBlack}
                    containerStyle={{
                        backgroundColor: colors.whiteRGB,
                        paddingBottom: Spacing.SCALE_10,
                    }}
                />
                <View style={styles.bodyWrapper}>
                    <View style={styles.body}>
                        {state.appSkeltonLoader ? (
                            <RequestScreenSkelton screen="Common" />
                        ) : (
                            <FlatList
                                data={gameReviews}
                                renderItem={({ item }) => (
                                    <GameReviewListingCard
                                        item={item}
                                        comeFrom="Profile Info"
                                        navigation={navigation}
                                    />
                                )}
                                contentContainerStyle={{ paddingHorizontal: Spacing.SCALE_16 }}
                                keyExtractor={(item: GameReview) => item.game_id.toString()}
                                refreshControl={
                                    <RefreshControl refreshing={state.appSkeltonLoader} onRefresh={handleRefresh} />
                                }
                                onEndReached={() => {
                                    if (hasNextPage) {
                                        actions.setAppLoader(true);
                                        setPage(page + 1);
                                        getGameReviews();
                                    }
                                }}
                                onEndReachedThreshold={0.5}
                                initialNumToRender={10}
                                maxToRenderPerBatch={10}
                            />
                        )}
                    </View>
                </View>
            </View>
        </>
    );
};

export default UsersAllGameReviewScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.whiteRGB,
    },
    headerTitleStyle: {
        color: colors.lightBlack,
        textAlign: 'left',
        marginLeft: Spacing.SCALE_10,
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
        marginBottom: Spacing.SCALE_1,
    },
    bodyWrapper: {
        flex: 1,
        backgroundColor: colors.screenBG,
    },
    body: {
        flex: 1,
        paddingVertical: Spacing.SCALE_12,
    },
});
