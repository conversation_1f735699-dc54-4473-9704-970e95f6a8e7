import React, { useContext, useEffect, useMemo, useState } from 'react';
import { FlatList, Linking, StatusBar, Text, TouchableOpacity, View } from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';

import { AuthContext } from '../../../../context/AuthContext';
import { GlobalContext } from '../../../../context/contextApi';
import { StreamChatContext } from '../../../../context/StreamChatContext';
import { getGolferId } from '../action/userProfileAction';
import styles from '../style/UserProfileStyle';
import GroupDetailBlockIcon from '../../../../assets/svg/GroupDetailBlockIcon.svg';
import { colors } from '../../../../theme/theme';
import { handleBlockUser, handleUnBlockUser } from '../../action';
import { handleOnPress } from '../../TGChatComponent/AlertComponent';
import BlockIconComponent from './BlockIconComponent';
import { BLOCK_USER_POPUP_TEXT, UNBLOCK_USER_POPUP_TEXT } from '../../client';
import { createOneToOneChatFrndScreen1 } from '../../../my-TG-friends/action/createOnetoOneChat';
import ProfileHeader from '../../../../components/layout/ProfileHeader';
import { Size, Spacing } from '../../../../utils/responsiveUI';
import {
    CreateRequestIconWhite,
    EmailGreyIcon,
    EyeIcon,
    MessageIconNew,
    PhoneGreyIcon,
    PlusIconWhite,
    RequestAcceptedIcon,
    WithDrawIcon,
} from '../../../../assets/svg';
import FastImage from 'react-native-fast-image';
import { localImages } from '../../../../assets/images';
import ProfileIcon from './ProfileIconComponent';
import moment from 'moment';
import { tiers } from '../../../../utils/constants/constants';
import TGCustomIconButton from '../../../../components/buttons/TGCustomIconButton';
import SocialLinkComponent from '../../../setting/view/SocialLinkComponent';
import CardListComponent from './CardListComponent';
import { apiServices } from '../../../../service/apiServices';
import RequestScreenSkelton from '../../../requests/view/RequestScreenSkelton';
import ViewAllPopup from './ViewAllPopup';
import { GAME_REVIEW, GOLF_CLUBS, MUTUAL_TG_GROUPS } from '../../../../utils/constants/strings';
import GameReviewListingCard from '../../../map/view/gameReview/GameReviewListingCard';
import { handleChannelCreation } from '../../newChat/action/getFriendList';
import { checkSendFriendRequest } from '../action/checkSendFriendRequest';
import { withdrawRequest } from '../../../my-TG-friends/action/withdrawRequest';
import DeclineScreenAddPopup from '../../../my-TG-friends/view/DeclineScreenAddPopup';
import { sendFriendRequestDeclinedScreen } from '../../../my-TG-friends/action/sendFriendRequest';
import CustomBottomSheet from '../../../../components/customBottomSheet/CustomBottomSheet';
import formatPhoneNumber from '../../../../utils/helpers/phoneNumberFormatter';

const UserProfileScreen = ({ route, navigation }: any) => {
    const { selectedUser, comeFromRequest = false, isComeFromChat = false } = route.params;
    const { user } = useContext(AuthContext);
    const { channel, updateBlockedChannel, setChannel, client } = useContext(StreamChatContext);
    const { state, actions } = useContext(GlobalContext);
    const [viewAllPopup, setViewAllPopup] = useState<string>('');
    const [isAnyUnmuteClub, setIsAnyUnmuteClub] = useState(false);
    const [showAddFriend, setShowAddFriend] = useState(false);
    const [addFriendPopup, setAddFriendPopup] = useState(false);
    const [unmutedClubs, setUnmutedClubs] = useState([]);
    const [isOpenCustomBottomSheet, setIsOpenCustomBottomSheet] = useState(false);
    const [options, setOptions] = useState<{ label: string; action: () => void }[]>([]);
    const [oneToOneChannel, setOneToOneChannel] = useState<any>({});

    const {
        about_yourself,
        streamChannelId,
        youHaveBlocked,
        isBlocked,
        userClubs,
        userProfileObjectLength,
        blockText,
        id,
        isFriends,
        private_network_id,
        isPNMember,
        btnText,
        alertBoxText,
        tg_ambassador_visibility,
        profilePhoto,
        name,
        memberSince,
        is_tg_founder,
        isSuperHost,
        age,
        golfIndex,
        gamesPlayed,
        facebook,
        linkedin,
        gameReviews,
        commonGroups,
        muted,
        isFriendRequestSent,
        isFriendRequestDeclined,
        friendRequestId,
        isFriendRequestReceived,
        email,
        phone,
        hasActiveRequestsBetween,
        gameReviewsCount,
        tier
    } = useMemo(() => {
        const { userProfile } = state;
        let userProfileObjectLength = userProfile && Object.keys(userProfile)?.length;
        const {
            about_yourself,
            blockStatus: { streamChannelId, youHaveBlocked } = {},
            isBlocked,
            userClubs,
            id,
            isFriends,
            private_network_id,
            tg_ambassador_visibility,
            profilePhoto,
            name,
            memberSince,
            is_tg_founder,
            isSuperHost,
            age,
            golfIndex,
            gamesPlayed,
            facebook,
            linkedin,
            gameReviews,
            commonGroups,
            muted,
            isFriendRequestSent,
            isFriendRequestDeclined,
            friendRequestId,
            isFriendRequestReceived,
            email,
            phone,
            hasActiveRequestsBetween,
            gameReviewsCount,
            tier
        } = ({} = userProfile);
        let blockText = isBlocked && youHaveBlocked ? 'Unblock User' : 'Block User';
        let btnText = isBlocked && youHaveBlocked ? 'Unblock' : 'Block';
        let alertBoxText = isBlocked && youHaveBlocked ? UNBLOCK_USER_POPUP_TEXT : BLOCK_USER_POPUP_TEXT;
        let isPNMember = userClubs?.length === 1 && private_network_id ? true : false;
        return {
            about_yourself,
            streamChannelId,
            youHaveBlocked,
            isBlocked,
            userClubs,
            userProfileObjectLength,
            blockText,
            id,
            isFriends,
            private_network_id,
            isPNMember,
            btnText,
            alertBoxText,
            tg_ambassador_visibility,
            profilePhoto,
            name,
            memberSince: moment(memberSince).format('YYYY'),
            is_tg_founder,
            isSuperHost,
            age,
            golfIndex,
            gamesPlayed,
            facebook,
            linkedin,
            gameReviews,
            commonGroups,
            muted,
            isFriendRequestSent,
            isFriendRequestDeclined,
            friendRequestId,
            isFriendRequestReceived,
            email,
            phone: formatPhoneNumber(phone),
            hasActiveRequestsBetween,
            gameReviewsCount,
            tier
        };
    }, [state?.userProfile]);

    useEffect(() => {
        actions.setMyClubs(user?.clubs?.map((data: any) => data?.club?.name));

        let clubs = userClubs?.filter((userClub: any) => {
            return !user?.clubs?.some((club: any) => club?.club_id === userClub?.id) && !userClub?.muted;
        });

        if (clubs?.length) {
            setIsAnyUnmuteClub(true);
        } else {
            setIsAnyUnmuteClub(false);
        }

        setUnmutedClubs(clubs);
    }, [user, userClubs]);

    useEffect(() => {
        actions?.setCurrentScreenName(null);
        handleGetProfileInfo();
    }, [state?.otherUser?.user?.name, state?.otherUser?.user?.image]);

    useEffect(() => {
        if (selectedUser?.id) {
            createOneToOneChatFrndScreen1(
                user?.id,
                selectedUser?.id,
                () => {},
                () => {},
                () => {},
                //@ts-ignore
                setOneToOneChannel,
            );
        }
    }, [selectedUser]);

    const handleGetProfileInfo = async () => {
        actions.setAppSkeltonLoader(true);
        try {
            let golfertId = getGolferId(user?.id, channel, selectedUser);
            let res = await apiServices.getGolferProfilePhoto(user?.id, golfertId, actions);
            if (res?.status) {
                checkSendFriendRequest(user?.id, res?.golferInfo[0]?.id, setShowAddFriend);
                setTimeout(() => {
                    actions.setAppSkeltonLoader(false);
                }, 1000);
            }
        } catch (error) {
            actions.setAppSkeltonLoader(false);
        }
    };

    const handleUpdateBlockedChannel = (channel: any) => {
        updateBlockedChannel(channel);
    };

    //Function for handling request button text (texts are :- Withdraw Request, View Request, Add Friend)
    const handleRequestBtnText = () => {
        if (isFriendRequestSent && !isFriendRequestDeclined) {
            return 'Withdraw';
        } else if (isFriendRequestReceived && !isFriendRequestDeclined) {
            return 'View Request';
        } else if (isFriendRequestDeclined) {
            return 'Add Friend';
        } else {
            return 'Add Friend';
        }
    };

    const handleIcon = () => {
        if (friendRequestId === null || (isFriendRequestSent && isFriendRequestDeclined)) {
            return <PlusIconWhite />;
        } else if (isFriendRequestSent) {
            return <WithDrawIcon />;
        } else if (isFriendRequestReceived && !isFriendRequestDeclined) {
            return <EyeIcon />;
        } else {
            return <PlusIconWhite />;
        }
    };

    const handleClick = () => {
        if (
            (friendRequestId === null && !isFriendRequestSent) ||
            (isFriendRequestSent && isFriendRequestDeclined) ||
            friendRequestId === null
        ) {
            setAddFriendPopup(true);
        } else if (isFriendRequestReceived && !isFriendRequestDeclined) {
            actions?.setFriendTabNavigation('Received');
            actions.setCurrentTab(2);
            navigation.navigate('MyTGFriendsScreen', {
                data: 2,
            });
        } else if (isFriendRequestSent) {
            handleWithDrawRequest();
        } else {
            setAddFriendPopup(true);
        }
    };

    const showSingleButton = () => {
        if (
            (isFriends && !muted && isAnyUnmuteClub) ||
            (!isBlocked && !isFriends && (showAddFriend || isFriendRequestReceived || isFriendRequestSent))
        ) {
            return false;
        } else {
            return true;
        }
    };

    //Withdraw friend request
    const handleWithDrawRequest = () => {
        withdrawRequest(user?.id, friendRequestId, () => {}, handleGetProfileInfo);
    };

    // Check user can create request or not
    const handleCreateRequest = () => {
        apiServices.checkCanCreateRequest(user, false).then((res) => {
            actions.setAppLoader(true);
            if (res?.canCreate) {
                navigation.navigate('CreateRequestMap', {
                    club: unmutedClubs?.map((data: any) => data?.name),
                    createOneToOneRequest: unmutedClubs?.length == 1 ? false : true,
                    friend_id: id,
                    screen: 'allFriendScreen',
                });
            } else {
                navigation.navigate('DeleteChannelConfirmationPopup', {
                    handleYesButton: async () => {},
                    popupSubText: res?.message,
                    firstBtnLabel: 'Dismiss',
                    secondBtnLabel: 'ok',
                });
            }
            actions.setAppLoader(false);
        });
    };

    const handleSendMessage = async () => {
        actions.setAppLoader(true);
        //@ts-ignore
        await createOneToOneChatFrndScreen1(user?.id, id, actions.setAppLoader, oneToOneChannelCreation);
    };

    const oneToOneChannelCreation = (res: any) => {
        handleUpdateBlockedChannel(res?.channel);
        handleChannelCreation(
            client,
            user,
            res?.channel?.id,
            navigation,
            actions.setAppLoader,
            () => {},
            setChannel,
            actions,
        );
    };

    const unBlockUser = () => {
        handleUnBlockUser(user?.id, oneToOneChannel?.data?.id, id, actions, handleGetProfileInfo);
    };
    const blockUser = () => {
        handleBlockUser(user?.id, oneToOneChannel?.data?.id, id, actions, handleGetProfileInfo);
    };

    const handlePress = () => {
        if (blockText === 'Unblock User') {
            handleOnPress(channel, unBlockUser, alertBoxText, btnText, navigation);
        } else if (blockText === 'Block User') {
            handleOnPress(channel, blockUser, alertBoxText, btnText, navigation);
        }
    };

    const handleAddFriend = (id: string, requestNote: string) => {
        sendFriendRequestDeclinedScreen(user?.id, id, requestNote, undefined, () => {}, handleGetProfileInfo);
    };

    const HandleGolfClubRender = ({ isMutualFriends = false, clubs }: { isMutualFriends?: boolean; clubs: any[] }) => {
        if (clubs?.length >= 1 || (clubs?.length >= 1 && private_network_id === null))
            return <CardListComponent userClubs={clubs || []} isMutualFriends={isMutualFriends} />;
        return null;
    };

    const SquareSection = ({ title, value }: { title: string; value: string }) => {
        return (
            <View style={styles.squareSection}>
                <Text style={styles.squareText}>{title}:</Text>
                <Text style={styles.squareValue}>{value}</Text>
            </View>
        );
    };

    const renderHeader = () => (
        <>
            <FastImage source={localImages.profileInfoBGIcon} style={styles.imageStyle} />
            <View style={styles.body}>
                <ProfileIcon
                    profilePhoto={profilePhoto}
                    name={name}
                    tg_ambassador_visibility={tg_ambassador_visibility}
                    wrapperStyle={styles.profileIconWrapper}
                    width={Size.SIZE_87}
                    height={Size.SIZE_87}
                />

                {/* User profile info */}
                <View style={styles.userProfileInfo}>
                    {/* MemberShip UI */}
                    <View style={styles.memberShipWrapper}>
                        <Text style={styles.memberShip}>{`Member Since ${memberSince}`}</Text>
                        <View style={styles.tierWrapper}>
                            <Text style={styles.tierTextStyle}>{tiers[tier]}</Text>
                        </View>
                    </View>

                    {/* User name container */}
                    <Text style={styles.name}>{name}</Text>

                    {/* User Super host and founderClub member */}
                    {(is_tg_founder || isSuperHost) && (
                        <View style={styles.badgeContainer}>
                            {is_tg_founder && (
                                <View
                                    style={[
                                        styles.fcmUserContainer,
                                        isSuperHost ? { marginRight: Spacing.SCALE_6 } : {},
                                    ]}>
                                    <Text style={styles.fcmTextStyle}>Founder Club Member</Text>
                                </View>
                            )}

                            {isSuperHost && (
                                <View style={[styles.fcmUserContainer]}>
                                    <Text style={styles.fcmTextStyle}>Super Host</Text>
                                </View>
                            )}
                        </View>
                    )}

                    {/* About */}
                    <Text style={styles.aboutText}>{about_yourself}</Text>
                    {/* Age and Golf index */}
                    <View style={styles.ageAndGolfIndexContainer}>
                        {age && <SquareSection title="Age" value={age} />}
                        {golfIndex && <SquareSection title="Golf Index" value={golfIndex} />}
                    </View>

                    {/* Game Played section only visible for friends */}
                    {isFriends ||
                        (hasActiveRequestsBetween && (
                            <View style={styles.gamePlayedContainer}>
                                <RequestAcceptedIcon height={Size.SIZE_16} width={Size.SIZE_16} />
                                <Text style={styles.gamePlayedText}>
                                    Game Played: <Text style={{ color: colors.brightGreen }}>{gamesPlayed?.total}</Text>
                                </Text>
                            </View>
                        ))}

                    <View style={styles.buttonContainer}>
                        {userProfileObjectLength && !isBlocked && (
                            <TGCustomIconButton
                                text="Message"
                                onPress={handleSendMessage}
                                btnStyle={[styles.sendMessageBtn, showSingleButton() ? { width: '100%' } : {}]}
                                loading={false}
                                Icon={() => <MessageIconNew />}
                                textStyle={styles.messageTextStyle}
                            />
                        )}
                        {isFriends && !muted && isAnyUnmuteClub && (
                            <TGCustomIconButton
                                text="Request"
                                onPress={handleCreateRequest}
                                btnStyle={styles.requestButton}
                                loading={false}
                                Icon={() => <CreateRequestIconWhite />}
                                textStyle={styles.requestTextStyle}
                            />
                        )}
                        {!isBlocked &&
                        !isFriends &&
                        (showAddFriend || isFriendRequestReceived || isFriendRequestSent) ? (
                            <TGCustomIconButton
                                text={handleRequestBtnText()}
                                onPress={handleClick}
                                btnStyle={
                                    handleRequestBtnText() !== 'Add Friend'
                                        ? styles.sendMessageBtn
                                        : styles.requestButton
                                }
                                loading={false}
                                Icon={() => handleIcon()}
                                textStyle={
                                    handleRequestBtnText() !== 'Add Friend'
                                        ? styles.messageTextStyle
                                        : styles.requestTextStyle
                                }
                            />
                        ) : null}
                    </View>
                </View>
            </View>
        </>
    );

    const renderItem = ({ item, index }: { item: any; index: number }) => {
        if (index === 0) {
            return isFriends || hasActiveRequestsBetween ? (
                <View style={styles.cardWrapper}>
                    <Text style={styles.cardHeader}>Contact</Text>
                    <View style={{ rowGap: Spacing.SCALE_10, marginTop: Spacing.SCALE_12 }}>
                        {phone && (
                            <TouchableOpacity
                                style={styles.contactContainer}
                                onPress={() => {
                                    setOptions([
                                        { label: 'Copy', action: () => Clipboard.setString(phone) },
                                        { label: 'Call', action: () => Linking.openURL(`tel:${phone}`) },
                                    ]);
                                    setIsOpenCustomBottomSheet(true);
                                }}>
                                <PhoneGreyIcon />
                                <Text style={styles.contactText}>{phone}</Text>
                            </TouchableOpacity>
                        )}
                        {email && (
                            <TouchableOpacity
                                style={styles.contactContainer}
                                onPress={() => {
                                    setOptions([
                                        { label: 'Copy', action: () => Clipboard.setString(email) },
                                        { label: 'Email', action: () => Linking.openURL(`mailto:${email}`) },
                                    ]);
                                    setIsOpenCustomBottomSheet(true);
                                }}>
                                <EmailGreyIcon />
                                <Text style={styles.contactText}>{email}</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                </View>
            ) : null;
        }
        if (index === 1 && (facebook || linkedin)) {
            return (
                <View style={[styles.cardWrapper, { paddingTop: 0 }]}>
                    <SocialLinkComponent
                        socialLinkTextStyle={styles.socialLinkTextStyle}
                        facebookLink={facebook}
                        linkedinLink={linkedin}
                    />
                </View>
            );
        }
        if (index === 2 && userClubs?.length > 0) {
            return (
                <View style={styles.cardWrapper}>
                    <View style={styles.cardHeaderContainer}>
                        <Text style={styles.cardHeader}>Golf Club(s)</Text>
                        {userClubs?.length > 3 && (
                            <TouchableOpacity onPress={() => setViewAllPopup(GOLF_CLUBS)}>
                                <Text style={styles.viewAllText}>View All</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                    <HandleGolfClubRender clubs={userClubs} />
                </View>
            );
        }
        if (index === 3 && commonGroups?.length > 0 && !isBlocked) {
            return (
                <View style={styles.cardWrapper}>
                    <View style={styles.cardHeaderContainer}>
                        <Text style={styles.cardHeader}>Mutual TG Group(s)</Text>
                        {userClubs?.length > 3 && (
                            <TouchableOpacity onPress={() => setViewAllPopup(MUTUAL_TG_GROUPS)}>
                                <Text style={styles.viewAllText}>View All</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                    <HandleGolfClubRender isMutualFriends={true} clubs={commonGroups} />
                </View>
            );
        }
        if (index === 4 && gameReviewsCount && !isBlocked) {
            return (
                <View style={styles.cardWrapper}>
                    <View style={styles.cardHeaderContainer}>
                        <Text style={styles.cardHeader}>
                            {GAME_REVIEW}(s) ({gameReviewsCount})
                        </Text>
                        {gameReviewsCount > 3 && (
                            <TouchableOpacity
                                onPress={() =>
                                    navigation.navigate('UsersAllGameReviewScreen', { golferId: id, userId: user?.id })
                                }>
                                <Text style={styles.viewAllText}>View All</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                    {gameReviews?.slice(0, 3)?.map((item: any) => (
                        <GameReviewListingCard item={item} comeFrom="Profile Info" navigation={navigation} />
                    ))}
                </View>
            );
        }
        if (index === 5) {
            return !isFriends && (!isBlocked || (isBlocked && youHaveBlocked)) ? (
                <View style={[styles.cardWrapper, { marginBottom: Spacing.SCALE_12 }]}>
                    <BlockIconComponent
                        blockText={blockText}
                        handlePress={handlePress}
                        isBlocked={isBlocked}
                        youHaveBlocked={youHaveBlocked}
                    />
                </View>
            ) : null;
        }
        if (index === 6) {
            return (
                userProfileObjectLength &&
                isBlocked &&
                !youHaveBlocked && (
                    <View style={styles.GroupDetailBlockIconStyle}>
                        <GroupDetailBlockIcon />
                        <Text style={styles.blockText}>This user has blocked you!</Text>
                    </View>
                )
            );
        }
        return null;
    };

    const data = [
        { id: 'contacts' },
        { id: 'social' },
        { id: 'golfClubs' },
        { id: 'mutualGroups' },
        { id: 'gameReviews' },
        { id: 'blockSection' },
        { id: 'blockedUI' },
    ];

    return (
        <>
            <StatusBar barStyle="light-content" backgroundColor={colors.whiteRGB} />
            <View style={styles.container}>
                <ProfileHeader
                    title={'Profile Info'}
                    headerTitleStyle={styles.headerTitleStyle}
                    backButtonFillColor={colors.lightBlack}
                    containerStyle={{
                        backgroundColor: colors.whiteRGB,
                        paddingBottom: Spacing.SCALE_10,
                    }}
                    isComeFromChat={isComeFromChat}
                />
                {state.appSkeltonLoader ? (
                    <RequestScreenSkelton screen="golferProfile" />
                ) : (
                    <FlatList
                        data={data}
                        //@ts-ignore
                        renderItem={renderItem}
                        keyExtractor={(item) => item.id}
                        ListHeaderComponent={renderHeader}
                        showsVerticalScrollIndicator={false}
                        style={styles.bodyWrapper}
                    />
                )}
            </View>
            {viewAllPopup.length ? (
                <ViewAllPopup
                    popupState={[viewAllPopup, setViewAllPopup]}
                    popupData={viewAllPopup === GOLF_CLUBS ? userClubs : commonGroups}
                />
            ) : null}
            {addFriendPopup && (
                <DeclineScreenAddPopup
                    popupState={[addFriendPopup, setAddFriendPopup]}
                    data={state?.userProfile}
                    handleAddFriend={handleAddFriend}
                    screenName="Profile Info"
                />
            )}
            {isOpenCustomBottomSheet && (
                <CustomBottomSheet
                    isVisible={isOpenCustomBottomSheet}
                    onClose={() => setIsOpenCustomBottomSheet(false)}
                    options={options}
                    cancelLabel="Cancel"
                />
            )}
        </>
    );
};

export default UserProfileScreen;
