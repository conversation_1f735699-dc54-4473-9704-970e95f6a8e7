import React, { useContext, useEffect, useMemo } from 'react';
import { Modal, Pressable, StyleSheet, View, Text } from 'react-native';

import { AuthContext } from '../../../context/AuthContext';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { handleBlockUser, handleUnBlockUser } from '../action';
import { StreamChatContext } from '../../../context/StreamChatContext';
import { handleOnPress } from './AlertComponent';
import { BLOCK_USER_POPUP_TEXT, UNBLOCK_USER_POPUP_TEXT } from '../client';
import { GlobalContext } from '../../../context/contextApi';
import { useNavigation } from '@react-navigation/native';

const TripleDotPopupScreen = ({ route }: any) => {
    const {selectedChannel} = route.params;
    const { user } = useContext(AuthContext);
    const { state } = useContext(GlobalContext);
    const navigation = useNavigation();
    const { channel, setChannel } = useContext(StreamChatContext);

    const { alertBoxText, btnText } = useMemo(() => {
        let alertBoxText =
            channel?.data?.blockedBy === null || channel?.data?.blockedBy === undefined
                ? BLOCK_USER_POPUP_TEXT
                : UNBLOCK_USER_POPUP_TEXT;
        let btnText = channel?.data?.blockedBy === null || channel?.data?.blockedBy === undefined ? 'Block' : 'Unblock';
        return { alertBoxText, btnText };
    }, []);

    useEffect(() => {
        setChannel(channel);
    }, [channel?.data]);
    let blockText =
        channel?.data?.blockedBy && channel?.data?.blockedBy !== null && channel?.data?.blockedBy.includes(user?.id)
            ? 'Unblock User'
            : 'Block User';

    const handlePress = () => {
        if (channel?.data?.blockedBy || channel?.data?.blockedBy === null || channel?.data?.blockedBy === undefined) {
            let membersArray = state?.channelMembers;
            let otherUserId = membersArray?.filter((member: any) => member?.user_id !== user?.id);
            if (
                channel?.data?.blockedBy !== null &&
                channel?.data?.blockedBy !== undefined &&
                channel?.data?.blockedBy[0] === user?.id
            ) {
                handleUnBlockUser(user?.id, selectedChannel?.id, otherUserId[0].user_id);
            } else {
                handleBlockUser(user?.id, selectedChannel?.id, otherUserId[0].user_id);
            }
        }
    };

    return (
        <View style={styles.modalStyle}>
            <View style={styles.container}>
                <Pressable style={styles.blankScreenWrapper} />
                <View style={styles.bodyStyle}>
                    <View style={styles.popupWrapper}>
                        <Pressable
                            style={styles.popupContent}
                            onPress={() => {
                                if (channel?.data?.isFriends) {
                                    navigation.goBack();
                                } else {
                                    navigation.goBack();
                                    setTimeout(() => {
                                        handleOnPress(channel, handlePress, alertBoxText, btnText, navigation);
                                    }, 500);
                                }
                            }}>
                            <Text style={styles.popupOptionStyle}>
                                {channel?.data?.isFriends ? 'View Profile' : blockText}
                            </Text>
                        </Pressable>
                    </View>
                    <Pressable
                        style={styles.popupWrapper1}
                        onPress={() => {
                            navigation.goBack();
                        }}>
                        <Text style={styles.cancelBtnStyle}>Cancel</Text>
                    </Pressable>
                </View>
            </View>
        </View>
    );
};
export default TripleDotPopupScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    popupWrapper: {
        width: Size.SIZE_340,
        position: 'absolute',
        bottom: Spacing.SCALE_85,
        backgroundColor: 'rgba(248, 248, 248, 1)',
        borderRadius: Size.SIZE_18,
        marginHorizontal: Spacing.SCALE_6,
        alignItems: 'center',
    },
    popupWrapper1: {
        width: Size.SIZE_340,
        minHeight: Size.SIZE_54,
        position: 'absolute',
        bottom: Spacing.SCALE_24,
        backgroundColor: '#FFFFFF',
        borderRadius: Size.SIZE_14,
        marginHorizontal: 7,
        justifyContent: 'center',
        alignItems: 'center',
    },
    listHeaderText1: {
        fontSize: Typography.FONT_SIZE_11,
        color: 'white',
        fontFamily: 'Ubuntu-Medium',
    },
    logo: {
        width: Size.SIZE_18,
        height: Size.SIZE_18,
        tintColor: 'white',
    },
    popupHeaderText: {
        color: 'rgba(51, 51, 51, 1)',
        fontSize: Typography.FONT_SIZE_24,
        fontWeight: '500',
        lineHeight: Size.SIZE_27,
        paddingHorizontal: Spacing.SCALE_18,
        fontFamily: 'Ubuntu-Medium',
    },
    headerWrapper: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingVertical: Spacing.SCALE_20,
    },
    crossIconWrapper: {
        paddingHorizontal: 20,
        paddingTop: 5,
    },
    popupWrapperData: {
        flexDirection: 'row',
        paddingHorizontal: Spacing.SCALE_20,
        paddingVertical: 20,
    },
    clubIconWrapper: {
        width: Size.SIZE_45,
        height: Size.SIZE_45,
        backgroundColor: '#F2F2F2',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: Size.SIZE_4,
    },
    popupTextWrapper: {
        marginLeft: Spacing.SCALE_13,
        marginTop: Spacing.SCALE_10,
    },
    popupTextStyle: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        fontFamily: 'Ubuntu-Medium',
    },
    cancelBtnStyle: {
        color: 'rgba(0, 122, 255, 1)',
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '600',
        textAlign: 'center',
        fontFamily: 'Ubuntu-Medium',
    },
    popupContent: {
        height: Size.SIZE_40,
        justifyContent: 'center',
    },
    popupOptionStyle: {
        fontSize: Typography.FONT_SIZE_16,
        fontWeight: '500',
        color: 'rgba(0, 122, 255, 1)',
        fontFamily: 'Ubuntu-Medium',
    },
    modalStyle: {
        flex: 1,
    },
    blankScreenWrapper: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    bodyStyle: {
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        width: '100%',
        minHeight: '100%',
        justifyContent: 'center',
        alignItems: 'center',
    },
    dividerStyle: {
        backgroundColor: 'rgba(196, 196, 196, 1)',
        height: 1,
        width: '95%',
    },
});
