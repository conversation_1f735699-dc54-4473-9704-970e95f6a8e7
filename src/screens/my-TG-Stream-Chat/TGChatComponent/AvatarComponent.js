import React, { useMemo } from 'react'
import { useContext } from 'react';
import { Image, TouchableOpacity, View, Text, StyleSheet } from 'react-native';

import { AuthContext } from '../../../context/AuthContext';
import { colors } from '../../../theme/theme';
import { Size, Spacing, Typography } from '../../../utils/responsiveUI';
import { ONE_TO_ONE } from '../client';
import { avatarComponentAction } from './avatarComponentAction';
import useThumbnail from '../../../hooks/useThumbnail';
import constants from '../../../utils/constants/constants';

const HandleAvatar = ({ data }) => {

    const { user } = useContext(AuthContext);

    const { img, initials, membersCount, type } = useMemo(() => {
        const { data: {
            type
        } = {},
            state: {
                members
            } = {}
        } = data?.channel
        const { img, initials } = avatarComponentAction(user, data, type)
        const membersCount = Object.keys(members).length
        return { img, initials, membersCount, type }
    }, [data])

    const handleAvatarrender = () => {
        if (membersCount === 1 && type === ONE_TO_ONE) {
            return (
                <View style={styles.deletedUserImageContainer}>
                    <Text style={styles.deletedUserImageTextStyle}>{'?'}</Text>
                </View>
            )
        }
        else {
            return (
                <View style={styles.imageContainer}>
                    {
                        img ? <Image
                            source={{ uri: useThumbnail(img, constants.ImageSize[128])?.thumbnailUrl }}
                            style={styles.image}
                        /> :
                            <Text style={styles.imageTextStyle}>{initials}</Text>
                    }
                </View>
            )
        }
    }

    return (
        <TouchableOpacity
            disallowInterruption={true}
        >
            {handleAvatarrender()}
        </TouchableOpacity>
    )
}

export default HandleAvatar

const styles = StyleSheet.create({
    imageContainer: {
        width: Size.SIZE_45,
        height: Size.SIZE_45,
        borderRadius: Size.SIZE_50,
        backgroundColor: 'rgba(9, 128, 137, 1)',
        justifyContent: 'center',
        alignItems: 'center'
    },
    deletedUserImageContainer: {
        width: Size.SIZE_45,
        height: Size.SIZE_45,
        borderRadius: Size.SIZE_50,
        backgroundColor:  colors.lightGrey,
        justifyContent: 'center',
        alignItems: 'center'
    },
    imageTextStyle: {
        textAlign: 'center',
        fontSize: Typography.FONT_SIZE_25,
        color: 'rgba(255, 255, 255, 1)',
        fontFamily: 'Ubuntu',
        fontWeight: '500',
        textTransform: 'capitalize'
    },
    deletedUserImageTextStyle: {
        textAlign: 'center',
        fontSize: Typography.FONT_SIZE_25,
        color: colors.lightShadeGray,
        fontFamily: 'Ubuntu',
        fontWeight: '500',
        textTransform: 'capitalize'
    },
    textWrapper: {
        width: Spacing.SCALE_250,
        alignItems: 'center',
        marginTop: Spacing.SCALE_10
    },
    text: {
        fontSize: Typography.FONT_SIZE_18,
        lineHeight: Size.SIZE_20,
        fontWeight: '500',
        fontFamily: 'Ubuntu',
        textAlign: 'center'
    },
    btnWrapper: {
        width: Spacing.SCALE_150,
        height: Size.SIZE_40,
        backgroundColor: 'rgba(9, 128, 137, 1)',
        borderRadius: Size.SIZE_8,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: Spacing.SCALE_10
    },
    btnText: {
        fontSize: Typography.FONT_SIZE_16,
        lineHeight: Size.SIZE_18,
        fontWeight: '500',
        fontFamily: 'Ubuntu',
        color: 'rgba(255, 255, 255, 1)'
    },
    image: {
        width: Size.SIZE_45,
        height: Size.SIZE_45,
        borderRadius: Size.SIZE_50
    }
})