import React, { useState, useEffect, useContext } from 'react';
import { View, ScrollView, Text, ActivityIndicator } from 'react-native';

import useQuery from '../../hooks/useQuery';
import { colors } from '../../theme/theme';
import { AuthContext } from '../../context/AuthContext';
import { FETCH_BENEFITS } from '../../graphql/queries/benefits';
import BenefitCard from '../../components/layout/benefits/BenefitCard';
import useClient from '../../hooks/useClient';
import PromoCodeModal from '../../components/modals/PromoCodeModal';
import { useSubscription } from 'react-apollo';
import BenefitFilterPopup from './BenefitFilterPopup';
import StreamChatSearchInput from '../my-TG-friends/view/StreamChatSearchInput';
import { Spacing } from '../../utils/responsiveUI';
import NewScreenHeader from '../../components/layout/NewScreenHeader';

const GET_BENEFITS = `
query getBenefitCategory {
    benefit_category {
      name
    }
  }
`;

export default function BenefitScreen({ navigation }) {
    const { data, error } = useSubscription(FETCH_BENEFITS);
    const { data: benefitCats, error: benefitCatsError } = useQuery(GET_BENEFITS);

    const [benefitCategories, setBenefitCategories] = useState();
    const [benefits, setBenefits] = useState();
    const [searchInput, setSearchInput] = useState();
    const [searching, setSearching] = useState();
    const { user } = useContext(AuthContext);
    const client = useClient();
    const [category, setCategory] = useState('all');
    const [modal, setModal] = useState();
    const [openFilter, setOpenFilter] = useState(false);

    useEffect(() => {
        if (benefitCats) {
            const { benefit_category } = benefitCats;
            setBenefitCategories([{ name: 'all' }, ...benefit_category]);
        }
    }, [benefitCats]);

    useEffect(() => {
        if (data) {
            let newBenefits =
                category === 'all' ? data.benefit : data.benefit.filter((benefit) => category === benefit.category);

            newBenefits = newBenefits.sort(function (a, b) {
                if (a?.title?.toLowerCase() < b?.title?.toLowerCase()) {
                    return -1;
                }
                if (a?.title?.toLowerCase() > b?.title?.toLowerCase()) {
                    return 1;
                }
                return 0;
            });

            if (searching && searchInput) {
                setBenefits(
                    newBenefits.filter((benefit) => {
                        if (benefit.title.toLowerCase().includes(searchInput.toLowerCase())) {
                            return true;
                        }

                        if (benefit.category.toLowerCase().includes(searchInput.toLowerCase())) {
                            return true;
                        }

                        return false;
                    }),
                );
            } else {
                setBenefits(newBenefits);
            }
        }
    }, [data, searching, searchInput, category]);

    useEffect(() => {
        setSearching(searchInput?.length > 0);
    }, [searchInput]);

    return (
        <>
            <View style={{ flex: 1 }}>
                <NewScreenHeader
                    title="Benefits"
                    showNotification={false}
                    showFilter
                    openFilterModal={() => setOpenFilter(true)}
                />
                <StreamChatSearchInput
                    searchState={[searchInput, setSearchInput]}
                    searchBoxWrapperStyle={{ marginTop: Spacing.SCALE_5, marginBottom: Spacing.SCALE_6 }}
                />
                <View style={{ flexGrow: 1 }}>
                    <View
                        style={{
                            flexGrow: 1,
                            backgroundColor: colors.lightgray,
                            overflow: 'hidden',
                        }}>
                        <View style={{ flexGrow: 1 }}>
                            <ScrollView
                                style={{ flex: 1 }}
                                contentContainerStyle={{
                                    paddingHorizontal: 10,
                                    paddingBottom: 30,
                                }}>
                                {benefits ? (
                                    benefits.length === 0 ? (
                                        <Text
                                            style={{
                                                fontFamily: 'Ubuntu-Regular',
                                                textAlign: 'center',
                                                paddingVertical: 30,
                                                color: colors.darkgray,
                                            }}>
                                            There are no benefits listed. Please check back later.
                                        </Text>
                                    ) : (
                                        benefits.map((benefit) => (
                                            <BenefitCard
                                                key={benefit.id}
                                                setModal={setModal}
                                                benefit={benefit}
                                                user={user}
                                                client={client}
                                                onPress={() =>
                                                    navigation.navigate('Benefit Details', { id: benefit.id })
                                                }
                                            />
                                        ))
                                    )
                                ) : (
                                    <View style={{ paddingVertical: 30 }}>
                                        <ActivityIndicator color="#098089" />
                                    </View>
                                )}
                            </ScrollView>
                        </View>
                    </View>
                </View>
            </View>
            {modal && <PromoCodeModal code={modal.promo_code} closeModal={() => setModal()} />}
            {openFilter && (
                <BenefitFilterPopup
                    popupState={[openFilter, setOpenFilter]}
                    benefitCategories={benefitCategories}
                    setCategory={setCategory}
                    category={category}
                />
            )}
        </>
    );
}
