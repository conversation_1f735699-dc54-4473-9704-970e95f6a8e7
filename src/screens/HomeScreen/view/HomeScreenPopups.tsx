import React, { useContext, useState, useEffect } from 'react';
import { Modal, Pressable, View } from 'react-native';
import moment from 'moment';

import { GlobalContext } from '../../../context/contextApi';
import UnMuteAccountPopup from '../../unmuteAccountPopup/view/UnMuteAccountPopup';
import { AuthContext } from '../../../context/AuthContext';
import PendingGameRequestPopup from '../../../components/popup/PendingGameRequestPopup';
import ContactInformVisibilityPopup from '../../feed/ContactInformVisibilityPopup';
import { UserClub } from '../../../interface';
import { fetcher } from '../../../service/fetcher';
import { CHECK_REQUEST_POPUP } from '../../../service/EndPoint';

const HomeScreenPopups = () => {
    const { state, actions } = useContext(GlobalContext);
    const { user } = useContext(AuthContext);
    const [showPopup, setShowPopup] = useState(false);

    useEffect(() => {
        if (state.homeScreenPopupSteps === 1) {
            if (
                user?.muted &&
                (user?.last_muted_prompt === null || moment().diff(moment(user?.last_muted_prompt), 'hours') >= 0)
            ) {
                setShowPopup(true);
            } else {
                setShowPopup(false);
                actions.setHomeScreenPopupState(2);
            }
        }
    }, [user, state.comeFromNotification, state.homeScreenPopupSteps]);

    useEffect(() => {
        if (state.homeScreenPopupSteps === 2) {
            actions.setAppLoader(true);
            const checkPendingRequestStatus = () => {
                fetcher({
                    endpoint: CHECK_REQUEST_POPUP,
                    method: 'POST',
                    body: {
                        userId: user?.id,
                    },
                }).then((res) => {
                    if (res.status) {
                        if (res.data) {
                            setShowPopup(res.data);
                        } else {
                            actions.setHomeScreenPopupState(3);
                        }
                    } else {
                        actions.setHomeScreenPopupState(3);
                    }
                    actions.setAppLoader(false);
                });
            };
            checkPendingRequestStatus();
        }
    }, [user, state.homeScreenPopupSteps]);

    useEffect(() => {
        if (state.homeScreenPopupSteps === 3) {
            if (
                user?.show_visibility_popup &&
                (user?.show_visibility_popup_time === null || new Date(user?.show_visibility_popup_time) <= new Date())
            ) {
                setShowPopup(!user?.clubs?.every((data: UserClub) => data?.visibleInNetwork));
            } else {
                setShowPopup(false);
                actions.setHomeScreenPopupState(4);
            }
            actions.setAppLoader(false);
        }
    }, [user, state.homeScreenPopupSteps]);

    const handlePopup = () => {
        switch (state?.homeScreenPopupSteps) {
            case 1:
                return <UnMuteAccountPopup user={user} popupState={[showPopup, setShowPopup]} />;
            case 2:
                return state.appLoader ? null : <PendingGameRequestPopup popupState={[showPopup, setShowPopup]} />;
            case 3: {
                return (
                    showPopup &&
                    user?.show_visibility_popup &&
                    (user?.show_visibility_popup_time === null ||
                        new Date(user?.show_visibility_popup_time) <= new Date()) && (
                        <ContactInformVisibilityPopup
                            isHaveToShowPopup={showPopup}
                            setIsHaveToShowPopup={setShowPopup}
                        />
                    )
                );
            }
        }
    };
    return (
        <Modal
            animationType="slide"
            transparent={true}
            visible={showPopup}
            style={{
                paddingHorizontal: 0,
                marginHorizontal: 0,
                paddingVertical: 0,
                marginVertical: 0,
            }}>
            <View style={{ flex: 1 }}>
                <Pressable style={{ flex: 1, backgroundColor: '#00000052' }} />
                {handlePopup()}
            </View>
        </Modal>
    );
};

export default HomeScreenPopups;
