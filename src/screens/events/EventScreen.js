import React, {useState, useEffect, useContext} from 'react';
import {
    View,
    ScrollView,
    Text,
    ActivityIndicator,
    RefreshControl,
} from 'react-native';
import auth from '@react-native-firebase/auth';

import ScreenHeader from '../../components/layout/ScreenHeader';
import useQuery from '../../hooks/useQuery';
import {colors} from '../../theme/theme';
import {AuthContext} from '../../context/AuthContext';
import {FETCH_EVENTS} from '../../graphql/queries/event';
import EventCard from '../../components/layout/events/EventCard';
import { GET_EVENTS_V4} from '../../service/EndPoint';
import showToast from '../../components/toast/CustomToast';
import NewScreenHeader from '../../components/layout/NewScreenHeader';
import config from '../../config';
import {useIsFocused} from '@react-navigation/native';
import EventFilterModal from './EventFilterModal';
import TGLoader from '../../components/layout/TGLoader';

export default function RequestScreen({navigation}) {
    const {data, refresh} = useQuery(FETCH_EVENTS);
    const {user} = useContext(AuthContext);
    const [refreshing, setRefresh] = useState(false);
    const [events, setEvents] = useState();
    const [openFilter, setOpenFilter] = useState(false);
    const [loading, setLoading] = useState(false);
    const [filter, setFilter] = useState('All Events');
    const isFocused = useIsFocused();

    useEffect(() => {
        // setFilter('All Events')
        getEvents();
    }, [isFocused]);

    async function getEvents() {
        setLoading(true);
        const token = await auth()?.currentUser?.getIdToken();
        let payload = {
            userId: user?.id,
        };
        if (filter === 'Pending Events') {
            payload.myEvents = true;
            payload.isApproved = 0;
        }
        fetch(GET_EVENTS_V4, {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
        })
            .then((data) => data.json())
            .then((res) => {
                setLoading(false);
                if (res?.status) {
                    setEvents(res?.events);
                } else {
                    showToast({});
                }
            })
            .catch((err) => {
                console.log('Error----->', err);
            });
    }
    useEffect(() => {
        if (data) {
            const eventData = data.event;
            const sortedEvent = eventData.sort(function (a, b) {
                // Turn your strings into dates, and then subtract them
                // to get a value that is either negative, positive, or zero.
                return new Date(a?.start_date) - new Date(b?.start_date);
            });
            setRefresh(false);
        }
    }, [data]);

    const _onRefresh = () => {
        getEvents();
        refresh();
        setRefresh(false);
    };

    return (
        <>
            <View style={{flex: 1}}>
                <NewScreenHeader
                    title="Events"
                    showNotification={false}
                    showFilter
                    onClick={() => {
                        navigation.navigate(config.routes.CREATE_NEW_EVENTS);
                    }}
                    openFilterModal={() => setOpenFilter((prev) => !prev)}
                />
                <View style={{flexGrow: 1, backgroundColor: 'white'}}>
                    <View
                        style={{
                            flexGrow: 1,
                            backgroundColor: colors.lightgray,
                            overflow: 'hidden',
                            borderTopLeftRadius: 25,
                            borderTopRightRadius: 25,
                        }}>
                        <View style={{flexGrow: 1}}>
                            <ScrollView
                                style={{flex: 1}}
                                contentContainerStyle={{
                                    paddingHorizontal: 15,
                                    paddingBottom: 30,
                                }}
                                refreshControl={
                                    <RefreshControl
                                        refreshing={refreshing}
                                        onRefresh={_onRefresh}
                                    />
                                }>
                                {events ? (
                                    events.length === 0 ? (
                                        <Text
                                            style={{
                                                fontFamily: 'Ubuntu-Regular',
                                                textAlign: 'center',
                                                paddingVertical: 30,
                                                color: colors.darkgray,
                                            }}>
                                            There are no events listed. Please
                                            check back later.
                                        </Text>
                                    ) : (
                                        events.map((event) => {
                                            return (
                                                <EventCard
                                                    event={event}
                                                    user={user}
                                                    loadingState={[loading, setLoading]}
                                                    getEvents={getEvents}
                                                    onPress={() =>
                                                        navigation.navigate(
                                                            'Event Details',
                                                            {id: event?.event_id},
                                                        )
                                                    }
                                                />
                                            );
                                        })
                                    )
                                ) : (
                                    <View style={{paddingVertical: 30}}>
                                        <ActivityIndicator color="#098089" />
                                    </View>
                                )}
                            </ScrollView>
                        </View>
                    </View>
                </View>
            </View>
            {
                loading && <TGLoader loading={loading} />
            }
            {openFilter && (
                <EventFilterModal
                    popupState={[openFilter, setOpenFilter]}
                    filterState={[filter, setFilter]}
                    getEvents={getEvents}
                    loadingState={[loading, setLoading]}
                />
            )}
        </>
    );
}
