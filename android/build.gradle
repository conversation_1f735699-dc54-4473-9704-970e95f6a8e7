buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 34
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.24"
        playServicesVersion = "21.0.1"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.0.2")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath 'com.google.gms:google-services:4.4.2'
        classpath "com.newrelic.agent.android:agent-gradle-plugin:7.4.1"
    }
}

apply plugin: "com.facebook.react.rootproject"

// Add the repositories in the allprojects section
allprojects {
    repositories {
        google()
        mavenCentral()

        // Add Jitpack for additional dependencies
        maven { url "https://www.jitpack.io" }

        // Add Mapbox Maven repository
        maven {
            url 'https://api.mapbox.com/downloads/v2/releases/maven'
            authentication {
                basic(BasicAuthentication)
            }
            credentials {
                // Do not change the username below.
                // This should always be `mapbox` (not your username).
                username = 'mapbox'
                // Use the secret token you stored in gradle.properties as the password
                password = '*****************************************************************************************'
            }
        }
    }
}
