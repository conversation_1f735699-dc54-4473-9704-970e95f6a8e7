# New Relic configuration file
# This file should be placed in the assets folder

# Enable crash reporting
com.newrelic.android.crash_reporting_enabled=true

# Enable network instrumentation
com.newrelic.android.network_instrumentation_enabled=true

# Enable HTTP response body capture
com.newrelic.android.http_response_body_capture_enabled=true

# Enable custom events
com.newrelic.android.custom_events_enabled=true

# Enable interaction traces
com.newrelic.android.interaction_traces_enabled=true

# Enable network error reporting
com.newrelic.android.network_error_reporting_enabled=true

# Enable application startup time tracking
com.newrelic.android.application_startup_time_enabled=true

# Enable background activity tracking
com.newrelic.android.background_activity_tracking_enabled=true

# Enable automatic interaction traces
com.newrelic.android.automatic_interaction_traces_enabled=true

# Enable automatic network instrumentation
com.newrelic.android.automatic_network_instrumentation_enabled=true 