<manifest xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-feature android:name="android.hardware.camera" android:required="false"/>
    <uses-feature android:name="android.hardware.camera.front" android:required="false"/>

    <!-- New Relic permissions -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/AppTheme"
      android:largeHeap="true"
      android:requestLegacyExternalStorage="true"
      android:preserveLegacyExternalStorage="true"
      android:usesCleartextTraffic="true"
      android:supportsRtl="true">
      <meta-data
                android:name="com.google.android.geo.API_KEY"
                android:value="AIzaSyDER9L7OqYuIR2eSC8qjVe8xl2jGvIYvwg"/>
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:screenOrientation="portrait"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleInstance"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
        <intent-filter>
                <action android:name="android.intent.action.SEND"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <data android:mimeType="image/*"/>
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.SEND_MULTIPLE"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <data android:mimeType="image/*"/>
            </intent-filter>

            <!-- Intent filter for deeplink in android start-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Specify the scheme, host, and path pattern -->
                <data android:scheme="https" android:host="thousandgreens-web-dev.herokuapp.com" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Specify the scheme, host, and path pattern -->
                <data android:scheme="https" android:host="thousandgreens-qa-f6fb54c63391.herokuapp.com" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Specify the scheme, host, and path pattern -->
                <data android:scheme="https" android:host="thousand-greens-staging.herokuapp.com" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Specify the scheme, host, and path pattern -->
                <data android:scheme="https" android:host="www.thousandgreens.com" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Specify the scheme, host, and path pattern -->
                <data android:scheme="https" android:host="thousandgreens.com" />
            </intent-filter>
            <!-- Intent filter for custom app scheme -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="app" android:host="thousandgreens"/>
            </intent-filter>
            <!-- Intent filter for deeplink in android end-->
            

      </activity>
      <!-- <activity
            android:name="com.facebook.react.devsupport.DevSettingsActivity"
            android:exported="true"
            tools:replace="android:exported"/>
        <service
            android:name="com.clevertap.android.sdk.pushnotification.CTNotificationIntentService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.clevertap.PUSH_EVENT" />
            </intent-filter>
        </service> -->
      <!-- Dev ID -->
        <meta-data android:name="CLEVERTAP_ACCOUNT_ID" android:value="TEST-77Z-WK9-RR7Z" />
        <meta-data android:name="CLEVERTAP_TOKEN" android:value="TEST-c30-1aa" />

        <!-- Prod ID -->
        <!-- <meta-data android:name="CLEVERTAP_ACCOUNT_ID" android:value="67Z-WK9-RR7Z" />
        <meta-data android:name="CLEVERTAP_TOKEN" android:value="c30-1a6" /> -->

        <meta-data android:name="CLEVERTAP_USE_GOOGLE_AD_ID" android:value="1"/>
    </application>
</manifest>
