{"name": "thousandgreens", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios --simulator='iPhone 13'", "lint": "eslint .", "start": "react-native start", "test": "jest", "pod": "cd ios && pod install && cd ..", "android:clean": "cd android && ./gradlew clean && cd ..", "android:dev": "cp -R 'dev/google-services.json' 'android/app' && ENVFILE=.env.dev react-native run-android", "android:qa": "cp -R 'qa/google-services.json' 'android/app' && ENVFILE=.env.qa react-native run-android", "android:staging": "cp -R 'staging/google-services.json' 'android/app' && ENVFILE=.env.staging react-native run-android", "android:prod": "cp -R 'prod/google-services.json' 'android/app' && ENVFILE=.env.prod react-native run-android", "ios:dev": "react-native run-ios --scheme 'thousandgreens-dev'", "ios:qa": "react-native run-ios --scheme 'thousandgreens-qa'", "ios:staging": "react-native run-ios --scheme 'thousandgreens-staging'", "ios:prod": "react-native run-ios --scheme 'thousandgreens'", "ios-8": "react-native run-ios --simulator='iPhone 8'", "build:ios": "react-native bundle --entry-file='index.js' --bundle-output='./ios/main.jsbundle' --dev=false --platform='ios'", "stagningEnv": "APP_ENV=staging", "apk:dev": "cp -R 'dev/google-services.json' 'android/app' && cd android && ./gradlew clean && ENVFILE=.env.dev ./gradlew assembleRelease && cd ..", "apk:qa": "cp -R 'qa/google-services.json' 'android/app' && cd android && ./gradlew clean && ENVFILE=.env.qa ./gradlew assembleRelease && cd ..", "apk:staging": "cp -R 'staging/google-services.json' 'android/app' && cd android && ./gradlew clean && ENVFILE=.env.staging ./gradlew assembleRelease && cd ..", "apk:prod": "cp -R 'prod/google-services.json' 'android/app' && cd android && ./gradlew clean && ENVFILE=.env.prod ./gradlew assembleRelease && cd ..", "app:dev": "cp -R 'dev/google-services.json' 'android/app' && react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/ && cd android && ./gradlew clean && ENVFILE=.env.dev ./gradlew bundleRelease && cd ..", "app:staging": "cp -R 'staging/google-services.json' 'android/app' && react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/ && cd android && ./gradlew clean && ENVFILE=.env.staging ./gradlew bundleRelease && cd ..", "app:prod": "cp -R 'prod/google-services.json' 'android/app' && react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/ && cd android && ./gradlew clean && ENVFILE=.env.prod ./gradlew bundleRelease && cd ..", "postinstall": "patch-package && yarn pod"}, "dependencies": {"@apollo/client": "^3.12.5", "@fawazahmed/react-native-read-more": "^3.0.4", "@gorhom/bottom-sheet": "5.1.1", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-camera-roll/camera-roll": "7.4.0", "@react-native-clipboard/clipboard": "^1.16.1", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-documents/picker": "^10.0.1", "@react-native-documents/viewer": "^1.0.0", "@react-native-firebase/app": "21.6.2", "@react-native-firebase/auth": "21.6.2", "@react-native-firebase/dynamic-links": "21.6.2", "@react-native-firebase/messaging": "21.6.2", "@react-native-firebase/storage": "21.6.2", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@react-navigation/stack": "^7.1.1", "@rnmapbox/maps": "10.1.39", "@stream-io/flat-list-mvcp": "^0.10.3", "apollo-cache-inmemory": "^1.6.6", "apollo-client": "^2.6.10", "apollo-link": "^1.2.14", "apollo-link-http": "^1.5.17", "apollo-link-ws": "^1.0.20", "apollo-utilities": "^1.3.4", "clevertap-react-native": "3.1.1", "country-state-city": "^3.2.1", "graphql": "^16.10.0", "graphql-request": "^7.1.2", "graphql-ws": "^5.16.2", "install": "^0.13.0", "jetifier": "^2.0.0", "libphonenumber-js": "^1.12.9", "localstorage-polyfill": "^1.0.1", "moment": "^2.30.1", "newrelic-react-native-agent": "^1.4.8", "papaparse": "^5.5.1", "patch-package": "^8.0.0", "react": "18.3.1", "react-apollo": "^3.1.5", "react-native": "0.76.6", "react-native-audio-recorder": "^0.0.8", "react-native-background-timer": "^2.4.1", "react-native-calendars": "^1.1307.0", "react-native-compressor": "^1.10.3", "react-native-config": "^1.5.3", "react-native-device-info": "^14.0.2", "react-native-draggable-flatlist": "^4.0.1", "react-native-elements": "^3.4.3", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.22.0", "react-native-google-places-autocomplete": "2.5.1", "react-native-haptic-feedback": "^2.3.3", "react-native-html-to-pdf": "^0.12.0", "react-native-htmlview": "^0.17.0", "react-native-image-crop-picker": "^0.41.6", "react-native-image-picker": "^7.2.3", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-otp-textinput": "^1.1.6", "react-native-paper": "^5.13.1", "react-native-pell-rich-editor": "^1.9.0", "react-native-permissions": "^5.2.3", "react-native-phone-number-input": "^2.1.0", "react-native-picker-select": "9.3.1", "react-native-push-notification": "^8.1.1", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "3.16.7", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^5.1.0", "react-native-screens": "4.4.0", "react-native-share": "^12.0.3", "react-native-shimmer-placeholder": "^2.0.9", "react-native-simple-toast": "^3.3.2", "react-native-svg": "^15.11.1", "react-native-svg-transformer": "^1.5.0", "react-native-swiper": "^1.6.0", "react-native-toast-message": "^2.2.1", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.9.1", "react-native-video-controls": "^2.8.1", "react-native-walkthrough-tooltip": "^1.6.0", "react-native-webview": "^13.13.1", "rn-fetch-blob": "^0.12.0", "rollbar": "^2.26.4", "rollbar-react-native": "^1.0.0-beta.5", "stream-chat-react-native": "6.6.7", "subscriptions-transport-ws": "^0.11.0", "swr": "^2.3.0", "yup": "0.29.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.6", "@react-native/eslint-config": "0.76.6", "@react-native/metro-config": "0.76.6", "@react-native/typescript-config": "0.76.6", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}