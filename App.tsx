import 'localstorage-polyfill';
import React, { useEffect, useState, useCallback, useRef, useMemo, useLayoutEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { ApolloProvider } from 'react-apollo';
import NetInfo from '@react-native-community/netinfo';
import { ApolloProvider as ApolloHookProvider } from '@apollo/client';
import Mapbox from '@rnmapbox/maps';
import { Platform, NativeEventEmitter, NativeModules, LogBox } from 'react-native';
import Toast, { ErrorToast, SuccessToast, ToastProps } from 'react-native-toast-message';
import RNFS from 'react-native-fs';
import NewRelic from 'newrelic-react-native-agent';
const CleverTap = require('clevertap-react-native');

import RenderNavigation from './src/navigation';
import useAuth from './src/hooks/useAuth';
import useApolloClient from './src/hooks/useApolloClient';
import { mapBoxAccessToken } from './src/screens/my-TG-Stream-Chat/client';
import { Size, Typography } from './src/utils/responsiveUI';
import { colors } from './src/theme/theme';
import ErrorBoundary from './src/components/ErrorBoundry/ErrorBoundry';
import constants from './src/utils/constants/constants';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { User } from './src/interface';
import CustomToastUI from './src/components/toast/CustomToastUI';

// Ignore specific warnings that might cause issues
LogBox.ignoreLogs([
    'Require cycle:',
    'ViewPropTypes will be removed',
    'AsyncStorage has been extracted',
    'Sending `onAnimatedValueUpdate` with no listeners registered',
]);

// Global error handler
const setupGlobalErrorHandler = () => {
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;

    // Override console.error to send to New Relic
    console.error = (...args) => {
        // Log to original console
        originalConsoleError.apply(console, args);

        // Send to New Relic
        try {
            const errorMessage = args.map(arg =>
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' ');

            NewRelic.recordError('Console Error', {
                message: errorMessage,
                level: 'ERROR',
                source: 'console.error'
            });
        } catch (e) {
            // Fallback if New Relic fails
            originalConsoleError('Failed to send error to New Relic:', e);
        }

        // Additional error handling if needed
        if (args[0] && typeof args[0] === 'string' && args[0].includes('Unhandled')) {
            console.warn('Unhandled error detected, consider adding error boundary');
        }
    };

    // Override console.warn to send to New Relic
    console.warn = (...args) => {
        // Log to original console
        originalConsoleWarn.apply(console, args);

        // Send to New Relic
        try {
            const warningMessage = args.map(arg =>
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' ');

            NewRelic.recordError('Console Warning', {
                message: warningMessage,
                level: 'WARNING',
                source: 'console.warn'
            });
        } catch (e) {
            // Fallback if New Relic fails
            originalConsoleError('Failed to send warning to New Relic:', e);
        }
    };

    // Handle unhandled promise rejections
    if (__DEV__) {
        const originalError = console.error;
        console.error = (...args) => {
            if (args[0] && args[0].includes && args[0].includes('Unhandled promise rejection')) {
                console.warn('Unhandled promise rejection detected');
            }
            originalError.apply(console, args);
        };
    }

    // Global unhandled promise rejection handler
    const handleUnhandledRejection = (event) => {
        try {
            NewRelic.recordError('Unhandled Promise Rejection', {
                message: event.reason?.message || String(event.reason),
                stack: event.reason?.stack,
                level: 'ERROR',
                source: 'unhandledRejection'
            });
        } catch (e) {
            originalConsoleError('Failed to send unhandled rejection to New Relic:', e);
        }
    };

    // Global error handler for uncaught exceptions
    const handleUncaughtError = (error) => {
        try {
            NewRelic.recordError('Uncaught Exception', {
                message: error.message,
                stack: error.stack,
                level: 'ERROR',
                source: 'uncaughtException'
            });
        } catch (e) {
            originalConsoleError('Failed to send uncaught error to New Relic:', e);
        }
    };

    // Set up global handlers (React Native specific)
    if (typeof global !== 'undefined') {
        global.addEventListener?.('unhandledrejection', handleUnhandledRejection);
        global.addEventListener?.('error', handleUncaughtError);
    }
};

const App = React.memo(() => {
    const {
        user,
        token,
        initializing,
        refreshUser,
        refreshToken,
        needsAnnualConfirmation,
        deferAnnualConfirmation,
        setDeferAnnualConfirmation,
        streamClient,
        duesState,
        maintenance,
        clubValidationRequired,
    } = useAuth() as unknown as { user: User | null; [key: string]: any };

    const { SharedDataModule } = NativeModules;

    const [isInternet, setInternet] = useState<boolean>(true);
    const [shareSheetImageUrl, setShareSheetImageUrl] = useState<string[]>([]);

    const navigationRef = useRef(null);

    const client = useApolloClient(token);

    // Setup global error handler
    useEffect(() => {
        setupGlobalErrorHandler();
    }, []);

    useLayoutEffect(() => {
        setShareSheetImageUrl([]);
    }, []);

    useEffect(() => {
        if (Platform.OS === 'android') {
            const sharedDataEmitter = new NativeEventEmitter(SharedDataModule);
            const subscription = sharedDataEmitter.addListener('SharedData', (event) => {
                convertContentUrisToFilePaths(event?.uris).then((localPath) => {
                    handleImage(localPath);
                });
            });

            return () => {
                subscription.remove(); // Cleanup listener on component unmount
            };
        }
    }, []);

    useEffect(() => {
        const fetchSharedData = async () => {
            try {
                const sharedData = await SharedDataModule.getSharedData();
                if (sharedData) {
                    convertContentUrisToFilePaths(sharedData?.uris).then((localPath) => {
                        handleImage(localPath);
                    });
                }
            } catch (error) {
                console.error('Error fetching shared data:', error);
            }
        };

        if (Platform.OS === 'android') fetchSharedData();
    }, []);

    const convertContentUrisToFilePaths = async (contentUris: string[]) => {
        try {
            if (!contentUris || contentUris.length === 0) {
                return [];
            }

            const convertedPaths = await Promise.all(
                contentUris.map(async (contentUri, index) => {
                    // Generate a unique filename using timestamp
                    const uniqueId = new Date().getTime() + Math.floor(Math.random() * 1000);
                    const destPath = `${RNFS.TemporaryDirectoryPath}/shared_image_${uniqueId}_${index}.jpg`;

                    await RNFS.copyFile(contentUri, destPath);
                    return destPath;
                }),
            );

            return convertedPaths;
        } catch (error) {
            console.error('Error converting content URIs:', error);
            return [];
        }
    };

    // Memoized Toast configuration
    const toastConfig = useMemo(
        () => ({
            // @ts-ignore
            error: (props: ToastProps) => <CustomToastUI props={props} />,
            // @ts-ignore
            success: (props: ToastProps) => <CustomToastUI props={props} />,
        }),
        [],
    );

    // Handle shared image
    const handleImage = useCallback((data: string[] = []) => {
        try {
            setShareSheetImageUrl(data);
        } catch (e) {
            console.error('Error in handleImage:', e);
        }
    }, []);

    // NetInfo listener and Mapbox configuration
    useEffect(() => {
        const unsubscribe = NetInfo.addEventListener((state) => {
            if (state.isConnected !== isInternet) {
                setInternet(state.isConnected ?? true);
            }
        });

        Mapbox.setAccessToken(mapBoxAccessToken);
        Mapbox.setTelemetryEnabled(false);

        return () => unsubscribe();
    }, [isInternet]);

    // CleverTap setup
    useEffect(() => {
        if (user?.id) {
            CleverTap.getCleverTapID((err: Error | null, res: string) => {
                if (err) console.error('CleverTapID Error:', err);
                else console.log('CleverTapID:', res);
            });

            CleverTap.setDebugLevel(3);
            CleverTap.onUserLogin({
                [constants.CLEVERTAP.PROFILE.IDENTITY]: user?.id,
                [constants.CLEVERTAP.PROFILE.NAME]: `${user?.first_name} ${user?.last_name}`,
                [constants.CLEVERTAP.PROFILE.EMAIL]: user?.email,
            });
        }
    }, [user?.id]);

    return (
        <GestureHandlerRootView style={{ flex: 1 }}>
            <ErrorBoundary>
                <NavigationContainer ref={navigationRef}>
                    {client && (
                        <ApolloProvider client={client}>
                            <ApolloHookProvider client={client}>
                                <RenderNavigation
                                    navigationRef={navigationRef}
                                    duesState1={duesState}
                                    shareSheetImageUrl={shareSheetImageUrl}
                                    user={user}
                                    token={token}
                                    initializing={initializing}
                                    refreshUser={refreshUser}
                                    refreshToken={refreshToken}
                                    needsAnnualConfirmation={needsAnnualConfirmation}
                                    deferAnnualConfirmation={deferAnnualConfirmation}
                                    setDeferAnnualConfirmation={setDeferAnnualConfirmation}
                                    streamClient={streamClient}
                                    maintenance={maintenance}
                                    clubValidationRequired={clubValidationRequired}
                                />
                                <Toast config={toastConfig} />
                            </ApolloHookProvider>
                        </ApolloProvider>
                    )}
                </NavigationContainer>
            </ErrorBoundary>
        </GestureHandlerRootView>
    );
});

export default App;
